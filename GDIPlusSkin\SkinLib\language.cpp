#include "pch.h"
#include "language.h"
#include <strsafe.h>

Clanguage::Clanguage ( ): m_strDefaultLangPath(_T(""))
, m_strCurrentLangPath ( _T ( "" ) )
, m_dwLastError(ERROR_SUCCESS)
, m_fileName(_T("chinese.lang" ) )
    {
    TCHAR *ptrEnd(NULL);
    TCHAR  szSoftPath [ MAX_PATH ] = { 0};

    ::GetModuleFileName ( NULL , szSoftPath , MAX_PATH );
    if ( ( ptrEnd = _tcsrchr ( szSoftPath , '\\' ) ) != NULL ) {
        *ptrEnd = '\0';
        }
   
    TCHAR szLangFolder [ MAX_PATH ] = { 0 };
    StringCchPrintf ( szLangFolder , MAX_PATH , _T ( "%s\\%s" ) , szSoftPath , LANGUAGE_DIR );

#if USER_LANLANGUAGE
        ::CreateDirectory( szLangFolder , NULL );
#endif
      
    
    m_strDefaultLangPath.Format ( _T ( "%s\\%s" ) , szLangFolder, ( LPCTSTR ) m_fileName );
   
    m_strCurrentLangPath = m_strDefaultLangPath;
    }
//================================================================================
Clanguage::~Clanguage ( )
    {

    }
//================================================================================
CString Clanguage::GetValue ( const CString &section , const CString &key )
    {
    TCHAR   szValue [ 512 ] = { 0 };
    CString cstr;

    if (GetIsChines  () ) {
        GetPrivateProfileString ( section , key , L"" , szValue , 512 , m_strCurrentLangPath );
        cstr = szValue;
        if ( cstr.IsEmpty ( ) ) {
            GetPrivateProfileString ( section , key , L"" , szValue , 512 , m_strDefaultLangPath );
            cstr = szValue;
            }
        }
    else {
        GetPrivateProfileString ( section , key , L"" , szValue , 512 , m_strDefaultLangPath );
        cstr = szValue;
        }

    m_dwLastError = ::GetLastError ( );

    if ( 0 != cstr.GetLength ( ) ) {
        cstr.Replace ( _T ( "\\n" ) , _T ( "\n" ) );
        }
    return cstr;
    }
//================================================================================
CString Clanguage::GetString ( const CString &key )
    {
    return GetValue ( _T ( "STRINGTABLE" ) , key );
    }
//================================================================================
CString Clanguage::GetSoftInfo ( const CString &key )
    {
    return GetValue ( _T ( "SOFT_INFO" ) , key  );
    }
//================================================================================
CString Clanguage::GetMenuText ( const CString &key  )
    {
    return GetValue ( _T ( "MENU" ) , key );
    }
//================================================================================
CString Clanguage::GetPopupMenuText ( const CString &key )
    {
    return GetValue ( _T ( "POPUP_MENU" ) , key );
    }
//================================================================================
CString Clanguage::GetTrayMenuText ( const CString &key )
    {
    return GetValue ( _T ( "TRAY_MENU" ) , key );
    }