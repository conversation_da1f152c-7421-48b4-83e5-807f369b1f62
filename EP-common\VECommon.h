﻿#pragma once

#include "CommonDefinition.h"
#include <string>
#include <vector>
#include <cstdint>
#include <cstdio>

// VE库的公共基础类
class CVECommon
{
public:
    CVECommon();
    virtual ~CVECommon();

    // 初始化和清理
    static int Initialize();
    static void Cleanup();

    // 错误处理
    static const wchar_t* GetLastErrorMessage();
    static const wchar_t* GetErrorDescription(int error_code);
    static std::string GetFileExtension( const std::string& filepath );
    static std::wstring GetFileExtensionW( const std::wstring& filepath );
    static bool DeleteFileIfExists( const std::string& filepath );
    static std::wstring GetAVErrorString( int error_code );
    static bool IsLargeFile( int64_t file_size );
    static bool IsSupportedFormat( const std::string& extension );
    static void CleanupTempFile( const std::string& temp_path );
    static void SetFinalOuptPath( const std::wstring& finalOuptPath );
    static const wchar_t* GetFinalOuptPath();
    static bool IsEncryptedVideoFile( const std::wstring& filepath );
    static std::vector<std::wstring> FindLicenseFilesForEncryptedVideo( const std::wstring& encryptedVideoPath );
    // 计算文件 MD5，返回32位小写十六进制字串，失败返回空串
    // callback 可为空；current/total 单位字节
    static std::wstring  FileMD5(const std::wstring& filepath,
                                 PROGRESS_CALLBACK callback = nullptr,
                                 void* userdata = nullptr);
    
    static std::wstring  FileMD5( FILE* fp  ,
        uint64_t fileSize ,
        PROGRESS_CALLBACK callback = nullptr ,
        void* userdata = nullptr );

    // === 新增：快速文件校验方案 ===
    
    // 1. 快速文件指纹验证（推荐）- 基于文件头、尾、大小和时间戳
    static std::wstring  FastFileFingerprint(const std::wstring& filepath);
    
    // 2. 部分哈希校验 - 只计算文件开头、中间、结尾的哈希
    static std::wstring  PartialFileMD5(const std::wstring& filepath, 
                                        size_t head_size = 64*1024,     // 文件头64KB
                                        size_t tail_size = 64*1024,     // 文件尾64KB 
                                        size_t middle_size = 32*1024,   // 文件中间32KB
                                        PROGRESS_CALLBACK callback = nullptr,
                                        void* userdata = nullptr);
    
    // 3. CRC32快速校验
    static uint32_t      FastFileCRC32(const std::wstring& filepath,
                                       PROGRESS_CALLBACK callback = nullptr,
                                       void* userdata = nullptr);
    
    // 4. 混合快速校验 - 文件大小 + 部分CRC32
    static std::wstring  HybridFastVerify(const std::wstring& filepath);
    
    // 5. 智能校验策略选择 - 根据文件大小自动选择最优方案
    static std::wstring  SmartFileVerify(const std::wstring& filepath,
                                         PROGRESS_CALLBACK callback = nullptr,
                                         void* userdata = nullptr);

    // === 新增：基于已打开文件指针的优化版本 ===
    
    // 基于文件指针的快速指纹校验（最优性能）
    static std::wstring  FastFileFingerprint(FILE* fp, uint64_t fileSize );
    
    // 基于文件指针的部分MD5校验
    static std::wstring  PartialFileMD5(FILE* fp, 
        uint64_t fileSize,
        size_t head_size = 64*1024,
                                        size_t tail_size = 64*1024, 
                                        size_t middle_size = 32*1024,
                                        PROGRESS_CALLBACK callback = nullptr,
                                        void* userdata = nullptr);
    
    // 基于文件指针的CRC32校验
    static uint32_t      FastFileCRC32(FILE* fp, 
        uint64_t fileSize , 
        PROGRESS_CALLBACK callback = nullptr,
                                       void* userdata = nullptr);
    
    // 基于文件指针的混合快速校验
    static std::wstring  HybridFastVerify(FILE* fp,uint64_t fileSize);

    static const wchar_t* GetFileMD5();
public:
    static std::wstring m_strLastError;  // 改为public以便导出函数访问
    static std::wstring m_finalOutputPath;
    static std::wstring m_fileMd5;
private:
    static bool m_bInitialized;
}; 