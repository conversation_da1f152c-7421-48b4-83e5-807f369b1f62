#include "stdafx.h"
#include "check.h"
#include "common.h"
#include "md5.h"
#include <strsafe.h>

 void VECheck(void)
	{
	/*if (!FileCheck(_T("ve.exe"), _T("66423df19cf70372b665a63e091f1857")))
		{
		::ExitProcess(0);
		}*/
	}

 void VEPlayCheck(void)
	{
	/*if (!FileCheck(_T("VideoPlayer.exe"), _T("4b55a1b903d10ebcb1007f1b4a7fc55b")))
		{
		::ExitProcess(0);
		}*/
	}

BOOL FileCheck(LPCTSTR szFile, LPCTSTR szMD5)
	{
	TCHAR szPath[MAX_PATH]     = {0};
	TCHAR szFileName[MAX_PATH] = {0};

	if (0 == GetAppPath(szPath, MAX_PATH))
		{
		wcscpy_s(szFileName, szFile);
		}
	else
		{
		StringCchPrintf(szFileName, MAX_PATH, _T("%s\\%s"), szPath, szFile);
		}

	return (0 ==wcscmp(szMD5, md5FileValue(szFileName)));
	}

 BOOL VEPlayVerCheck(const int &iVer)
	{
	DWORD dwError(0);
	return (iVer < GetVEPlayVer(dwError)) ? FALSE:TRUE;
	}

 int GetVEPlayVer(DWORD &dwError)
	{
	int iResult(-1);
	CString strCURL(_T("http://www.jiamisoft.com/vepc.html"));
	CString strResult(_T(""));

	dwError = OpenUrlAndResult(strCURL, strResult);

	if (0 == dwError)
		{
		CString strVer(_T(""));
		int iF = strResult.Find(_T("["));

		if (-1 != iF)
			{
			int iB = strResult.Find(_T("]"));

			if (iB > iF)
				{
				strVer = strResult.Mid(iF+1, iB-1);
				iResult = StrToInt(strVer);
				}
			}
		}

	return iResult;
	}