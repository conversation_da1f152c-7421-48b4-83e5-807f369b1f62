﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="skin">
      <UniqueIdentifier>{256812d7-3f48-4c49-ac2f-c29860cfa279}</UniqueIdentifier>
    </Filter>
    <Filter Include="skin\zip">
      <UniqueIdentifier>{6ca46ff1-c2e4-4421-9e86-439d0626ea4c}</UniqueIdentifier>
    </Filter>
    <Filter Include="skin\common">
      <UniqueIdentifier>{4896ba30-6e1a-4c3f-be9f-026b2361cfe9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="framework.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="GDIPlusSkinH.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPAdvToolList.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPAdvToolsTab.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPButton.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPCheckBox.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPChileDlgSkinBase.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPDlgSkinBase.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPDrivePickerListCtrl.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPEdit.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPHeader.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPHooks.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPIconButton.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPImageInfo.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPListBox.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPListCtrl.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPListHeader.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPMsgBox.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPProgress.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPQScrollBar.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPQScrollBarApi.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPQScrollBarDraw.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPScrollBar.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPSkinInfo.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPStatic.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPTaskList.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPXControl.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\language.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\XZipUnZip\XUnzip.h">
      <Filter>skin\zip</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\command\Functions.h">
      <Filter>skin\common</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\command\MemoryDC.h">
      <Filter>skin\common</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\command\OSVersion.h">
      <Filter>skin\common</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\GPMsgBoxEx.h">
      <Filter>skin</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\MemoryDCPool.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\DirtyRegionManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\FastImageRenderer.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\LazyImageLoader.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SkinLib\PreloadStrategy.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="GDIPlusSkin.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="GDIPlusSkinH.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPAdvToolList.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPAdvToolsTab.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPButton.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPCheckBox.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPChileDlgSkinBase.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPDlgSkinBase.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPDrivePickerListCtrl.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPEdit.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPHeader.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPHooks.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPIconButton.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPImageInfo.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPkinInfo.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPListBox.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPListCtrl.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPListHeader.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPMsgBox.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPProgress.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPQScrollBar.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPQScrollBarApi.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPQScrollBarDraw.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPScrollBar.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPStatic.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPTaskList.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPXControl.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\language.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\XZipUnZip\XUnzip.cpp">
      <Filter>skin\zip</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\command\Functions.cpp">
      <Filter>skin\common</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\command\OSVersion.cpp">
      <Filter>skin\common</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\GPMsgBoxEx.cpp">
      <Filter>skin</Filter>
    </ClCompile>
    <ClCompile Include="SkinLib\LazyImageLoader.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>