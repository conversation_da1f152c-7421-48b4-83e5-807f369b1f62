﻿#include "pch.h"
#include "VECommon.h"
#include <cctype>     // std::tolower
#include <cwctype>    // std::towlower
#include <sys/stat.h> // _stat
#include <cstdio>

extern "C" {
#include <libavformat/avformat.h>
}

// 静态成员变量初始化
bool CVECommon::m_bInitialized = false;
std::wstring CVECommon::m_strLastError;
std::wstring CVECommon::m_finalOutputPath;
std::wstring CVECommon::m_fileMd5;

// 错误信息映射表 - 完善版本
static const wchar_t* g_ErrorMessages[] = {
    L"操作成功",                    // VE_SUCCESS = 0
    L"参数错误",                    // VE_ERROR_PARAMETER = 1
    L"打开输入文件失败",              // VE_ERROR_OPEN_INPUT_FILE = 2
    L"获取流信息失败",               // VE_ERROR_NO_STREAM_INFO = 3
    L"没有视频流",                  // VE_ERROR_NO_VIDEO_STREAM = 4
    L"没有音频流",                  // VE_ERROR_NO_AUDIO_STREAM = 5
    L"打开解码器失败",               // VE_ERROR_OPEN_DECODER = 6
    L"打开编码器失败",               // VE_ERROR_OPEN_ENCODER = 7
    L"内存分配失败",                // VE_ERROR_MEMORY_ALLOCATION = 8
    L"创建输出文件失败",             // VE_ERROR_CREATE_OUTPUT_FILE = 9
    L"写入文件头失败",               // VE_ERROR_WRITE_HEADER = 10
    L"加密失败",                    // VE_ERROR_ENCRYPTION_FAILED = 11
    L"解密失败",                    // VE_ERROR_DECRYPTION_FAILED = 12
    L"格式不支持",                  // VE_ERROR_FORMAT_NOT_SUPPORTED = 13
    L"文件已存在",                  // VE_ERROR_FILE_EXISTS = 14
    L"密码无效",                    // VE_ERROR_INVALID_PASSWORD = 15
    L"输入文件无效或已损坏"           // VE_ERROR_INVALID_INPUT = 16
};

CVECommon::CVECommon()
{
}

CVECommon::~CVECommon()
{
}

int CVECommon::Initialize()
{
    if (m_bInitialized)
        return VE_SUCCESS;

    try
    {
        // FFmpeg 4.0+ 不再需要 av_register_all()
        // av_register_all(); // 移除过时的API调用
        
        // 初始化网络功能
        avformat_network_init();
        
        m_bInitialized = true;
        return VE_SUCCESS;
    }
    catch (...)
    {
        m_strLastError = L"初始化失败";
        return VE_ERROR_PARAMETER;
    }
}

void CVECommon::Cleanup()
{
    if (m_bInitialized)
    {
        // 释放网络资源
        avformat_network_deinit();
    }
    
    m_bInitialized = false;
    m_strLastError.clear();
}

const wchar_t* CVECommon::GetLastErrorMessage()
{
    return m_strLastError.c_str();
}

const wchar_t* CVECommon::GetErrorDescription(int error_code)
{
    if (error_code >= 0 && error_code < sizeof(g_ErrorMessages) / sizeof(g_ErrorMessages[0]))
    {
        return g_ErrorMessages[error_code];
    }
    return L"未知错误";
} 

void CVECommon::SetFinalOuptPath( const std::wstring& finalOuptPath )
    {
    m_finalOutputPath = finalOuptPath;
    }

const wchar_t * CVECommon::GetFinalOuptPath( )
    {
    return m_finalOutputPath.c_str  ();
    }

const wchar_t* CVECommon::GetFileMD5( )
    {
    return m_fileMd5.c_str( );
    }

// 新增：文件格式支持检测
bool CVECommon::IsSupportedFormat( const std::string& extension )
    {
    std::string ext_lower = extension;
    std::transform( ext_lower.begin( ) , ext_lower.end( ) , ext_lower.begin( ) , [ ]( unsigned char c ) { return static_cast< char >( std::tolower( c ) ); } );

    // 支持的格式列表
    const std::vector<std::string> supported_formats = {
        ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".mpeg", ".mpg",
        ".rmvb", ".dat", ".3gp", ".3gpp", ".m4v", ".asf", ".vob"
        };

    return std::find( supported_formats.begin( ) , supported_formats.end( ) , ext_lower ) != supported_formats.end( );
    }

// 新增：删除文件（如果存在）
bool CVECommon::DeleteFileIfExists( const std::string& filepath )
    {
    struct _stat file_stat;
    if (_stat( filepath.c_str( ) , &file_stat ) == 0) {
        // 文件存在，尝试删除
        return ( DeleteFileA( filepath.c_str( ) ) != 0 );
        }
    return true; // 文件不存在，认为删除成功
    }

// 新增：大文件检测
bool CVECommon::IsLargeFile( int64_t file_size )
    {
    // 超过2GB的文件被认为是大文件
    return file_size > (2* 1024LL * 1024LL * 1024LL );
    }

// 新增：清理临时文件
void CVECommon::CleanupTempFile( const std::string& temp_path )
    {
    if (!temp_path.empty( )) {
        DeleteFileIfExists( temp_path );
        }
    }

std::wstring CVECommon::GetAVErrorString( int error_code )
    {
    char errbuf [ AV_ERROR_MAX_STRING_SIZE ] = { 0 };
    av_strerror( error_code , errbuf , AV_ERROR_MAX_STRING_SIZE );

    // 为特定错误提供更友好的描述
    if (error_code == AVERROR( ENOMEM )) {
        return L"内存不足(ENOMEM)，请尝试关闭其他应用或增加系统可用内存";
        }

    // 转换为宽字符串 - 使用UTF-8编码
    int len = MultiByteToWideChar( CP_UTF8 , 0 , errbuf , -1 , nullptr , 0 );
    if (len <= 0)
        return L"未知错误";

    std::vector<wchar_t> wbuf( len );
    MultiByteToWideChar( CP_UTF8 , 0 , errbuf , -1 , &wbuf [ 0 ] , len );

    return std::wstring( &wbuf [ 0 ] );
    }

std::string CVECommon::GetFileExtension( const std::string& filepath )
    {
    size_t pos = filepath.find_last_of( '.' );
    if (pos != std::string::npos) {
        std::string extension = filepath.substr( pos );
        std::string ext_lower = extension;
        std::transform( ext_lower.begin( ) , ext_lower.end( ) , ext_lower.begin( ) , [ ]( unsigned char c ) { return static_cast< char >( std::tolower( c ) ); } );
        return ext_lower;
        }
    return "";
    }

std::wstring CVECommon::GetFileExtensionW( const std::wstring& filepath )
    {
    size_t pos = filepath.find_last_of( '.' );
    if (pos != std::wstring::npos) {
        std::wstring extension = filepath.substr( pos );
        std::wstring ext_lower = extension;
        std::transform( ext_lower.begin( ) , ext_lower.end( ) , ext_lower.begin( ) , [ ]( wchar_t c ) { return static_cast< wchar_t >( std::towlower( c ) ); } );
        return ext_lower;
        }
    return L"";
    }

bool CVECommon::IsEncryptedVideoFile( const std::wstring& filepath )
    {
    // 获取扩展名（小写，包含点）
    std::wstring ext = GetFileExtensionW( filepath );
    if (ext.empty())
        return false;

    // 必须以 .ve 开头（忽略大小写，GetFileExtensionW 已转换为小写）
    const std::wstring vePrefix = L".ve";
    if (ext.rfind( vePrefix , 0 ) != 0) // 不以 .ve 开头
        return false;

    // 提取原始扩展名（带点）
    std::wstring origExt = L"." + ext.substr( vePrefix.length() );
    if (origExt.length() <= 1) // 没有原始扩展
        return false;

    // 使用现有的格式支持检测函数 (需要窄字符)
    int len = WideCharToMultiByte( CP_UTF8 , 0 , origExt.c_str() , -1 , nullptr , 0 , nullptr , nullptr );
    if (len <= 0)
        return false;
    std::string origExtUtf8( len , '\0' );
    WideCharToMultiByte( CP_UTF8 , 0 , origExt.c_str() , -1 , &origExtUtf8[0] , len , nullptr , nullptr );

    // 去掉字符串末尾的\0
    if (!origExtUtf8.empty() && origExtUtf8.back() == '\0')
        origExtUtf8.pop_back();

    return IsSupportedFormat( origExtUtf8 );
    }

std::vector<std::wstring> CVECommon::FindLicenseFilesForEncryptedVideo( const std::wstring& encryptedVideoPath )
    {
    std::vector<std::wstring> results;

    // 仅处理加密视频文件
    if (!IsEncryptedVideoFile( encryptedVideoPath ))
        return results;

    // 拆分路径
    wchar_t drive[_MAX_DRIVE] = { 0 };
    wchar_t dir[_MAX_DIR] = { 0 };
    wchar_t fname[_MAX_FNAME] = { 0 };
    wchar_t ext[_MAX_EXT] = { 0 };
    _wsplitpath_s( encryptedVideoPath.c_str( ) , drive , dir , fname , ext );

    std::wstring baseDir = std::wstring( drive ) + dir; // 目录（带尾反斜杠）

    std::wstring extFixed = ext;          // 默认使用 ext
    if (extFixed.length( ) > 3 && extFixed.rfind( L".ve" , 0 ) == 0) {
        extFixed = L"." + extFixed.substr( 3 );   // ".vemp4" -> ".mp4"
        }

    std::wstring pattern = baseDir + fname + extFixed + L"-????.veca";

    WIN32_FIND_DATAW fd;
    HANDLE hFind = FindFirstFileW( pattern.c_str( ) , &fd );
    DWORD  dwError( ERROR_SUCCESS );
    if (hFind == INVALID_HANDLE_VALUE) {
        dwError = ::GetLastError( );
        return results; // 未找到任何匹配
        }

    do {
        // fd.cFileName 只含文件名
        const std::wstring fileName = fd.cFileName;
        // 期望格式：fname-XXXX.veca
        size_t hyphenPos = fileName.rfind( L'-' );
        size_t dotPos = fileName.rfind( L'.' );
        if (hyphenPos == std::wstring::npos || dotPos == std::wstring::npos || dotPos <= hyphenPos)
            continue;

        // 验证扩展名 .veca
        if (_wcsicmp( fileName.c_str( ) + dotPos , L".veca" ) != 0)
            continue;

        std::wstring numberStr = fileName.substr( hyphenPos + 1 , dotPos - hyphenPos - 1 );
        if (numberStr.length( ) != 4 || !std::all_of( numberStr.begin( ) , numberStr.end( ) , iswdigit ))
            continue;

        int num = _wtoi( numberStr.c_str( ) );
        if (num < 0 || num > 9999)
            continue;

        results.push_back( baseDir + fileName );
        }
    while (FindNextFileW( hFind , &fd ));

    FindClose( hFind );

    // 可按编号排序（升序）
    std::sort( results.begin( ) , results.end( ) , [ & ]( const std::wstring& a , const std::wstring& b )
        {
        // 提取编号比较
        auto extractNum = [ & ]( const std::wstring& path ) -> int
            {
            size_t slashPos = path.find_last_of( L"\\/" );
            std::wstring fileName = ( slashPos == std::wstring::npos ) ? path : path.substr( slashPos + 1 );
            size_t hyphenPos = fileName.rfind( L'-' );
            size_t dotPos = fileName.rfind( L'.' );
            if (hyphenPos == std::wstring::npos || dotPos == std::wstring::npos || dotPos <= hyphenPos)
                return INT_MAX;
            return _wtoi( fileName.substr( hyphenPos + 1 , dotPos - hyphenPos - 1 ).c_str( ) );
            };
        return extractNum( a ) < extractNum( b );
        } );

    return results;
    }

// 计算指定文件的 MD5 值
std::wstring CVECommon::FileMD5(const std::wstring& filepath,
                                PROGRESS_CALLBACK callback,
                                void* userdata)
{
    

    // 使用宽字符文件打开，兼容 Unicode 路径
    FILE* fp = nullptr;
    if (_wfopen_s(&fp, filepath.c_str(), L"rb") != 0 || !fp)
    {
        m_strLastError = L"FileMD5: 无法打开文件 " + filepath + L"\n";
        OutputDebugStringW(m_strLastError.c_str());
        return L"";
    }

    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"FastFileFingerprint: 无法获取文件信息 " + filepath;
        return L"";
        }
    uint64_t fileSize = static_cast< uint64_t >( st.st_size );

    return FileMD5(fp , fileSize, callback , userdata );
   
}

std::wstring  CVECommon::FileMD5( FILE* fp , uint64_t fileSize , PROGRESS_CALLBACK callback, void* userdata)
    {
    if (!fp) {
        m_strLastError = L"FileMD5: 无效的文件指针";
        return L"";
        }

    using namespace CryptoPP;

    // 获取文件总大小以便回调
    unsigned long long totalBytes = static_cast< unsigned long long >(fileSize < 0 ? 0 : fileSize );

    Weak::MD5 md5;
    const size_t kBufferSize = 32 * 1024; // 32KB 缓冲区
    std::vector<byte> buffer( kBufferSize );

    unsigned long long processedBytes = 0;

    while (!feof( fp )) {
        size_t readBytes = fread( buffer.data( ) , 1 , kBufferSize , fp );
        if (readBytes > 0) {
            md5.Update( buffer.data( ) , readBytes );
            processedBytes += readBytes;
            if (callback) {
                callback( static_cast< unsigned long >( processedBytes ) ,
                    static_cast< unsigned long >( totalBytes ) ,
                    L"计算文件MD5中..." ,
                    userdata );
                }
            }
        }

    fclose( fp );

    if (callback) {
        callback( static_cast< unsigned long >( totalBytes ) , static_cast< unsigned long >( totalBytes ) , L"计算完成" , userdata );
        }

    byte digest [ Weak::MD5::DIGESTSIZE ] = { 0 };
    md5.Final( digest );

    std::string result;
    HexEncoder encoder( new StringSink( result ) , false /* 小写 */ );
    encoder.Put( digest , sizeof( digest ) );
    encoder.MessageEnd( );

    m_fileMd5 = std::wstring( result.begin( ) , result.end( ) );
    return m_fileMd5;
    }
// === 新增：快速文件校验方案实现 ===

// 1. 快速文件指纹验证 - 基于文件属性和部分内容
std::wstring CVECommon::FastFileFingerprint(const std::wstring& filepath)
{

    FILE* fp = nullptr;
    if (_wfopen_s(&fp, filepath.c_str(), L"rb") != 0 || !fp)
    {
        m_strLastError = L"FastFileFingerprint: 无法打开文件 " + filepath;
        return L"";
    }

    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"FastFileFingerprint: 无法获取文件信息 " + filepath;
        return L"";
        }
    uint64_t fileSize = static_cast< uint64_t >( st.st_size );

    return FastFileFingerprint  (fp, fileSize);
}

// 2. 部分哈希校验 - 计算文件关键部分的MD5
std::wstring CVECommon::PartialFileMD5(const std::wstring& filepath, 
                                       size_t head_size, size_t tail_size, size_t middle_size,
                                       PROGRESS_CALLBACK callback, void* userdata)
{
    FILE* fp = nullptr;
    if (_wfopen_s(&fp, filepath.c_str(), L"rb") != 0 || !fp)
    {
        m_strLastError = L"PartialFileMD5: 无法打开文件 " + filepath;
        return L"";
    }

    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"PartialFileMD5: 无法获取文件信息 " + filepath;
        return L"";
        }
    uint64_t fileSize = static_cast< uint64_t >( st.st_size );

    return PartialFileMD5 (fp, fileSize, head_size, tail_size, middle_size, callback, userdata);
}

// 3. CRC32快速校验
uint32_t CVECommon::FastFileCRC32(const std::wstring& filepath,
                                  PROGRESS_CALLBACK callback, void* userdata)
{
    using namespace CryptoPP;  // 添加命名空间声明
    
    FILE* fp = nullptr;
    if (_wfopen_s(&fp, filepath.c_str(), L"rb") != 0 || !fp)
    {
        m_strLastError = L"FastFileCRC32: 无法打开文件 " + filepath;
        return 0;
    }
    
    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"FastFileCRC32: 无法获取文件信息 " + filepath;
        return 0;
        }
    uint64_t fileSize = static_cast< uint64_t >( st.st_size );

    return FastFileCRC32  (fp, fileSize, callback,userdata);
}

// 4. 混合快速校验 - 文件大小 + 部分CRC32
std::wstring CVECommon::HybridFastVerify(const std::wstring& filepath)
{
    FILE* fp = nullptr;
    if (_wfopen_s(&fp, filepath.c_str(), L"rb") != 0 || !fp)
    {
        m_strLastError = L"HybridFastVerify: 无法打开文件 " + filepath;
        return L"";
    }

    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"HybridFastVerify: 无法获取文件信息 " + filepath;
        return L"";
        }

    uint64_t fileSize = static_cast< uint64_t >( st.st_size );
    
    return HybridFastVerify  (fp, fileSize);
}

// 5. 智能校验策略选择
std::wstring CVECommon::SmartFileVerify(const std::wstring& filepath,
                                       PROGRESS_CALLBACK callback, void* userdata)
{
    FILE* fp = nullptr;
    if (_wfopen_s( &fp , filepath.c_str( ) , L"rb" ) != 0 || !fp) {
        m_strLastError = L"SmartFileVerify: 无法打开文件 " + filepath;
        return L"";
        }


    struct _stat64 st = { 0 };
    if (_fstat64( _fileno( fp ) , &st ) != 0) {
        m_strLastError = L"SmartFileVerify: 无法获取文件信息 " + filepath;
        return L"";
        }
    
    uint64_t fileSize = static_cast<uint64_t>(st.st_size);
    
    // 根据文件大小选择最合适的校验策略
    if (fileSize < 10 * 1024 * 1024) {  // 小于10MB - 使用完整MD5
        if (callback) callback(0, 1, L"文件较小，使用完整MD5校验...", userdata);
        return FileMD5(fp, fileSize, callback, userdata);
    }
    else if (fileSize < 100 * 1024 * 1024) {  // 10MB-100MB - 使用部分MD5
        if (callback) callback(0, 1, L"文件中等大小，使用部分MD5校验...", userdata);
        return PartialFileMD5(fp, fileSize , 32*1024, 32*1024, 16*1024, callback, userdata);
    }
    else if (fileSize < 1024LL * 1024 * 1024) {  // 100MB-1GB - 使用快速指纹
        if (callback) callback(0, 1, L"文件较大，使用快速指纹校验...", userdata);
        return FastFileFingerprint(fp,fileSize);
    }
    else {  // 大于1GB - 使用混合快速校验
        if (callback) callback(0, 1, L"超大文件，使用混合快速校验...", userdata);
        return HybridFastVerify(fp , fileSize );
    }
}

// === 新增：基于已打开文件指针的优化版本 ===

// 基于文件指针的快速指纹校验（最优性能）
std::wstring CVECommon::FastFileFingerprint(FILE* fp , uint64_t fileSize )
{
    using namespace CryptoPP;
    
    if (!fp) {
        m_strLastError = L"FastFileFingerprint: 无效的文件指针";
        return L"";
    }

    Weak::MD5 md5;

    // 1. 添加文件大小信息
    md5.Update( reinterpret_cast< const byte* >( &fileSize ) , sizeof( fileSize ) );
    
    // 2. 读取文件头（前8KB）
    const size_t headSize = 8192;
    std::vector<byte> buffer(headSize);
    
    fseek(fp, 0, SEEK_SET);
    size_t readBytes = fread(buffer.data(), 1, headSize, fp);
    if (readBytes > 0) {
        md5.Update(buffer.data(), readBytes);
    }
    
    // 3. 如果文件足够大，读取文件尾（后8KB）
    if (fileSize > headSize * 2) {
        fseek(fp, -static_cast<long>(headSize), SEEK_END);
        readBytes = fread(buffer.data(), 1, headSize, fp);
        if (readBytes > 0) {
            md5.Update(buffer.data(), readBytes);
        }
    }
    

    fclose(fp);
    
    byte digest[Weak::MD5::DIGESTSIZE] = {0};
    md5.Final(digest);
    
    std::string result;
    HexEncoder encoder(new StringSink(result), false);
    encoder.Put(digest, sizeof(digest));
    encoder.MessageEnd();
    
    m_fileMd5 = std::wstring(result.begin(), result.end());
    return m_fileMd5;
}

// 基于文件指针的部分MD5校验
std::wstring CVECommon::PartialFileMD5(FILE* fp, uint64_t fileSize , size_t head_size, size_t tail_size, size_t middle_size,
                                       PROGRESS_CALLBACK callback, void* userdata)
{
    using namespace CryptoPP;
    
    if (!fp) {
        m_strLastError = L"PartialFileMD5: 无效的文件指针";
        return L"";
    }
  
    Weak::MD5 md5;
    std::vector<byte> buffer((std::max)({head_size, tail_size, middle_size}));
    
    size_t totalToRead = head_size + tail_size + middle_size;
    size_t totalRead = 0;
    
    // 读取文件头
    if (callback)
        callback(0, static_cast<unsigned long>(totalToRead), L"读取文件头...", userdata);
    
    fseek(fp, 0, SEEK_SET);
    size_t readBytes = fread(buffer.data(), 1, head_size, fp);
    if (readBytes > 0) {
        md5.Update(buffer.data(), readBytes);
        totalRead += readBytes;
    }
    
    // 读取文件中间部分
    if (fileSize > head_size + tail_size + middle_size) {
        if (callback) 
            callback(static_cast<unsigned long>(totalRead), static_cast<unsigned long>(totalToRead), L"读取文件中间...", userdata);
        
        long long middlePos = fileSize / 2 - middle_size / 2;
        fseek(fp, static_cast<long>(middlePos), SEEK_SET);
        readBytes = fread(buffer.data(), 1, middle_size, fp);
        if (readBytes > 0) {
            md5.Update(buffer.data(), readBytes);
            totalRead += readBytes;
        }
    }
    
    // 读取文件尾
    if (fileSize > head_size) {
        if (callback) 
            callback(static_cast<unsigned long>(totalRead), static_cast<unsigned long>(totalToRead), L"读取文件尾...", userdata);
        
        fseek(fp, -static_cast<long>(tail_size), SEEK_END);
        readBytes = fread(buffer.data(), 1, tail_size, fp);
        if (readBytes > 0) {
            md5.Update(buffer.data(), readBytes);
            totalRead += readBytes;
        }
    }
    
  
    fclose(fp);
    
    if (callback) 
        callback(static_cast<unsigned long>(totalRead), static_cast<unsigned long>(totalToRead), L"计算完成", userdata);
    
    byte digest[Weak::MD5::DIGESTSIZE] = {0};
    md5.Final(digest);
    
    std::string result;
    HexEncoder encoder(new StringSink(result), false);
    encoder.Put(digest, sizeof(digest));
    encoder.MessageEnd();
    
    m_fileMd5 = std::wstring(result.begin(), result.end());
    return m_fileMd5;
}

// 基于文件指针的CRC32校验
uint32_t CVECommon::FastFileCRC32(FILE* fp, uint64_t fileSize , PROGRESS_CALLBACK callback, void* userdata)
{
    using namespace CryptoPP;
    
    if (!fp) {
        m_strLastError = L"FastFileCRC32: 无效的文件指针";
        return 0;
    }

    // CRC32查找表 - 使用IEEE 802.3标准
    static uint32_t crc_table[256];
    static bool table_initialized = false;
    
    if (!table_initialized) {
        for (uint32_t i = 0; i < 256; i++) {
            uint32_t crc = i;
            for (int j = 0; j < 8; j++) {
                if (crc & 1) {
                    crc = (crc >> 1) ^ 0xEDB88320;
                } else {
                    crc >>= 1;
                }
            }
            crc_table[i] = crc;
        }
        table_initialized = true;
    }
    
    uint32_t crc = 0xFFFFFFFF;
    const size_t kBufferSize = 64 * 1024; // 64KB缓冲区
    std::vector<byte> buffer(kBufferSize);
    unsigned long long processedBytes = 0;
    
    fseek(fp, 0, SEEK_SET);
    
    while (processedBytes < fileSize) {
        size_t toRead = (std::min)(kBufferSize, static_cast<size_t>(fileSize - processedBytes));
        size_t readBytes = fread(buffer.data(), 1, toRead, fp);
        
        if (readBytes == 0) 
            break;
        
        for (size_t i = 0; i < readBytes; i++) {
            crc = crc_table[(crc ^ buffer[i]) & 0xFF] ^ (crc >> 8);
        }
        processedBytes += readBytes;
        
        if (callback) {
            callback(static_cast<unsigned long>(processedBytes), 
                    static_cast<unsigned long>(fileSize), 
                    L"计算CRC32中...", userdata);
        }
    }
    
  
    fclose(fp);
    
    if (callback) {
        callback(static_cast<unsigned long>(fileSize), static_cast<unsigned long>(fileSize), L"CRC32计算完成", userdata);
    }
    
    return crc ^ 0xFFFFFFFF;
}

// 基于文件指针的混合快速校验
std::wstring CVECommon::HybridFastVerify(FILE* fp , uint64_t fileSize )
{
    using namespace CryptoPP;
    
    if (!fp) {
        m_strLastError = L"HybridFastVerify: 无效的文件指针";
        return L"";
    }
    
    // 只读取文件的关键部分进行CRC32计算
    uint32_t partialCRC = 0xFFFFFFFF;
    static uint32_t crc_table[256];
    static bool table_initialized = false;
    
    if (!table_initialized) {
        for (uint32_t i = 0; i < 256; i++) {
            uint32_t crc = i;
            for (int j = 0; j < 8; j++) {
                if (crc & 1) {
                    crc = (crc >> 1) ^ 0xEDB88320;
                } else {
                    crc >>= 1;
                }
            }
            crc_table[i] = crc;
        }
        table_initialized = true;
    }
    
    // 读取文件头部8KB
    const size_t chunkSize = 8192;
    std::vector<byte> buffer(chunkSize);
    
    fseek(fp, 0, SEEK_SET);
    size_t readBytes = fread(buffer.data(), 1, chunkSize, fp);
    
    for (size_t i = 0; i < readBytes; i++) {
        partialCRC = crc_table[(partialCRC ^ buffer[i]) & 0xFF] ^ (partialCRC >> 8);
    }
    
    // 如果文件足够大，再读取文件尾部8KB
    if (fileSize > chunkSize * 2) {
        fseek(fp, -static_cast<long>(chunkSize), SEEK_END);
        readBytes = fread(buffer.data(), 1, chunkSize, fp);
        for (size_t i = 0; i < readBytes; i++) {
            partialCRC = crc_table[(partialCRC ^ buffer[i]) & 0xFF] ^ (partialCRC >> 8);
        }
    }
    
 
    fclose( fp );
    
    partialCRC ^= 0xFFFFFFFF;
    
    // 组合文件大小和部分CRC32
    wchar_t result[64] = {0};
    swprintf_s(result, L"%016llx_%08x", fileSize, partialCRC);
    
    m_fileMd5 = std::wstring(result);
    return m_fileMd5;
}
