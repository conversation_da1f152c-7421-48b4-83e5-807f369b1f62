﻿// CDlgInfo.cpp: 实现文件
//

#include "pch.h"
#include "VideoPlayer.h"
#include "CDlgInfo.h"
#include "funLib.h"
#include "progressHand.h"
#include "..\EP-common\VECommon.h"
#include "afxdialogex.h"


// CDlgInfo 对话框

IMPLEMENT_DYNAMIC(CDlgInfo, CDialogEx)

CDlgInfo::CDlgInfo(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_INFO, pParent)
	, m_pva(nullptr)
	, m_ErrorNum(0)
    , m_strFilePath(_T("") )
{

}

CDlgInfo::~CDlgInfo()
{
}

void CDlgInfo::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange( pDX );
	DDX_Control( pDX , IDC_STATIC_ID , m_gsID );
	DDX_Control( pDX , IDC_STATIC_PASS , m_gsPass );
	DDX_Control( pDX , IDC_EDIT_INFO_NO , m_geID );
	DDX_Control( pDX , IDC_EDIT_INFO_PASS , m_gePass );
	DDX_Control( pDX , IDC_STATIC_INFO_DES , m_gsInfo );
	DDX_Control( pDX , IDC_PROGRESS_VA , m_progressCtrl );
	}


BEGIN_MESSAGE_MAP(CDlgInfo, CDialogEx)
	ON_MESSAGE( UWM_CALCULATION_PROGRESS , &CDlgInfo::OnProgress )
	ON_MESSAGE( UWM_OP_COMPLETE ,          &CDlgInfo::OnOpComplete )
	ON_BN_CLICKED( IDC_BUTTON_INFO_PLAY ,  &CDlgInfo::OnBnClickedButtonInfoPlay )
	ON_BN_CLICKED( IDC_BUTTON_INFO_BUY ,  &CDlgInfo::OnBnClickedButtonInfoBuy )
END_MESSAGE_MAP()


// CDlgInfo 消息处理程序


BOOL CDlgInfo::OnInitDialog( )
	{
	CDialogEx::OnInitDialog( );

	if (m_pva) {
        m_gsInfo.SetWindowText(_T("授权文件校验中……" ) );
        SetWindowText( APP_NAME );
		}
	else {
		OnCancel( );
		}

    m_progressCtrl.SetRange( 0 , 100 );
    m_progressCtrl.SetStep( 1 );
    m_progressCtrl.SetPos( 0 );

    AfxBeginThread( WorkThraed , this );
	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
	}


void CDlgInfo::OnBnClickedButtonInfoPlay( )
	{
	CString strPass;
	m_gePass.GetWindowText( strPass );

	if (strPass.IsEmpty( )) {
        CString strTemp;
        strTemp.LoadStringW( IDS_STRING103 );
        MessageBox( strTemp , APP_NAME , MB_OK );
		return;
		}

	m_ErrorNum++;
	if (m_pva) {
		m_pva->SetPass( strPass );
		if (m_pva->VerifyPass( )) {
			OnOK( );
			}
		else {
			CString strTemp;
			strTemp.LoadStringW(IDS_STRING102 );
			MessageBox( strTemp , APP_NAME , MB_OK );
			}
		}

	if (3 <= m_ErrorNum) {
		OnCancel( );
		}
	}

BOOL CDlgInfo::PreTranslateMessage( MSG* pMsg )
    {
    if (pMsg->message == WM_KEYDOWN || pMsg->message == WM_SYSKEYDOWN) {
        if (pMsg->wParam == VK_RETURN || pMsg->wParam == VK_ESCAPE ||
            ( pMsg->wParam == VK_F4 && ( pMsg->lParam & ( 1 << 29 ) ) )) // Alt+F4
            {
            return TRUE; // 吞掉
            }
        }
    return CDialogEx::PreTranslateMessage( pMsg );
    }

// 屏蔽系统菜单关闭（Alt+F4 等）
BOOL CDlgInfo::OnCommand( WPARAM wParam , LPARAM lParam )
    {
    if (wParam == SC_CLOSE) {
		OnCancel( );
		return TRUE;
        }
    return CDialogEx::OnCommand( wParam , lParam );
    }

void CDlgInfo::OnBnClickedButtonInfoBuy( )
	{
	OpenURL( m_pva->GetVBuyURL( ).GetString  () );
	}
//=================================================================
UINT CDlgInfo::WorkThraed( LPVOID lParam )
    {
    CDlgInfo* pThis = reinterpret_cast< CDlgInfo* >( lParam );
    ASSERT( pThis != NULL );
  
    bool ret = pThis->Md5Mat();
    //pThis->CheckMd5Finish();

_exit:
    pThis->PostMessage( UWM_OP_COMPLETE , static_cast< WPARAM >(ret) , 0 );
    return 0;
    }
//=================================================================
bool CDlgInfo::Md5Mat( void )
    {
    std::wstring fileMD5 = CVECommon::SmartFileVerify( m_strFilePath.GetString( ) , progressHand::progressWord, this );

    if (fileMD5.compare( m_pva->GetFileMD5( ) )) {
		return false;
        }
    
	return true;
	}
//=================================================================
void CDlgInfo::CheckMd5Finish( )
	{
    m_progressCtrl.SetPos( 100 );
	}
//=============================================================
LRESULT CDlgInfo::OnProgress( WPARAM wParam , LPARAM lParam )
    {
    progressHand::ProgressData* pData = reinterpret_cast< progressHand::ProgressData* >( wParam );
    if (!pData)
        return 0;

    double percent =  static_cast< double >( pData->cur ) / static_cast< double >( pData->tot );

    CString msg;
    if (!pData->text.IsEmpty( ))
        msg = pData->text;


    m_progressCtrl.SetPos( static_cast< int >( percent ) );

    delete pData;
    return 0;
    }
//=================================================================
LRESULT CDlgInfo::OnOpComplete( WPARAM wParam , LPARAM lParam )
    {
    bool result = static_cast< bool>( wParam );

    if (result == true) {
        if (m_pva->GetTryTime( )) {
            GetDlgItem( IDC_BUTTON_INFO_BUY )->SetWindowText( m_pva->GetVBuyText( ) );
            GetDlgItem( IDC_BUTTON_INFO_BUY )->ShowWindow( SW_SHOW );
            }
        // 确保返回的字符串是Unicode格式，必要时进行转换
        CStringW des = m_pva->GetVDes( );
        m_gsInfo.SetWindowText( des );
        m_geID.SetWindowText( m_pva->GetAuthID( ) );
        SetWindowText( m_pva->GetVTitel( ) );
        CheckMd5Finish( );
        }
    else {
        MessageBox(_T("授权文件不符。" ) , APP_NAME , MB_ICONERROR | MB_OK );
        OnCancel( );
        }

    return 0;
    }