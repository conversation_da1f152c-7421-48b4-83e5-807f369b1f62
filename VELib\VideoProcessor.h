﻿#pragma once

#include "VideoDefinition.h"
#include "../EP-common/VideoAESCommon.h"
#include <string>
#include <memory>
#include <stdexcept>

// 前向声明
struct AVFormatContext;
struct AVCodecContext;
struct AVPacket;
struct AVFrame;

// AVPacket RAII包装器 - 异常安全的内存管理
class AVPacketWrapper {
private:
    AVPacket* m_packet;
    bool m_has_data; // 跟踪是否包含需要释放的数据
    
public:
    AVPacketWrapper() : m_packet(av_packet_alloc()), m_has_data(false) {
        if (!m_packet) {
            throw std::bad_alloc();
        }
    }
    
    ~AVPacketWrapper() {
        if (m_packet) {
            // 异常安全：确保在析构时释放所有资源
            if (m_has_data) {
                av_packet_unref(m_packet);  // 先释放数据引用
            }
            av_packet_free(&m_packet);      // 再释放结构体
        }
    }
    
    AVPacket* get() { return m_packet; }
    AVPacket* operator->() { return m_packet; }
    AVPacket& operator*() { return *m_packet; }
    
    // 手动释放数据引用
    void unref() {
        if (m_packet && m_has_data) {
            av_packet_unref(m_packet);
            m_has_data = false;
        }
    }
    
    // 标记包含数据（在av_read_frame成功后调用）
    void mark_has_data() {
        m_has_data = true;
    }
    
    // 检查是否包含数据
    bool has_data() const { return m_has_data; }
    
    bool is_valid() const { return m_packet != nullptr; }
    
    // 禁止拷贝
    AVPacketWrapper(const AVPacketWrapper&) = delete;
    AVPacketWrapper& operator=(const AVPacketWrapper&) = delete;
    
    // 支持移动
    AVPacketWrapper(AVPacketWrapper&& other) noexcept 
        : m_packet(other.m_packet), m_has_data(other.m_has_data) {
        other.m_packet = nullptr;
        other.m_has_data = false;  // ✅ 重置moved-from对象的状态
    }
    
    AVPacketWrapper& operator=(AVPacketWrapper&& other) noexcept {
        if (this != &other) {
            // ✅ 先清理当前对象的资源
            if (m_packet) {
                if (m_has_data) {
                    av_packet_unref(m_packet);
                }
                av_packet_free(&m_packet);
            }
            
            // ✅ 移动资源和状态
            m_packet = other.m_packet;
            m_has_data = other.m_has_data;
            
            // ✅ 重置moved-from对象
            other.m_packet = nullptr;
            other.m_has_data = false;
        }
        return *this;
    }
};

// 视频处理器类
class VE_API CVideoProcessor
{
public:
    CVideoProcessor();
    virtual ~CVideoProcessor();

    // 主要功能函数
    int EncryptVideoFile(EncryptParams* params);
#ifdef DECRYPT_VIDEO
    int DecryptVideoFile(EncryptParams* params);
#endif
    int GetFileInfo(const wchar_t* filepath, VideoInfo* info);
    int GetVideoKeyFrameCount(const wchar_t* filepath, int* keyFrameCount);

    // 设置进度回调
    void SetProgressCallback(PROGRESS_CALLBACK callback, void* userdata);
    
    // 错误处理
    const wchar_t* GetLastError() const;
    std::wstring GetFinalOuptPath( );
private:
    // 初始化和清理
    bool InitializeFFmpeg();
    void CleanupFFmpeg();

    // 文件操作
    int OpenInputFile(const char* filepath);
    int CreateOutputFile(const char* filepath, const char* format = nullptr);
    void CloseFiles();

    // 视频处理 - 异常安全版本
    int ProcessVideoEncryption(EncryptParams* params);
#ifdef DECRYPT_VIDEO
    int ProcessVideoDecryption(EncryptParams* params);
#endif

    // 数据包处理 - 异常安全版本
    bool ProcessPacket(AVPacket* packet, bool encrypt);
    bool ProcessVideoPacket(AVPacket* packet, bool encrypt);
    bool ProcessAudioPacket(AVPacket* packet, bool encrypt);
    
    // 新增：异常安全的包处理循环
    int ProcessPacketsWithErrorHandling(bool encrypt);

    // 辅助函数
    std::string GenerateOutputPath(const std::string& input_path, const std::string& ouput_path , bool encrypt);
    void UpdateProgress(unsigned long current, unsigned long total, const std::wstring& message);
    int CountKeyFrames();
    int CountKeyFramesOptimized(AVFormatContext* formatCtx, int videoStreamIndex);
    void PrepareOutputMemory();
    bool ValidateInputStreams(std::wstring& errorDetail);
    bool TryRepairInputStreams(); // 尝试修复输入流中的问题
    const char* GetCompatibleOutputFormat(AVCodecID codec_id); // 根据编解码器选择兼容的输出格式
    
    // 新增：参考Sepration.cpp的文件处理逻辑
    int RenameOutputFile(const std::string& temp_path, const std::string& final_path);
    //std::string GetFinalOutputPath(const std::string& temp_path, bool encrypt, const std::string& original_ext);
    
    // 新增：临时文件处理 - 参考Sepration.cpp
    std::string GenerateTempOutputPath(const std::string& directory, const std::string& basename, const std::string& extension);
    int FinalizeTempFile(const std::string& temp_path, const std::string& original_ext, bool is_dat_to_mp4);
   
    
    // 新增：动态加密策略管理
    VideoEncryptType DetermineVideoEncryptType(const std::string& extension);
    AudioEncryptType DetermineAudioEncryptType(const std::string& extension);
    
    VideoEncryptType GetOptimalEncryptType(AVCodecID codec_id);
    
    
    // 新增：元数据验证和处理
    bool ValidateStreamMetadata(int stream_index);
    void FixStreamTimestamps(AVPacket* packet);
    bool VerifyOutputMetadata();
    
    // 错误处理
    void SetLastError(const std::wstring& error);
   
    

private:
    // PImpl - 隐藏实现细节，避免 DLL 导出 STL / 内部类型
    class Impl;
    std::unique_ptr<Impl> m_impl;
};