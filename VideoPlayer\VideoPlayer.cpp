﻿
// VideoPlayer.cpp: 定义应用程序的类行为。
//

#include "pch.h"
#include "framework.h"
#include "VideoPlayer.h"
#include "VideoPlayerDlg.h"
#include <system/AppRuntime.h>
#ifdef _DEBUG
#define new DEBUG_NEW
#include <libssh/libssh.h>   // 间接用到 libgcrypt / libgpg-error
#include <gcrypt.h>
#include <gpg-error.h>
#endif


// CVideoPlayerApp

BEGIN_MESSAGE_MAP(CVideoPlayerApp, CWinApp)
	ON_COMMAND(ID_HELP, &CWinApp::OnHelp)
END_MESSAGE_MAP()


// CVideoPlayerApp 构造

CVideoPlayerApp::CVideoPlayerApp()
{
	// 支持重新启动管理器
	m_dwRestartManagerSupportFlags = AFX_RESTART_MANAGER_SUPPORT_RESTART;

	// TODO: 在此处添加构造代码，
	// 将所有重要的初始化放置在 InitInstance 中
}


// 唯一的 CVideoPlayerApp 对象

CVideoPlayerApp theApp;

// CVideoPlayerApp 初始化

BOOL CVideoPlayerApp::InitInstance()
{   
	// 如果一个运行在 Windows XP 上的应用程序清单指定要
	// 使用 ComCtl32.dll 版本 6 或更高版本来启用可视化方式，
	//则需要 InitCommonControlsEx()。  否则，将无法创建窗口。
	INITCOMMONCONTROLSEX InitCtrls;
	InitCtrls.dwSize = sizeof(InitCtrls);
	// 将它设置为包括所有要在应用程序中使用的
	// 公共控件类。
	InitCtrls.dwICC = ICC_WIN95_CLASSES;
	InitCommonControlsEx(&InitCtrls);

	CWinApp::InitInstance();

	AfxEnableControlContainer();

	// 解析命令行参数
    if ( !m_cmdParser.ParseCommandLine ( ) ) {
        return FALSE;
        }

    // 如果显示帮助，显示后退出
    if ( m_cmdParser.IsHelp ( ) ) {
        m_cmdParser.ShowHelp ( );
        return FALSE;
        }

    auto rt = CAppRuntime::ForCurrentExe( true ); // Local\ 会话级
    if (rt.AlreadyRunning( )) {
        MessageBoxW( nullptr , L"程序已在运行。" , L"提示" , MB_OK | MB_ICONINFORMATION );
        return FALSE;
        }
	// 创建 shell 管理器，以防对话框包含
	// 任何 shell 树视图控件或 shell 列表视图控件。
	CShellManager *pShellManager = new CShellManager;

	// 激活"Windows Native"视觉管理器，以便在 MFC 控件中启用主题
	CMFCVisualManager::SetDefaultManager(RUNTIME_CLASS(CMFCVisualManagerWindows));

	// 标准初始化
	// 如果未使用这些功能并希望减小
	// 最终可执行文件的大小，则应移除下列
	// 不需要的特定初始化例程
	// 更改用于存储设置的注册表项
	// TODO: 应适当修改该字符串，
	// 例如修改为公司或组织名
	//SetRegistryKey(_T("应用程序向导生成的本地应用程序"));
	// 
  
	CVideoPlayerDlg dlg;
	m_pMainWnd = &dlg;
	
	// 如果有命令行文件参数，设置到对话框
    if ( m_cmdParser.HasFile ( ) ) {
        dlg.SetCommandLineFile ( m_cmdParser.GetFilePath ( ), m_cmdParser.IsAutoPlay ( ) );
        }

	// 统一使用传统模式：模态对话框
	INT_PTR nResponse = dlg.DoModal();
	if (nResponse == IDOK)
	{
		// TODO: 在此放置处理何时用
		//  "确定"来关闭对话框的代码
	}
	else if (nResponse == IDCANCEL)
	{
		// TODO: 在此放置处理何时用
		//  "取消"来关闭对话框的代码
	}
	else if (nResponse == -1)
	{
		TRACE(traceAppMsg, 0, "警告: 对话框创建失败，应用程序将意外终止。\n");
		TRACE(traceAppMsg, 0, "警告: 如果您在对话框上使用 MFC 控件，则无法 #define _AFX_NO_MFC_CONTROLS_IN_DIALOGS。\n");
	}

	// 删除上面创建的 shell 管理器。
	if (pShellManager != nullptr)
	{
		delete pShellManager;
	}

#if !defined(_AFXDLL) && !defined(_AFX_NO_MFC_CONTROLS_IN_DIALOGS)
	ControlBarCleanUp();
#endif

	// 由于对话框已关闭，所以将返回 FALSE 以便退出应用程序，
	//  而不是启动应用程序的消息泵。
	return FALSE;
}



int CVideoPlayerApp::ExitInstance( )
	{
    CMFCVisualManager* pVisualManager = CMFCVisualManager::GetInstance( );

    if (NULL != pVisualManager) {
        delete pVisualManager;
        CMFCVisualManager::SetDefaultManager( NULL );
        }
#ifdef _DEBUG
    // 1) 结束 libssh（若有）
    ssh_finalize( );              // 会顺带释放 libgcrypt / gpg-error

    // 2) 再保险：显式终止底层库
    gcry_control( GCRYCTL_TERM_SECMEM, 0);   // 终止 libgcrypt
#endif

	return CWinApp::ExitInstance( );
	}
