﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="UI">
      <UniqueIdentifier>{d466b860-69be-4e0b-8bef-5792d67a910f}</UniqueIdentifier>
    </Filter>
    <Filter Include="UI\pngbutton">
      <UniqueIdentifier>{ed274027-081b-4b69-897a-ecbb9b6012ea}</UniqueIdentifier>
    </Filter>
    <Filter Include="FunLib">
      <UniqueIdentifier>{ee85c153-cbaf-4492-baf3-2eb5738575a2}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="VideoPlayer.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="VideoPlayerDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="framework.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="UI\DUIButton.h">
      <Filter>UI</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\Border.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\colors.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\GResource.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\Layers.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\MemDC.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\Style.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\StyleButton.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\pngButton\StyleEnum.h">
      <Filter>UI\pngbutton</Filter>
    </ClInclude>
    <ClInclude Include="UI\BitmapSlider.h">
      <Filter>UI</Filter>
    </ClInclude>
    <ClInclude Include="UI\memdc.h">
      <Filter>UI</Filter>
    </ClInclude>
    <ClInclude Include="CDlgInfo.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CDlgOpenFile.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="funLib.h">
      <Filter>FunLib</Filter>
    </ClInclude>
    <ClInclude Include="CDlgAsk.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="UI\CaptchaStatic.h">
      <Filter>UI</Filter>
    </ClInclude>
    <ClInclude Include="CDlgWed.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="progressHand.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ScopeGuard.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CommandLineParser.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="VideoPlayer.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="VideoPlayerDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="UI\DUIButton.cpp">
      <Filter>UI</Filter>
    </ClCompile>
    <ClCompile Include="UI\pngButton\Border.cpp">
      <Filter>UI\pngbutton</Filter>
    </ClCompile>
    <ClCompile Include="UI\pngButton\Layers.cpp">
      <Filter>UI\pngbutton</Filter>
    </ClCompile>
    <ClCompile Include="UI\pngButton\Style.cpp">
      <Filter>UI\pngbutton</Filter>
    </ClCompile>
    <ClCompile Include="UI\pngButton\StyleButton.cpp">
      <Filter>UI\pngbutton</Filter>
    </ClCompile>
    <ClCompile Include="UI\BitmapSlider.cpp">
      <Filter>UI</Filter>
    </ClCompile>
    <ClCompile Include="CDlgInfo.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CDlgOpenFile.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="funLib.cpp">
      <Filter>FunLib</Filter>
    </ClCompile>
    <ClCompile Include="CDlgAsk.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="UI\CaptchaStatic.cpp">
      <Filter>UI</Filter>
    </ClCompile>
    <ClCompile Include="LeakHook.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CDlgWed.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="progressHand.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CommandLineParser.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="VideoPlayer.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\VideoPlayer.rc2">
      <Filter>资源文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\VideoPlayer.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png1.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\6.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\03.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\02.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\mp_channel.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\mp_channel_active.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\mp_thumb.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\mp_thumb_active.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\06.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\04.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\01.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\vol_on.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\vol_off.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\jy32.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\yl32.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\png\05.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\ok.ico">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>