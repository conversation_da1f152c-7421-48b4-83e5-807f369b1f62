#pragma once
#include "afxwin.h"

// CGPHeader
#define WMU_HEADER_BUTTON	(WM_APP+1003)

class CGPListHeader : public CHeaderCtrl
{
	DECLARE_DYNAMIC(CGPListHeader)

public:
    CGPListHeader();
	virtual ~CGPListHeader();
    void SetMenuItem( int iItem) {
        m_iMenuItem = iItem;
        }
    void GetMenuPos(int &ix, int &iy);
protected:
	DECLARE_MESSAGE_MAP()
	afx_msg void OnPaint();
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
    virtual void DrawItem( LPDRAWITEMSTRUCT lpDrawItemStruct );
private:
    Image * m_pStdImage;
    Image * m_pMenuImage;
    int     m_iMenuItem;
    };


