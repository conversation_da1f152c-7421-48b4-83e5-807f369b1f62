#include "stdafx.h"
#include "Error.h"
#include "ExceptionHandler.h"
#include <windows.h>  
#include <Shlwapi.h>
#include <time.h>
#include <process.h>
#include <Shlobj.h>
#include <locale.h>
#include <crtdbg.h>
#include <strsafe.h>

CError        g_error;
CReportBug    g_rb;
CLogging      g_log;

/*------------------------------------------------------------------------------------*/
CError::CError( ) 
    {
   SetDllDirectoryA( "" );
#ifdef _DEBUG
    ExceptionHandler::GetInstance().Initialize( APP_SOFT_NAME, true);
#else
   ExceptionHandler::GetInstance( ).Initialize( APP_SOFT_NAME , false );
#endif
    }
/*------------------------------------------------------------------------------------*/
CError::~CError( )
    {
    CLOSE_OPENED_LIBRARIES;
    }