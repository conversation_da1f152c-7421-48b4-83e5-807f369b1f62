#pragma once

#define UWM_CALCULATION_BASE        (WM_APP + 10)
#define UWM_CALCULATION_PROGRESS    (UWM_CALCULATION_BASE)
#define UWM_OP_COMPLETE             (UWM_CALCULATION_BASE + 1)


namespace progressHand
    {
    struct ProgressData
        {
        unsigned long cur;
        unsigned long tot;
        CString        text;
        };

    void progressWord( unsigned long current , unsigned long total , LPCTSTR pszMsg , void* userdata );
    }