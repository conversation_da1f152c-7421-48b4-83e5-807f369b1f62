﻿
// VEDlg.cpp : 实现文件
//

#include "stdafx.h"
#include <regex>
#include <vector>
#include <cstdint>
#include "VE.h"
#include "VEDlg.h"
#include "foot.h"
#include "afxdialogex.h"
#include "DlgBuy.h"
#include "GetMID.h"
#include "DlgAct.h"
#include "CDlgRemoveKey.h"
#include "msg.h"
#include <ScopeGuard.h>
#include "afxwin.h"
#include <Funlib.h>
#include <net\netTools.h>
#include <string\strTools.h>
#include <fileTools\fileTools.h>
#include <time\timeTools.h>
#include <strsafe.h>
#ifdef _DEBUG
#define new DEBUG_NEW
#endif



class CAboutDlg : public CGPDlgSkinBase
{
public:
	CAboutDlg();

// 对话框数据
	enum { IDD = IDD_ABOUTBOX };
    bool GetRem( void )
        {
        return m_bRemKey;
        }
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	
// 实现
protected:
	DECLARE_MESSAGE_MAP()
private:
	CGPStatic m_csLogo;
public:
	virtual BOOL OnInitDialog();
	afx_msg void OnBnClickedButtonRemKey();
	bool      m_bRemKey;
	CGPStatic m_gsName;
	CGPStatic m_gsEmail;
	CGPStatic m_gsWed;
	CGPStatic m_gsVer;
    CGPStatic m_gsLicTo;
    CGPStatic m_gsLicExp;
    CGPStatic m_gsLicCnt;
    CString  m_strLicContact;
    CString  m_strLicExp;
    CString  m_strLicCnt;
    void LoadLicenseInfo();
	};

CAboutDlg::CAboutDlg() : CGPDlgSkinBase(CAboutDlg::IDD), m_bRemKey(false)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CGPDlgSkinBase::DoDataExchange( pDX );
	DDX_Control( pDX , IDC_STATIC_LOGO , m_csLogo );
	DDX_Control( pDX , IDC_STATIC_NAME , m_gsName );
	DDX_Control( pDX , IDC_STATIC_EMAIL , m_gsEmail );
	DDX_Control( pDX , IDC_STATIC_WED , m_gsWed );
	DDX_Control( pDX , IDC_STATIC_VER , m_gsVer );
	}

BEGIN_MESSAGE_MAP(CAboutDlg, CGPDlgSkinBase )
	ON_BN_CLICKED(IDC_BUTTON_REM_KEY, &CAboutDlg::OnBnClickedButtonRemKey)
END_MESSAGE_MAP()


// CVEDlg 对话框



CVEDlg::CVEDlg(CWnd* pParent /*=NULL*/)
	: CGPDlgSkinBase(CVEDlg::IDD, pParent)
	, m_strVEFile(_T(""))
	, m_bIsOpen(FALSE)
	, m_bFun(FALSE)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CVEDlg::DoDataExchange(CDataExchange* pDX)
{
	CGPDlgSkinBase::DoDataExchange( pDX );
	DDX_Control( pDX , IDC_TAB1 , m_tab );
	DDX_Control( pDX , IDC_MFCBUTTON_NEW , m_butNew );
	DDX_Control( pDX , IDC_MFCBUTTON_OPEN , m_butOpen );
	DDX_Control( pDX , IDC_MFCBUTTON_SAVE , m_butSave );
	DDX_Control( pDX , IDC_MFCBUTTON_HELP , m_btnHelp );
	DDX_Control( pDX , IDC_MFCBUTTON_BUY , m_btnBuy );
	}

BEGIN_MESSAGE_MAP(CVEDlg, CGPDlgSkinBase )
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_MESSAGE(UWM_NEW_VEFILE, &CVEDlg::OnNewVEFile)
	ON_MESSAGE(UWM_SAVE_VE, &CVEDlg::OnSaveVE)
	ON_MESSAGE(UWM_ENCRYPT_END, &CVEDlg::OnEncryptEnd)
	ON_MESSAGE( UM_WND_SKINMENU , &CVEDlg::OnSkinMeun )
	ON_NOTIFY(TCN_SELCHANGE, IDC_TAB1, &CVEDlg::OnTcnSelchangeTab1)
	ON_BN_CLICKED(IDC_MFCBUTTON_OPEN, &CVEDlg::OnBnClickedMfcbuttonOpen)
	ON_BN_CLICKED(IDC_MFCBUTTON_SAVE, &CVEDlg::OnBnClickedMfcbuttonSave)
	ON_BN_CLICKED(IDC_MFCBUTTON_NEW, &CVEDlg::OnBnClickedMfcbuttonNew)
	ON_BN_CLICKED(IDC_MFCBUTTON_BUY, &CVEDlg::OnBnClickedMfcbuttonBuy)
	ON_BN_CLICKED(IDC_MFCBUTTON_HELP, &CVEDlg::OnBnClickedMfcbuttonHelp)
	ON_COMMAND(ID_POP_NEW, &CVEDlg::OnPopNew)
	ON_COMMAND(ID_POP_OPEN, &CVEDlg::OnPopOpen)
	ON_COMMAND(ID_POP_SAVE, &CVEDlg::OnPopSave)
	ON_COMMAND(ID_POP_HELP, &CVEDlg::OnPopHelp)
	ON_COMMAND(ID_POP_ABOUT, &CVEDlg::OnPopAbout)
	ON_COMMAND(ID_POP_EXIT, &CVEDlg::OnPopExit)
END_MESSAGE_MAP()


// CVEDlg 消息处理程序
LRESULT CVEDlg::OnEncryptEnd(WPARAM wParam,LPARAM lParam)
	{
	UNREFERENCED_PARAMETER(wParam);
	UNREFERENCED_PARAMETER(lParam);

	if (_T( "" ) != m_strVEFile) {
		m_dlgCA.SetVE( m_strVEFile );
		m_dlgCA.SetCAFile( );
		}
	
	GetDlgItem(IDC_MFCBUTTON_SAVE)->EnableWindow(TRUE);
	return 1;
	}

LRESULT CVEDlg::OnSaveVE(WPARAM wParam,LPARAM lParam)
	{
	UNREFERENCED_PARAMETER(wParam);
	UNREFERENCED_PARAMETER(lParam);

	if (_T( "" ) != m_strVEFile) {
		m_dlgFile.SaveInfo( m_strVEFile );
		m_dlgInfo.SaveInfo( m_strVEFile );
		m_dlgLimit.SaveInfo( m_strVEFile );
		m_dlgAuth.SaveInfo( m_strVEFile );
		}

	return 0;
	}

LRESULT CVEDlg::OnNewVEFile(WPARAM wParam,LPARAM lParam)
	{
	
	UNREFERENCED_PARAMETER(lParam);

    if (1 != wParam) {
        m_strVEFile = m_dlgFile.GetVEFile( );
        }

    if (::PathFileExists( m_strVEFile )) {
        m_dlgFile.GetInfo( m_strVEFile );
        m_dlgInfo.GetInfo( m_strVEFile );
        m_dlgLimit.GetInfo( m_strVEFile );
        m_dlgAuth.GetInfo( m_strVEFile );
        m_dlgCA.SetVE( m_strVEFile );
        m_dlgCA.SetCAFile( );
        }
    else  {
        m_dlgFile.SaveInfo( m_strVEFile );
        m_dlgInfo.SaveInfo( m_strVEFile );
        m_dlgLimit.SaveInfo( m_strVEFile );
        m_dlgAuth.SaveInfo( m_strVEFile );
        }

	m_bIsOpen = TRUE;
	m_butSave.EnableWindow( TRUE );
	return 0;
	}

void CVEDlg::ve(bool init)
	{
	VMP_GUARD( "CVEDlg-ve" );
    bool hasFile( false );
    bool valid( false );
	HALVault_Initialize( APP_ID , KEY_URL );
#ifdef _DEBUG
	//HALVault_VerificationFile( L"6EDB983A650E0242DF089F6FDF7AE013" );
#else
	HALVault_VerificationFile( L"E7FBD4507D98AD27A0CD97FC8E9C450F" );
#endif
    m_bFun = ( 0 == VaultManager::CheckLicense( hasFile , valid ) );

    if (!m_bFun || !hasFile || !valid) {
        StringCchCopy( g_szAppName ,
            _countof( g_szAppName ) ,
            StrTools::AES_DecryptFromHex( g_szGlobal [ 0 ] , APP_ID ).c_str( ) );
        m_btnBuy.ShowWindow( SW_SHOW );
        }
    else {
        StringCchCopy( g_szAppName ,
            _countof( g_szAppName ) ,
            StrTools::AES_DecryptFromHex( g_szGlobal [ 1 ] , APP_ID ).c_str( ) );

        m_btnBuy.ShowWindow( SW_HIDE );

		if (!init) {
			MsgBox( IDS_STRING146 , m_hWnd , g_szAppName );
			}
        }

	if (!init) {
        m_dlgFile.PostMessage( UWM_VE , 0 , 0 );
        m_dlgAuth.PostMessage( UWM_VE , 0 , 0 );
        m_dlgInfo.PostMessage( UWM_VE , 0 , 0 );
        m_dlgCA.PostMessage( UWM_VE , 0 , 0 );
        m_dlgLimit.PostMessage( UWM_VE , 0 , 0 );
		}

    M_SetWindowText( g_szAppName );
        
	}

BOOL CVEDlg::OnInitDialog()
{
	SetCaptionNum( FALSE , TRUE , TRUE);
   
	CGPDlgSkinBase::OnInitDialog();


	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	SetIcon(m_hIcon, TRUE);			
	SetIcon(m_hIcon, FALSE);

	try
		{

		ve( );

        CRect rectBut;
        m_butSave.GetWindowRect( &rectBut );
        ScreenToClient( &rectBut );
        SetTopHeight( rectBut.bottom );
		
        CRect rect;
        m_tab.GetClientRect( &rect );
        rect.top += (LONG)(24 * GetPix());
        rect.left += 1;

		m_tab.InsertItem(0, _T("1 加密设置 →"));
		m_tab.InsertItem(1, _T("2 信息设置 →"));
		m_tab.InsertItem(2, _T("3 环境设置 →"));
		m_tab.InsertItem(3, _T("4 授权设置 →"));
		m_tab.InsertItem(4, _T("5 生成授权 ✓"));

		if (m_dlgInfo.Create( IDD_DIALOG_INFO , GetDlgItem( IDC_TAB1 ) )) {
			m_dlgInfo.SetVI( &m_vi );
			m_dlgFile.SetMainWnd( this );
			m_dlgInfo.MoveWindow( &rect );
			}

		if (m_dlgLimit.Create( IDD_DIALOG_LIMIT , GetDlgItem( IDC_TAB1 ) )) {
			m_dlgLimit.SetVI( &m_vi );
			m_dlgLimit.MoveWindow( &rect );
			}

		if (m_dlgAuth.Create( IDD_DIALOG_AUTH , GetDlgItem( IDC_TAB1 ) )) {
			m_dlgAuth.SetVI(&m_vi );
			m_dlgAuth.MoveWindow( &rect );
			}

		if (m_dlgFile.Create( IDD_DIALOG_FILE , GetDlgItem( IDC_TAB1 ) )) {
			m_dlgFile.SetVI(&m_vi );
			m_dlgFile.MoveWindow( &rect );
			}

		if (m_dlgCA.Create( IDD_DIALOG_CA , GetDlgItem( IDC_TAB1 ) )) {
			m_dlgCA.SetVI(&m_vi );
			m_dlgCA.MoveWindow( &rect );
			}

	
		m_tab.SetCurSel(0);
		m_dlgFile.ShowWindow( SW_SHOW );
		
		m_butNew.SetIconImage( IDB_PNG_NEW );
		m_butNew.SetTextColor( Color( 255 , 255 , 255 ) );
		m_butOpen.SetIconImage( IDB_PNG_OPEN );
		m_butOpen.SetTextColor( Color( 255 , 255 , 255 ) );
		m_butSave.SetIconImage( IDB_PNG_SAVE );
		m_butSave.SetTextColor( Color( 255 , 255 , 255 ) );
		m_btnHelp.SetTextColor( Color( 255 , 255 , 255 ) );
		m_btnHelp.SetIconImage( IDB_PNG_HELP );
        m_btnBuy.SetTextColor( Color( 255 , 255 , 255 ) );
        m_btnBuy.SetIconImage( IDB_PNG_BUY);

		
		m_butSave.EnableWindow( FALSE );
	
        

		RenewBK( );

		}
	catch (...)
		{
		}

	return TRUE;  
}

void CVEDlg::OnTcnSelchangeTab1(NMHDR *pNMHDR, LRESULT *pResult)
	{
	UNREFERENCED_PARAMETER(pNMHDR);
	*pResult = 0;
	static int iPrvDlg(0); //上次选中的选项卡

	if (_T( "" ) != m_strVEFile) {
		switch (iPrvDlg) {
				case 0:
					m_dlgFile.SaveInfo( m_strVEFile );
					break;
				case 1:
					m_dlgInfo.SaveInfo( m_strVEFile );
					break;
				case 2:
					m_dlgLimit.SaveInfo( m_strVEFile );
					break;
				case 3:
					m_dlgAuth.SaveInfo( m_strVEFile );
					break;
			}

		m_dlgCA.SetVE( m_strVEFile );
		}

	

	iPrvDlg = m_tab.GetCurSel();

	m_dlgFile.ShowWindow(0 == iPrvDlg ? SW_SHOW:SW_HIDE);
	m_dlgInfo.ShowWindow(1 == iPrvDlg ? SW_SHOW:SW_HIDE);
	m_dlgLimit.ShowWindow(2 == iPrvDlg ? SW_SHOW:SW_HIDE);
	m_dlgAuth.ShowWindow(3== iPrvDlg ? SW_SHOW:SW_HIDE);
	m_dlgCA.ShowWindow(4 == iPrvDlg ? SW_SHOW:SW_HIDE);


	if (4 == iPrvDlg) {
		m_dlgCA.SetCAFile( );
		}
	}


void CVEDlg::OnBnClickedMfcbuttonOpen()
	{
    CFileDialog fileDlg( TRUE , 0 , 0 , OFN_FILEMUSTEXIST , _T( "VE Files (*.ve)|*.VE||" ) , this );

    if (fileDlg.DoModal( ) == IDOK) {
        CString strSelPath = fileDlg.GetPathName( );

        if (strSelPath.GetLength( ) != 0) {
            m_strVEFile = strSelPath;
            SendMessage( UWM_NEW_VEFILE , 1 );
            }

        m_bIsOpen = TRUE;
        }
	}


void CVEDlg::OnBnClickedMfcbuttonSave()
	{
    if (_T( "" ) == m_strVEFile) {
        CFileDialog fileDlg( FALSE , 0 , 0 , OFN_FILEMUSTEXIST , _T( "VE Files (*.ve)|*.VE||" ) , this );

        if (fileDlg.DoModal( ) == IDOK) {
            CString strSelPath = fileDlg.GetPathName( );

            if (strSelPath.GetLength( ) != 0) {
                if (_T( ".ve" ) != strSelPath.Right( 3 )
                    && _T( ".VE" ) != strSelPath.Right( 3 )) {
                    m_strVEFile = strSelPath + _T( ".ve" );
                    }
                }
            }
        }

    if (_T( "" ) != m_strVEFile) {
        m_dlgFile.SaveInfo( m_strVEFile );
        m_dlgInfo.SaveInfo( m_strVEFile );
        m_dlgLimit.SaveInfo( m_strVEFile );
        m_dlgAuth.SaveInfo( m_strVEFile );
        }
	}


void CVEDlg::OnBnClickedMfcbuttonNew()
	{
	m_strVEFile = _T("");
	m_dlgFile.NewInfo();
	m_dlgInfo.NewInfo();
	m_dlgLimit.NewInfo();
	m_dlgAuth.NewInfo();
	m_dlgCA.NewInfo();

	SetWindowText(APP_NAME);
	if (0 != m_tab.GetCurSel())
		{
		m_tab.SetCurSel(0);
		m_dlgFile.ShowWindow(SW_SHOW);
		m_dlgInfo.ShowWindow(SW_HIDE);
		m_dlgLimit.ShowWindow(SW_HIDE);
		m_dlgAuth.ShowWindow(SW_HIDE);
		m_dlgCA.ShowWindow(SW_HIDE);
		}

	m_butSave.EnableWindow( FALSE );
	}

void CVEDlg::OnBnClickedMfcbuttonBuy()
	{
    CDlgAct dlg;
    if (IDOK == dlg.DoModal( )) {
        ve( false);
        }
	}


void CVEDlg::OnPopNew()
	{
	OnBnClickedMfcbuttonNew();
	}


void CVEDlg::OnPopOpen()
	{
	OnBnClickedMfcbuttonOpen();
	}


void CVEDlg::OnPopSave()
	{
	OnBnClickedMfcbuttonSave();
	}


void CVEDlg::OnPopHelp()
	{
	OnBnClickedMfcbuttonHelp();
	}

void CVEDlg::OnPopAbout()
	{
	CAboutDlg dlg;
	dlg.DoModal( );

	if (dlg.GetRem( )) {
        ve( false);
		}
	}

void CVEDlg::OnPopExit()
	{
	OnCancel();
	}


void CVEDlg::OnBnClickedMfcbuttonHelp()
	{
	NetTools::OpenURLEx(_T("https://www.jiamisoft.com/video-encryption/help/"));
	}


void CVEDlg::OnSysCommand(UINT nID, LPARAM lParam)
	{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
		{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
		}
	else
		{
		CGPDlgSkinBase::OnSysCommand(nID, lParam);
		}
	}

void CVEDlg::OnPaint()
	{
	if (IsIconic())
		{
		CPaintDC dc(this); 

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
		}
	else
		{
		CGPDlgSkinBase::OnPaint();
		}
	}


HCURSOR CVEDlg::OnQueryDragIcon()
	{
	return static_cast<HCURSOR>(m_hIcon);
	}

BOOL CAboutDlg::OnInitDialog()
	{
	CGPDlgSkinBase::OnInitDialog();

    volatile int a = clock( );

	if (( a * a ) % 3 != 2) {
		bool hasFile( false );
		bool valid( false );
		bool bFun = ( 0 == VaultManager::CheckLicense( hasFile , valid ) );

		if (!bFun || !hasFile || !valid) {
			GetDlgItem( IDC_BUTTON_REM_KEY )->EnableWindow( FALSE );
			GetDlgItem( IDC_BUTTON_REM_KEY )->SetWindowText( L"未获得授权" );
			}
		else {
			GetDlgItem( IDC_BUTTON_REM_KEY )->EnableWindow(TRUE );
			}
		}

	m_csLogo.SetImage( IDR_MAINFRAME , L"Icon" );
	
	CString strName;
	strName.Format( _T("%s %s") , g_szAppName , APP_VER );
	m_gsName.SetWindowText( strName );
	SetWindowText( L"关于金钻视频加密专家" );
    return TRUE;  // return TRUE unless you set the focus to a control
	// 异常: OCX 属性页应返回 FALSE
	}

void CAboutDlg::OnBnClickedButtonRemKey()
	{
	CDlgRemoveKey dlg;
	if (IDOK == dlg.DoModal( )) {
        m_bRemKey = TRUE;
		GetDlgItem( IDC_BUTTON_REM_KEY )->SetWindowText( L"未获得授权" );
        GetDlgItem( IDC_BUTTON_REM_KEY )->EnableWindow( FALSE );
		}
	
	}
/*****************************************************************************
OnSkinMeun

Last modification time:2024/06/12 16:48:43
*****************************************************************************/
LRESULT CVEDlg::OnSkinMeun( WPARAM wParam , LPARAM lParam )
    {
    try {
        CMenu menu;
        CMenu* pSubMenu;
        if (!menu.LoadMenu( IDR_MENU1)) {
            return 0;
            }

        pSubMenu = menu.GetSubMenu( 0 );

        ::SetMenuDefaultItem( pSubMenu->m_hMenu , 0 , TRUE );

		if (m_bFun) {

			}

        // Display and track the popup menu
        CPoint pos;
        pos.x = wParam;
        pos.y = lParam;

        ClientToScreen( &pos );

        ::TrackPopupMenu( pSubMenu->m_hMenu ,
            0 ,
            pos.x ,
            pos.y ,
            0 ,
            this->GetSafeHwnd( ) ,
            NULL );

        menu.DestroyMenu( );
        }
    catch (...) {
        }
    return 1;
    }