﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="头文件\ControlLib">
      <UniqueIdentifier>{4dc7ab9c-0e21-4e8f-99aa-2451511401b6}</UniqueIdentifier>
    </Filter>
    <Filter Include="源文件\ControlLib">
      <UniqueIdentifier>{d9370f8a-1836-4620-9132-0e92557c9dae}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="VE.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="VEDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgInfo.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgLimit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgAuth.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgFile.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgCA.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgInfoPrv.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgBP.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgBuy.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgAct.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="MFCEditBrowseCtrlEx.h">
      <Filter>头文件\ControlLib</Filter>
    </ClInclude>
    <ClInclude Include="msg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="global.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CDlgRemoveKey.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ScopeGuard.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="VE.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="VEDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgInfo.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgLimit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgAuth.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgFile.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgCA.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgInfoPrv.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgBP.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgBuy.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgAct.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="MFCEditBrowseCtrlEx.cpp">
      <Filter>源文件\ControlLib</Filter>
    </ClCompile>
    <ClCompile Include="msg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="global.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CDlgRemoveKey.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="VE.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\VE.rc2">
      <Filter>资源文件</Filter>
    </None>
    <None Include="res\gpskinpn.bin">
      <Filter>资源文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\VE.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\new.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\open.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\save.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\info.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\buy_64.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\key_64.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\telephone_blue.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\buy.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\help.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\new.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\open.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\save.png">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>