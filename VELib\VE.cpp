#include "pch.h"
#include "VE.h"
#include "VideoProcessor.h"

CVE::CVE()
{
}

CVE::~CVE()
{
}

// DLL 导出函数实现
extern "C" {

    VE_API int InitializeVE()
    {
        return CVECommon::Initialize();
    }

    VE_API void CleanupVE()
    {
        CVECommon::Cleanup();
    }

    VE_API const wchar_t* GetErrorDescription(int error_code)
    {
        return CVECommon::GetErrorDescription(error_code);
    }

    VE_API  const wchar_t* GetFinalOuptPath( )
        {
        return CVECommon::GetFinalOuptPath( );
        }

    VE_API  const wchar_t* GetFileMD5( )
        {
        return CVECommon::GetFileMD5  ();
        }

    VE_API  const wchar_t* FileMD5( const wchar_t* filepath )
        {
        if (L"" == CVECommon::FileMD5( filepath )) {
            return L"";
            }

        return CVECommon::GetFileMD5( );
        }

    VE_API const wchar_t * GetLastErrorMessage()
    {
        return CVECommon::GetLastErrorMessage();
    }

    VE_API int EncryptVideo(EncryptParams* params)
    {
        if (!params)
        {
            CVECommon::m_strLastError = L"参数不能为空";
            return VE_ERROR_PARAMETER;
        }

        CVideoProcessor processor;
        int result = processor.EncryptVideoFile(params);
        if (result != VE_SUCCESS) {
            CVECommon::m_strLastError = processor.GetLastError( );
            }
        else {
            CVECommon::SetFinalOuptPath(processor.GetFinalOuptPath  ());
            }
        return result;
    }
#ifdef DECRYPT_VIDEO
    VE_API int DecryptVideo(EncryptParams* params)
    {
        if (!params)
        {
            CVECommon::m_strLastError = L"参数不能为空";
            return VE_ERROR_PARAMETER;
        }

        CVideoProcessor processor;
        int result = processor.DecryptVideoFile(params);
        if (result != VE_SUCCESS)
        {
            CVECommon::m_strLastError = processor.GetLastError();
        }
        return result;
    }
#endif
    VE_API int GetVideoFileInfo(const wchar_t* filepath, VideoFileInfo* info)
    {
        if (!filepath || !info)
        {
            CVECommon::m_strLastError = L"参数不能为空";
            return VE_ERROR_PARAMETER;
        }

        std::unique_ptr<CVideoProcessor> processor = std::make_unique<CVideoProcessor>();
        int result = processor->GetFileInfo(filepath, (VideoInfo*)info);
        if (result != VE_SUCCESS)
        {
            CVECommon::m_strLastError = processor->GetLastError();
        }
        
        // processor 会在函数结束时自动销毁
        return result;
    }

    VE_API int GetVideoInfo(const wchar_t* filepath, VideoInfo* info)
    {
        // VideoInfo和VideoFileInfo是同一个结构体的别名
        return GetVideoFileInfo(filepath, (VideoFileInfo*)info);
    }

    // C++类导出函数
    VE_API CVideoProcessor* CreateVideoProcessor()
    {
        return new CVideoProcessor();
    }

    VE_API void DestroyVideoProcessor(CVideoProcessor* processor)
    {
        if (processor)
            delete processor;
    }

    VE_API int VideoProcessor_EncryptVideo(CVideoProcessor* processor, EncryptParams* params)
    {
        if (!processor || !params)
            return VE_ERROR_PARAMETER;
        return processor->EncryptVideoFile(params);
    }
#ifdef DECRYPT_VIDEO
    VE_API int VideoProcessor_DecryptVideo(CVideoProcessor* processor, EncryptParams* params)
    {
        if (!processor || !params)
            return VE_ERROR_PARAMETER;
        return processor->DecryptVideoFile(params);
    }
#endif
    VE_API int VideoProcessor_GetFileInfo(CVideoProcessor* processor, const wchar_t* filepath, VideoInfo* info)
    {
        if (!processor || !filepath || !info)
            return VE_ERROR_PARAMETER;
        return processor->GetFileInfo(filepath, info);
    }

    VE_API int GetVideoKeyFrameCount(const wchar_t* filepath, int* keyFrameCount)
    {
        if (!filepath || !keyFrameCount)
        {
            CVECommon::m_strLastError = L"参数不能为空";
            return VE_ERROR_PARAMETER;
        }

        std::unique_ptr<CVideoProcessor> processor = std::make_unique<CVideoProcessor>();
        int result = processor->GetVideoKeyFrameCount(filepath, keyFrameCount);
        if (result != VE_SUCCESS)
        {
            CVECommon::m_strLastError = processor->GetLastError();
        }
        
        // processor 会在函数结束时自动销毁
        return result;
    }

    // 新增：与Sepration.cpp对应的错误内容获取函数
    VE_API const wchar_t* GetErrorContent(int error_code)
    {
        return CVECommon::GetErrorDescription(error_code);
    }

    // 新增：兼容Sepration.cpp的加密函数接口
    VE_API int EncryptVideoFile(EncryptParams* params)
    {
        return EncryptVideo(params);
    }

    // 新增：批处理支持
    VE_API int BatchEncryptFiles(const wchar_t** input_files, int file_count, 
                                const wchar_t* password, PROGRESS_CALLBACK callback, void* userdata)
    {
        if (!input_files || file_count <= 0 || !password)
        {
            CVECommon::m_strLastError = L"批处理参数错误";
            return VE_ERROR_PARAMETER;
        }

        int success_count = 0;
        for (int i = 0; i < file_count; i++)
        {
            EncryptParams params = { 0 };
            wcscpy_s(params.input_file, input_files[i]);
            wcscpy_s(params.password, password);
            params.progress_callback = callback;
            params.user_data = userdata;

            int result = EncryptVideo(&params);
            if (result == VE_SUCCESS)
                success_count++;
        }

        return success_count == file_count ? VE_SUCCESS : VE_ERROR_PARAMETER;
    }
}