﻿// CDlgRemoveKey.cpp : 实现文件
//

#include "stdafx.h"
#include "VE.h"
#include "CDlgRemoveKey.h"
#include "afxdialogex.h"
#include "msg.h"
#include <fileTools/fileTools.h>
#include <string/strTools.h>
#include <time/timeTools.h>

// CDlgRemoveKey 对话框

IMPLEMENT_DYNAMIC(CDlgRemoveKey, CGPDlgSkinBase )

CDlgRemoveKey::CDlgRemoveKey(CWnd* pParent /*=nullptr*/)
    : CGPDlgSkinBase(IDD_DIALOG_REMOVE_KEY, pParent)
    , m_iHttpCode(0)
{
}

CDlgRemoveKey::~CDlgRemoveKey()
{
}

void CDlgRemoveKey::DoDataExchange(CDataExchange* pDX)
{
    CGPDlgSkinBase::DoDataExchange(pDX);

    // 绑定控件
    DDX_Control(pDX, IDC_STATIC_LIC_TO,   m_gsContact);
    DDX_Control(pDX, IDC_STATIC_LIC_EXP,  m_gsExpire);
    DDX_Control(pDX, IDC_STATIC_LIC_CNT,  m_gsCnt);
    DDX_Control(pDX, IDC_STATIC_SERIAL,   m_gsSerial);
    DDX_Control(pDX, IDC_STATIC_HID,        m_gsHWID);
}

BEGIN_MESSAGE_MAP(CDlgRemoveKey, CGPDlgSkinBase )
    ON_BN_CLICKED(IDC_BUTTON_REM_KEY, &CDlgRemoveKey::OnBnClickedButtonRemove)
    ON_MESSAGE(UM_THREAD_END, &CDlgRemoveKey::OnThreadEnd)
END_MESSAGE_MAP()

//============================================================
BOOL CDlgRemoveKey::OnInitDialog()
{
    CGPDlgSkinBase::OnInitDialog();

    SetWindowText(L"查看授权信息");

    // 读取本地授权信息
    LicenseInfoC info{};
    VaultManager::GetLicenseInfoC(info);

    m_strContact = StrTools::StringToWString( info.contact ).c_str( );

    CString  strText;
    strText.Format  (L"授权所有人：%s", m_strContact.GetString  ());
    m_gsContact.SetWindowText(strText );

    m_strExpire = TimeTools::TimeToWString( info.expireUnix ).c_str( );
    strText.Format( L"授权到期时间：%s" , m_strExpire.GetString  () );
    m_gsExpire.SetWindowText(strText );

    m_strCnt.Format(L"授权数量：%d", info.quantity);
    m_gsCnt.SetWindowText( m_strCnt );

    // 序列号处理
    std::string serial = info.serial;          // 假设成员名为 serial
    m_strSerial = StrTools::StringToWString( serial ).c_str( ) ;

    // 脱敏：首段保留，后面段替换*
    std::string masked(serial);
    bool pastFirstDash = false;
    for (char& ch : masked)
    {
        if (pastFirstDash && ch != '-') ch = '*';
        if (ch == '-') pastFirstDash = true;
    }
     
     m_strSerialMasked.Format( L"授权码：%s" , StrTools::StringToWString( masked ).c_str( ));
     m_gsSerial.SetWindowText( m_strSerialMasked );
    // HWID
    wchar_t hwid[33] = { 0 };
    if (0 == VaultManager::GetHWID( hwid , 33 )) {
        m_strHWID = hwid;
        strText.Format( L"硬件ID：%s" , hwid );
        m_gsHWID.SetWindowText(strText);
        }

    return TRUE; // return TRUE unless you set the focus to a control
}

//============================================================
void CDlgRemoveKey::OnBnClickedButtonRemove()
{
    GetDlgItem(IDC_BUTTON_REM_KEY)->EnableWindow(FALSE);
    AfxBeginThread(WorkThread, this);
}

//============================================================
UINT CDlgRemoveKey::Work(UINT& step)
{
    step = 0;

    // 1. 删除本地授权文件
    DWORD dwError = FileTools::SecureDeleteFile(VaultManager::GetLicenstFilePath(), 1);
    if (ERROR_SUCCESS != dwError)
        return dwError;

    step++; // 已完成第一步

    // 2. 请求服务器删除记录
    uint8_t id = 0;
    SoftwareCatalog::NameToId(APP_NAME, id);

    std::string serial  = StrTools::WStringToString(m_strSerial.GetString());
    std::string contact = StrTools::WStringToString(m_strContact.GetString());
    std::string hwidHex = StrTools::WStringToString(m_strHWID.GetString());

    TCHAR szMsg[512] = { 0 };
    UINT er = VaultManager::RemoveOnline(
        KEY_URL,
        serial,
        hwidHex,
        szMsg,
        511,
        m_iHttpCode,
        true);

    step++;

   m_strErr = szMsg;

    return er;
}

//============================================================
UINT CDlgRemoveKey::WorkThread(LPVOID lParam)
{
    CDlgRemoveKey* pThis = reinterpret_cast<CDlgRemoveKey*>(lParam);
    ASSERT(pThis);
    try
    {
        UINT steps = 0;
        UINT ret   = pThis->Work(steps);
        pThis->PostMessage(UM_THREAD_END, ret, steps);
    }
    catch (...)
    {
    }
    return 0;
}

//============================================================
LRESULT CDlgRemoveKey::OnThreadEnd( WPARAM wParam , LPARAM lParam )
    {
    UINT result = static_cast< UINT >( wParam );
    UINT steps = static_cast< UINT >( lParam );

    GetDlgItem( IDC_BUTTON_REM_KEY )->EnableWindow( TRUE );

    if (result == 0 && steps == 2) {
        MsgBox( IDS_STRING142 , m_hWnd , g_szAppName ); // 使用已有成功提示字符串
        EndDialog( IDOK );
        }
    else {
        if (m_strErr.IsEmpty( ))
            MsgErrorBox( IDS_STRING143 , result , m_hWnd , g_szAppName ); // 没有服务器 msg，显示错误码
        else
            MsgTextBox( m_strErr , m_hWnd , g_szAppName );
        }
    return 0;
    }
