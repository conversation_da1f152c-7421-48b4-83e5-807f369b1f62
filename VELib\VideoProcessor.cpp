#include "pch.h"
#include "VideoProcessor.h"
#include "../EP-common/VECommon.h"
#include <sys/stat.h>
#include <algorithm>
#include <ctime>
#include <chrono>
#include <cctype>

// 全局关闭 FFmpeg 弃用 API 告警（MSVC C4996）
#pragma warning(disable : 4996)

// 新增：FFmpeg 声道布局头
extern "C" {
#include <libavutil/channel_layout.h>
#include <libavcodec/packet.h>
}

// 新增：统一复制流级 side-data 的工具函数（替代弃用 API）
static void CopyStreamSideData(const AVStream* in, AVStream* out)
{
    // AV_PKT_DATA_NB 为 side-data 枚举的计数（FFmpeg ≥5）
    #pragma warning(push)
    #pragma warning(disable : 4996)
    for (int t = 0; t < AV_PKT_DATA_NB; ++t)
    {
        size_t sz = 0;
        const uint8_t* src = av_stream_get_side_data(in, (AVPacketSideDataType)t, &sz);
        if (!src || sz == 0) continue;

        uint8_t* dst = av_stream_new_side_data(out, (AVPacketSideDataType)t, sz);
        if (dst)
            memcpy(dst, src, sz);
    }
    #pragma warning(pop)
}

// ---------------------- PImpl implementation ----------------------
class CVideoProcessor::Impl
{
public:
    // FFmpeg 相关
    AVFormatContext* m_pInputFormatCtx = nullptr;
    AVFormatContext* m_pOutputFormatCtx = nullptr;
    AVCodecContext* m_pVideoCodecCtx = nullptr;
    AVCodecContext* m_pAudioCodecCtx = nullptr;

    // 流索引
    int m_videoStreamIndex = -1;
    int m_audioStreamIndex = -1;

    // 加密相关
    std::unique_ptr<CVideoAESCommon> m_pVideoAES;
    VideoEncryptType m_videoEncryptType = VIDEO_ENCRYPT_PARTIAL;
    AudioEncryptType m_audioEncryptType = AUDIO_ENCRYPT_SIMPLE;

    // 进度和状态
    PROGRESS_CALLBACK m_progressCallback = nullptr;
    void* m_progressUserData = nullptr;
    std::wstring m_lastError;
    bool m_bInitialized = false;

    // 统计信息
    unsigned long m_totalPackets = 0;
    unsigned long m_processedPackets = 0;
    int64_t m_processedBytes = 0;
    int64_t m_totalFileSize = 0;
    int m_keyFrameCount = 0;

    std::wstring m_finalOutputPath;
    // 临时文件管理
    std::string m_tempFilePath;
    std::string m_originalExt;
    bool m_isDatToMp4 = false;

    Impl() : m_pVideoAES(std::make_unique<CVideoAESCommon>()) {}
};

// 宏映射旧成员名 -> PImpl 成员
#define m_pInputFormatCtx  m_impl->m_pInputFormatCtx
#define m_pOutputFormatCtx m_impl->m_pOutputFormatCtx
#define m_pVideoCodecCtx   m_impl->m_pVideoCodecCtx
#define m_pAudioCodecCtx   m_impl->m_pAudioCodecCtx
#define m_videoStreamIndex m_impl->m_videoStreamIndex
#define m_audioStreamIndex m_impl->m_audioStreamIndex
#define m_pVideoAES        m_impl->m_pVideoAES
#define m_videoEncryptType m_impl->m_videoEncryptType
#define m_audioEncryptType m_impl->m_audioEncryptType
#define m_progressCallback m_impl->m_progressCallback
#define m_progressUserData m_impl->m_progressUserData
#define m_lastError        m_impl->m_lastError
#define m_bInitialized     m_impl->m_bInitialized
#define m_totalPackets     m_impl->m_totalPackets
#define m_processedPackets m_impl->m_processedPackets
#define m_processedBytes   m_impl->m_processedBytes
#define m_totalFileSize    m_impl->m_totalFileSize
#define m_keyFrameCount    m_impl->m_keyFrameCount
#define m_finalOutputPath  m_impl->m_finalOutputPath
#define m_tempFilePath     m_impl->m_tempFilePath
#define m_originalExt      m_impl->m_originalExt
#define m_isDatToMp4       m_impl->m_isDatToMp4

CVideoProcessor::CVideoProcessor()
    : m_impl(std::make_unique<Impl>())
{
    InitializeFFmpeg();
}

CVideoProcessor::~CVideoProcessor()
{
    CleanupFFmpeg();
}

bool CVideoProcessor::InitializeFFmpeg()
{
    // FFmpeg 4.0+ 不再需要 av_register_all()
    // av_register_all(); // 移除过时的API调用
    avformat_network_init();
    m_bInitialized = true;
    return true;
}

void CVideoProcessor::CleanupFFmpeg()
{
    CloseFiles();
    avformat_network_deinit(); // 确保网络资源被正确释放
    m_bInitialized = false;
}

int CVideoProcessor::EncryptVideoFile(EncryptParams* params)
{
    if (!params || !m_bInitialized)
        return VE_ERROR_PARAMETER;

    // 参数验证
    if (!params->input_file || wcslen(params->input_file) == 0)
    {
        SetLastError(L"输入文件路径为空");
        return VE_ERROR_PARAMETER;
    }

    if (!params->password || wcslen(params->password) == 0)
    {
        SetLastError(L"密码不能为空");
        return VE_ERROR_PARAMETER;
    }

    // 将宽字符密码转换为多字节字符串
    char passwordA[256];
    int conv_result = WideCharToMultiByte(CP_UTF8, 0, params->password, -1, passwordA, sizeof(passwordA), NULL, NULL);
    if (conv_result == 0)
    {
        SetLastError(L"密码转换失败");
        return VE_ERROR_PARAMETER;
    }

    // 初始化加密器
    if (!m_pVideoAES->Initialize(passwordA))
    {
        SetLastError(L"初始化AES加密器失败");
        return VE_ERROR_ENCRYPTION_FAILED;
    }

    // 设置进度回调（支持新旧字段名）
    m_progressCallback = params->progress_callback ;
    m_progressUserData = params->user_data;

    return ProcessVideoEncryption(params);
}
#ifdef DECRYPT_VIDEO
int CVideoProcessor::DecryptVideoFile(EncryptParams* params)
{
    if (!params || !m_bInitialized)
        return VE_ERROR_PARAMETER;

    // 参数验证
    if (!params->input_file || wcslen(params->input_file) == 0)
    {
        SetLastError(L"输入文件路径为空");
        return VE_ERROR_PARAMETER;
    }

    if (!params->password || wcslen(params->password) == 0)
    {
        SetLastError(L"密码不能为空");
        return VE_ERROR_PARAMETER;
    }

    // 将宽字符密码转换为多字节字符串
    char passwordA[256];
    int conv_result = WideCharToMultiByte(CP_UTF8, 0, params->password, -1, passwordA, sizeof(passwordA), NULL, NULL);
    if (conv_result == 0)
    {
        SetLastError(L"密码转换失败");
        return VE_ERROR_PARAMETER;
    }

    // 初始化解密器
    if (!m_pVideoAES->Initialize(passwordA))
    {
        SetLastError(L"初始化AES解密器失败");
        return VE_ERROR_DECRYPTION_FAILED;
    }

    // 设置进度回调（支持新旧字段名）
    m_progressCallback = params->progress_callback ;
    m_progressUserData = params->user_data ;

    return ProcessVideoDecryption(params);
}
#endif
int CVideoProcessor::ProcessVideoEncryption(EncryptParams* params)
{
    if (!params || !params->input_file) {
        SetLastError(L"参数无效");
        return VE_ERROR_PARAMETER;
    }
    
    int result = VE_SUCCESS;
    
    // 转换宽字符路径为UTF-8 - 修复中文路径支持
    char input_fileA [ MAX_PATH * 3 ] = { 0 }; // UTF-8可能需要更多字节

    int conv_result = WideCharToMultiByte( CP_UTF8 , 0 , params->input_file , -1 , input_fileA , sizeof( input_fileA ) , NULL , NULL );
    if (conv_result == 0) {
        SetLastError( L"输入文件路径转换失败" );
        return VE_ERROR_PARAMETER;
        }
    
    char output_path [ MAX_PATH * 3 ] = { 0 };
    conv_result = WideCharToMultiByte( CP_UTF8 , 0 , params->output_file , -1 , output_path , sizeof( output_path ) , NULL , NULL );

    if (conv_result == 0) {
        SetLastError( L"文件输出路径转换失败" );
        return VE_ERROR_PARAMETER;
        }

    try
    {
        
        // 打开输入文件
        result = OpenInputFile(input_fileA);
        if (result != VE_SUCCESS) {
            return result;
        }

        // 重新获取文件大小（解决 _stat64 在 UTF-8/中文路径下失败的问题）
        if (m_pInputFormatCtx && m_pInputFormatCtx->pb)
        {
            int64_t avioSize = avio_size(m_pInputFormatCtx->pb);
            if (avioSize > 0)
                m_totalFileSize = avioSize;
        }

        // 确定加密类型 - 使用动态策略
        std::string ext = CVECommon::GetFileExtension(input_fileA);
        
        // 首先检查是否支持该格式
        if (!CVECommon::IsSupportedFormat(ext))
        {
            SetLastError(L"不支持的文件格式: " + std::wstring(ext.begin(), ext.end()));
            return VE_ERROR_FORMAT_NOT_SUPPORTED;
        }
        
        // 使用动态策略确定加密类型
        m_videoEncryptType = CVideoAESCommon::GetVideoEncryptTypeByFormat( ext );
        m_audioEncryptType = CVideoAESCommon::GetAudioEncryptTypeByFormat( ext );

        // 生成输出文件路径
        std::string output_FilePath = GenerateOutputPath( input_fileA , output_path, true );

        // 创建输出文件
        result = CreateOutputFile(output_FilePath.c_str(), nullptr);
        if (result != VE_SUCCESS)
            return result;

        // 统计总包数（用于进度显示）- 改进的进度计算
        m_totalPackets = 0;
        m_processedPackets = 0;
        
        // 检查文件大小，对大文件采用不同策略
        struct _stat64 file_stat;
        int64_t total_file_size = 0;
        if (_wstat64(params->input_file, &file_stat) == 0)
        {
            total_file_size = file_stat.st_size;
            
            // 判断是否为大文件
            if (CVECommon::IsLargeFile(total_file_size))
            {
                // 大文件：基于文件大小进行进度计算，而非包数量
                m_totalPackets = 100; // 使用百分比进度
                
                // 设置进度消息
                UpdateProgress(0, 100, L"开始处理大文件...");
            }
            else
            {
                // 小文件：估算包数量用于精确进度显示
                // 根据码率和时长估算包数量
                if (m_videoStreamIndex >= 0 && m_pInputFormatCtx->streams[m_videoStreamIndex])
                {
                    AVStream* video_stream = m_pInputFormatCtx->streams[m_videoStreamIndex];
                    if (video_stream->duration > 0 && video_stream->time_base.den > 0)
                    {
                        double duration = video_stream->duration * av_q2d(video_stream->time_base);
                        if (duration > 0)
                        {
                            // 基于帧率估算视频包数量
                            double fps = 25.0; // 默认帧率
                            if (video_stream->avg_frame_rate.num > 0 && video_stream->avg_frame_rate.den > 0)
                            {
                                fps = (double)video_stream->avg_frame_rate.num / video_stream->avg_frame_rate.den;
                            }
                            
                            // 估算视频包数量
                            int estimated_video_packets = (int)(duration * fps);
                            
                            // 如果有音频流，估算音频包数量
                            int estimated_audio_packets = 0;
                            if (m_audioStreamIndex >= 0)
                            {
                                // 音频包通常更频繁，假设音频包是视频包的3-5倍
                                estimated_audio_packets = estimated_video_packets * 4;
                            }
                            
                            m_totalPackets = estimated_video_packets + estimated_audio_packets;
                        }
                    }
                }
                
                // 如果无法通过流信息估算，使用文件大小估算
                if (m_totalPackets == 0)
                {
                    // 假设平均每个包32KB
                    m_totalPackets = static_cast<unsigned long>(total_file_size / (32 * 1024));
                    if (m_totalPackets == 0) m_totalPackets = 1000; // 最少1000个包的估算
                }
            }
        }
        else
        {
            // 如果无法获取文件信息，默认设置一个包数量用于进度显示
            m_totalPackets = 10000;
        }
        
        // 初始化进度计算变量
        m_processedBytes = 0;
        m_totalFileSize = total_file_size;
        
        // 重新定位到文件开始
        av_seek_frame(m_pInputFormatCtx, -1, 0, AVSEEK_FLAG_BACKWARD);

        // 验证输入流是否有效
        std::wstring streamError;
        if (!ValidateInputStreams(streamError))
        {
            // 尝试修复问题
            if (TryRepairInputStreams())
            {
                // 修复后再次验证
                if (!ValidateInputStreams(streamError))
                {
                    SetLastError(L"输入文件无效且无法修复: " + streamError);
                    return VE_ERROR_INVALID_INPUT;
                }
            }
            else
            {
                SetLastError(L"输入文件无效: " + streamError);
                return VE_ERROR_INVALID_INPUT;
            }
        }

        // 为写入文件头预分配必要的内存
        PrepareOutputMemory();
        
        // 根据输出格式处理
        const char* output_format_name = m_pOutputFormatCtx->oformat->name;
        // 根据输入格式处理（更可靠，基于内容而非扩展名）
        const char* input_format_name = m_pInputFormatCtx->iformat->name;
        
        // 对于MP4大文件，需要特殊处理
        AVDictionary* opts = nullptr;
        bool hasHEVC = false;
        bool isMP4Format = false;
        
        // 检查输入/输出格式是否为MP4或MOV
        if (input_format_name && (
            strcmp(input_format_name, "mp4") == 0 || 
            strcmp(input_format_name, "mov") == 0 ||
            strcmp(input_format_name, "m4v") == 0 ||
            strcmp(input_format_name, "mov,mp4,m4a,3gp,3g2,mj2") == 0)) // FFmpeg有时会返回这个复合名称
        {
            isMP4Format = true;
        }
        
        struct _stat64 file_stat_check;
        bool isLargeFile_check = false;
        if (_wstat64(params->input_file, &file_stat_check) == 0)
        {
            isLargeFile_check = (file_stat_check.st_size > 2LL * 1024LL * 1024LL * 1024LL); // 超过2GB为大文件
        }
        
        // 检查是否包含H.265编码
        for (unsigned int i = 0; i < m_pInputFormatCtx->nb_streams; i++)
        {
            if (m_pInputFormatCtx->streams[i]->codecpar->codec_id == AV_CODEC_ID_HEVC)
            {
                hasHEVC = true;
                break;
            }
        }
        
        // 格式特定处理
        if (output_format_name)
        {
            // MP4/MOV格式处理
            if (strcmp(output_format_name, "mp4") == 0 || strcmp(output_format_name, "mov") == 0)
            {
                // 统一使用分片模式，对大文件尤其重要
                av_dict_set(&opts, "movflags", "frag_keyframe+empty_moov", 0);

                if (isLargeFile_check)
                {
                    // 大型MP4文件写入优化：确保FFmpeg内部知道要处理大文件
                    // 这有助于muxer正确选择co64 atom
                    av_dict_set(&opts, "large_file", "1", 0); 
                }
                else
                {
                    // 小文件可以附加faststart，提升首帧启动速度
                    av_dict_set(&opts, "movflags", "frag_keyframe+empty_moov+faststart", 0);
                }
                
                // H.265/HEVC特殊处理
                if (hasHEVC)
                {
                    // 确保使用正确的HEVC标签
                    av_dict_set(&opts, "brand", "iso5", 0); // ISO Base Media File Format v5 (支持HEVC)
                    
                    // 分片模式下，可以设置分片时长
                    av_dict_set(&opts, "frag_duration", "5000000", 0); // 5秒一个分片
                }
            }
            // MKV格式处理
            else if (strcmp(output_format_name, "matroska") == 0 || strcmp(output_format_name, "webm") == 0)
            {
                if (isLargeFile_check)
                {
                    // MKV大文件优化
                    av_dict_set(&opts, "cluster_size_limit", "2M", 0);
                    av_dict_set(&opts, "cluster_time_limit", "5000", 0);
                }
            }
        }
        
        // 写入输出文件头
        int ret = avformat_write_header(m_pOutputFormatCtx, opts ? &opts : NULL);
        
        // 释放选项
        if (opts)
            av_dict_free(&opts);
        
        if (ret < 0)
        {
            SetLastError(std::wstring(L"写入输出文件失败 (") + CVECommon::GetAVErrorString(ret) + std::wstring(L")"));
            
            if (ret == AVERROR(ENOMEM))
                return VE_ERROR_MEMORY_ALLOCATION;
            else if (ret == AVERROR(EIO))
                return VE_ERROR_CREATE_OUTPUT_FILE;
            else
                return VE_ERROR_WRITE_HEADER;
        }

        // 使用异常安全的包处理函数
        result = ProcessPacketsWithErrorHandling(true);
        if (result != VE_SUCCESS) {
            return result;
        }

        // 写入文件尾
        av_write_trailer(m_pOutputFormatCtx);
        
        // 关闭文件以确保数据完全写入
        CloseFiles();
        
        UpdateProgress(m_totalPackets, m_totalPackets, L"加密完成，正在最终化文件...");

        // 最终化临时文件 - 参考Sepration.cpp的重命名逻辑
        int finalize_result = FinalizeTempFile ( m_tempFilePath, m_originalExt, m_isDatToMp4 );
        if ( finalize_result != VE_SUCCESS ) {
            // 最终化失败，清理临时文件
            CVECommon::CleanupTempFile ( m_tempFilePath );
            return finalize_result;
            }

        // 删除源文件（如果需要）
        if ( params->delete_source ) {
            if ( _wremove ( params->input_file ) != 0 ) {
                SetLastError ( L"删除源文件失败" );
                // 不返回错误，因为加密已经成功
                }
            }

        UpdateProgress( m_totalPackets , m_totalPackets , L"加密完成，正在做文件校验..." );

        if (L"" == CVECommon::SmartFileVerify( m_finalOutputPath )) {
            UpdateProgress( m_totalPackets , m_totalPackets , L"文件校验失败" );
            }
    }
    catch (const std::exception& e)
    {
        // 异常清理工作
        CVECommon::CleanupTempFile(m_tempFilePath);
        CloseFiles();
        SetLastError(L"加密过程中发生异常");
        return VE_ERROR_ENCRYPTION_FAILED;
    }
    catch (...)
    {
        // 异常清理工作
        CVECommon::CleanupTempFile(m_tempFilePath);
        CloseFiles();
        SetLastError(L"加密过程中发生未知异常");
        return VE_ERROR_ENCRYPTION_FAILED;
    }
    
    CloseFiles();
    return result;
}
#ifdef DECRYPT_VIDEO
int CVideoProcessor::ProcessVideoDecryption(EncryptParams* params)
{
    if (!params || !params->input_file) {
        SetLastError(L"参数无效");
        return VE_ERROR_PARAMETER;
    }
    
    int result = VE_SUCCESS;
    
    // 转换宽字符路径为UTF-8 - 修复中文路径支持
    char input_fileA[MAX_PATH * 3] = { 0 }; // UTF-8可能需要更多字节
    int conv_result = WideCharToMultiByte(CP_UTF8, 0, params->input_file, -1, input_fileA, sizeof(input_fileA), NULL, NULL);
    if (conv_result == 0)
    {
        SetLastError(L"输入文件路径转换失败");
        return VE_ERROR_PARAMETER;
    }
    
    char output_path [ MAX_PATH * 3 ] = { 0 }; // UTF-8可能需要更多字节
     conv_result = WideCharToMultiByte( CP_UTF8 , 0 , params->output_file , -1 , output_path , 
         sizeof( output_path ) , NULL , NULL );
    if (conv_result == 0) {
        SetLastError( L"文件输出路径转换失败" );
        return VE_ERROR_PARAMETER;
        }

    try
    {
        // 打开输入文件
        result = OpenInputFile(input_fileA);
        if (result != VE_SUCCESS)
            return result;

        // 重新获取文件大小（解决 _stat64 在 UTF-8/中文路径下失败的问题）
        if (m_pInputFormatCtx && m_pInputFormatCtx->pb)
        {
            int64_t avioSize = avio_size(m_pInputFormatCtx->pb);
            if (avioSize > 0)
                m_totalFileSize = avioSize;
        }

        // 确定解密类型 - 使用动态策略
        std::string ext = CVECommon::GetFileExtension(input_fileA);
        
        // 处理加密文件的扩展名
      if (ext.length() > 3 && ext.substr(0, 3) == ".ve")
        {
            ext = "." + ext.substr(3);  // 移除"ve"前缀
        }
        
        // 使用动态策略确定解密类型
        m_videoEncryptType = CVideoAESCommon::GetVideoEncryptTypeByFormat(ext);
        m_audioEncryptType = CVideoAESCommon::GetAudioEncryptTypeByFormat(ext);

        // 生成输出文件路径
        std::string output_FilePath = GenerateOutputPath( input_fileA , output_path, false );

        // 创建输出文件
        result = CreateOutputFile( output_FilePath.c_str(), nullptr);
        if (result != VE_SUCCESS)
            return result;

        // 统计总包数
        m_totalPackets = 0;
        m_processedPackets = 0;
        
        // 检查文件大小，对大文件采用不同策略
        struct _stat64 file_stat;
        if (_wstat64(params->input_file, &file_stat) == 0)
        {
            // 当文件大于2GB时，不进行完整的包计数，而是估算
            if (file_stat.st_size > 2LL * 1024LL * 1024LL * 1024LL) // 2GB
            {
                // 对大文件，估算包数而不是实际统计
                // 假设平均每个包大约32KB (视频质量和格式而定)
                m_totalPackets = static_cast<unsigned long>(file_stat.st_size / (32 * 1024));
                
                // 如果有视频流，获取视频流的信息来改进估算
                if (m_videoStreamIndex >= 0 && m_pInputFormatCtx->streams[m_videoStreamIndex])
                {
                    AVStream* video_stream = m_pInputFormatCtx->streams[m_videoStreamIndex];
                    if (video_stream->duration > 0 && video_stream->time_base.den > 0)
                    {
                        double duration = video_stream->duration * av_q2d(video_stream->time_base);
                        if (duration > 0)
                        {
                            // 估算每秒包数量并乘以持续时间
                            double fps = 30.0; // 假设的帧率
                            if (video_stream->avg_frame_rate.num > 0 && video_stream->avg_frame_rate.den > 0)
                            {
                                fps = av_q2d(video_stream->avg_frame_rate);
                            }
                            m_totalPackets = static_cast<unsigned long>(duration * fps * 1.5); // 1.5为包与帧的估算比例
                        }
                    }
                }
                
                // 确保预估的包数量不会太小
                if (m_totalPackets < 1000)
                    m_totalPackets = 1000;
            }
            else
            {
                // 对较小文件，正常统计包数量
                AVPacket* packet = av_packet_alloc();
                if (!packet)
                {
                    SetLastError(L"分配AVPacket失败");
                    return VE_ERROR_MEMORY_ALLOCATION;
                }
                
                while (av_read_frame(m_pInputFormatCtx, packet) >= 0)
                {
                    m_totalPackets++;
                    av_packet_unref(packet);
                }
                
                av_packet_free(&packet);
                
                // 重新定位到文件开始
                av_seek_frame(m_pInputFormatCtx, -1, 0, AVSEEK_FLAG_BACKWARD);
            }
        }
        else
        {
            // 如果无法获取文件信息，默认设置一个包数量用于进度显示
            m_totalPackets = 10000;
        }
        
        // 初始化进度计算变量
        m_processedBytes = 0;
        m_totalFileSize = file_stat.st_size;
        
        // 重新定位到文件开始
        av_seek_frame(m_pInputFormatCtx, -1, 0, AVSEEK_FLAG_BACKWARD);

        // 验证输入流是否有效
        std::wstring streamError;
        if (!ValidateInputStreams(streamError))
        {
            // 尝试修复问题
            if (TryRepairInputStreams())
            {
                // 修复后再次验证
                if (!ValidateInputStreams(streamError))
                {
                    SetLastError(L"输入文件无效且无法修复: " + streamError);
                    return VE_ERROR_INVALID_INPUT;
                }
            }
            else
            {
                SetLastError(L"输入文件无效: " + streamError);
                return VE_ERROR_INVALID_INPUT;
            }
        }

        // 为写入文件头预分配必要的内存
        PrepareOutputMemory();
        
        // 检查输出格式是否为MP4
        const char* format_name = m_pOutputFormatCtx->oformat->name;
        
        // 对于MP4大文件，需要特殊处理
        AVDictionary* opts = nullptr;
        bool hasHEVC = false;
        bool isLargeFile = false;
        if (_wstat64(params->input_file, &file_stat) == 0)
        {
            isLargeFile = (file_stat.st_size > 2LL * 1024LL * 1024LL * 1024LL); // 超过2GB为大文件
        }
        
        // 检查是否包含H.265编码
        for (unsigned int i = 0; i < m_pInputFormatCtx->nb_streams; i++)
        {
            if (m_pInputFormatCtx->streams[i]->codecpar->codec_id == AV_CODEC_ID_HEVC)
            {
                hasHEVC = true;
                break;
            }
        }
        
        if (format_name && (strcmp(format_name, "mp4") == 0 || strcmp(format_name, "mov") == 0))
        {
            // 统一使用分片模式
            av_dict_set(&opts, "movflags", "frag_keyframe+empty_moov", 0);

            // MP4/MOV格式优化
            if (isLargeFile)
            {
                // 大型MP4文件写入优化
                av_dict_set(&opts, "large_file", "1", 0); 
            }
            else
            {
                 av_dict_set(&opts, "movflags", "frag_keyframe+empty_moov+faststart", 0);
            }
            
            // H.265/HEVC特殊处理
            if (hasHEVC)
            {
                // 确保使用正确的HEVC标签
                av_dict_set(&opts, "brand", "iso5", 0); // ISO Base Media File Format v5 (支持HEVC)
                
                // 分片模式下可以设置分片时长
                av_dict_set(&opts, "frag_duration", "5000000", 0); // 5秒片段
            }
        }
        
        // 写入输出文件头
        int ret = avformat_write_header(m_pOutputFormatCtx, opts ? &opts : NULL);
        
        // 释放选项
        if (opts)
            av_dict_free(&opts);
        
        if (ret < 0)
        {
            SetLastError(std::wstring(L"写入输出文件失败 (") + CVECommon::GetAVErrorString(ret) + std::wstring(L")"));
            
            if (ret == AVERROR(ENOMEM))
                return VE_ERROR_MEMORY_ALLOCATION;
            else if (ret == AVERROR(EIO))
                return VE_ERROR_CREATE_OUTPUT_FILE;
            else
                return VE_ERROR_WRITE_HEADER;
        }

        // 使用异常安全的包处理函数
        result = ProcessPacketsWithErrorHandling(false);
        if (result != VE_SUCCESS) {
            return result;
        }

        // 写入文件尾
        av_write_trailer(m_pOutputFormatCtx);
        
        UpdateProgress(m_totalPackets, m_totalPackets, L"解密完成");

        // 删除源文件（如果需要）
        if (params->delete_source)
        {
            if (_wremove(params->input_file) != 0)
            {
                SetLastError(L"删除源文件失败");
                // 不返回错误，因为解密已经成功
            }
        }
    }
    catch (const std::exception& e)
    {
        // 异常清理工作
        CloseFiles();
        SetLastError(L"解密过程中发生异常");
        return VE_ERROR_DECRYPTION_FAILED;
    }
    catch (...)
    {
        // 异常清理工作
        CloseFiles();
        SetLastError(L"解密过程中发生未知异常");
        return VE_ERROR_DECRYPTION_FAILED;
    }
    
    CloseFiles();
    return result;
}
#endif
int CVideoProcessor::OpenInputFile(const char* filepath)
{
    if (!filepath)
        return VE_ERROR_PARAMETER;

    // 打开输入文件
    int ret = avformat_open_input(&m_pInputFormatCtx, filepath, nullptr, nullptr);
    if (ret != 0)
    {
        SetLastError(L"打开输入文件失败: " + CVECommon::GetAVErrorString(ret));
        return VE_ERROR_OPEN_INPUT_FILE;
    }

    // 查找流信息
    ret = avformat_find_stream_info(m_pInputFormatCtx, nullptr);
    if (ret < 0)
    {
        SetLastError(L"获取流信息失败: " + CVECommon::GetAVErrorString(ret));
        avformat_close_input(&m_pInputFormatCtx);
        return VE_ERROR_NO_STREAM_INFO;
    }

    // 查找视频流
    m_videoStreamIndex = av_find_best_stream(m_pInputFormatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
    if (m_videoStreamIndex < 0)
    {
        SetLastError(L"视频文件没有视频流");
        avformat_close_input(&m_pInputFormatCtx);
        return VE_ERROR_NO_VIDEO_STREAM;
    }

    // 查找音频流(可选)
    m_audioStreamIndex = av_find_best_stream(m_pInputFormatCtx, AVMEDIA_TYPE_AUDIO, -1, -1, nullptr, 0);

    return VE_SUCCESS;
}

int CVideoProcessor::CreateOutputFile(const char* filepath, const char* format)
{
    if (!filepath)
        return VE_ERROR_PARAMETER;

    // 使用提供的格式或让FFmpeg根据文件扩展名自动检测
    const char* output_format = format;
    std::string final_filepath(filepath);
    
    // 释放之前的输出上下文，防止内存泄漏
    if (m_pOutputFormatCtx)
    {
        if (!(m_pOutputFormatCtx->oformat->flags & AVFMT_NOFILE) && m_pOutputFormatCtx->pb)
        {
            avio_closep(&m_pOutputFormatCtx->pb);
        }
        avformat_free_context(m_pOutputFormatCtx);
        m_pOutputFormatCtx = nullptr;
    }
    
    int ret = avformat_alloc_output_context2(&m_pOutputFormatCtx, nullptr, output_format, final_filepath.c_str());
    if (ret < 0)
    {
        if (ret == AVERROR(ENOMEM))
        {
            SetLastError(L"创建输出上下文失败: 内存不足，请尝试关闭其他应用或重启系统");
            return VE_ERROR_MEMORY_ALLOCATION;
        }
        else
        {
            SetLastError(L"创建输出上下文失败 (" + CVECommon::GetAVErrorString(ret) + L")");
            return VE_ERROR_CREATE_OUTPUT_FILE;
        }
    }

    // 复制容器级别的元数据
    if (m_pInputFormatCtx->metadata)
    {
        av_dict_copy(&m_pOutputFormatCtx->metadata, m_pInputFormatCtx->metadata, 0);
    }

    // 复制章节信息
    if (m_pInputFormatCtx->nb_chapters > 0)
    {
        for (unsigned int i = 0; i < m_pInputFormatCtx->nb_chapters; i++)
        {
            AVChapter* in_chapter = m_pInputFormatCtx->chapters[i];
            AVChapter* out_chapter = (AVChapter*)av_mallocz(sizeof(AVChapter));
            
            if (!out_chapter)
            {
                SetLastError(L"无法分配章节内存");
                return VE_ERROR_MEMORY_ALLOCATION;
            }
            
            // 复制章节ID和时间信息
            out_chapter->id = in_chapter->id;
            out_chapter->time_base = in_chapter->time_base;
            out_chapter->start = in_chapter->start;
            out_chapter->end = in_chapter->end;
            
            // 复制章节元数据
            if (in_chapter->metadata)
            {
                av_dict_copy(&out_chapter->metadata, in_chapter->metadata, 0);
            }
            
            // 添加到输出上下文
            m_pOutputFormatCtx->chapters = (AVChapter**)av_realloc(m_pOutputFormatCtx->chapters, 
                                                                 (m_pOutputFormatCtx->nb_chapters + 1) * sizeof(AVChapter*));
            if (!m_pOutputFormatCtx->chapters)
            {
                av_freep(&out_chapter);
                SetLastError(L"无法重新分配章节数组内存");
                return VE_ERROR_MEMORY_ALLOCATION;
            }
            
            m_pOutputFormatCtx->chapters[m_pOutputFormatCtx->nb_chapters] = out_chapter;
            m_pOutputFormatCtx->nb_chapters++;
        }
    }

    // 复制多节目流信息
    if (m_pInputFormatCtx->nb_programs > 0)
    {
        for (unsigned int i = 0; i < m_pInputFormatCtx->nb_programs; i++)
        {
            AVProgram* in_program = m_pInputFormatCtx->programs[i];
            
            // 创建新的节目
            AVProgram* out_program = av_new_program(m_pOutputFormatCtx, in_program->id);
            if (!out_program)
            {
                SetLastError(L"无法创建新的节目");
                return VE_ERROR_MEMORY_ALLOCATION;
            }
            
            // 复制节目属性
            out_program->flags = in_program->flags;
            out_program->discard = in_program->discard;
            out_program->program_num = in_program->program_num;
            out_program->pmt_pid = in_program->pmt_pid;
            out_program->pcr_pid = in_program->pcr_pid;
            out_program->pmt_version = in_program->pmt_version;
            
            // 复制节目元数据
            if (in_program->metadata)
            {
                av_dict_copy(&out_program->metadata, in_program->metadata, 0);
            }
            
            // 复制节目中的流索引
            for (unsigned int j = 0; j < in_program->nb_stream_indexes; j++)
            {
                av_program_add_stream_index(m_pOutputFormatCtx, out_program->id, in_program->stream_index[j]);
            }
        }
    }

    // 复制流信息
    for (unsigned int i = 0; i < m_pInputFormatCtx->nb_streams; i++)
    {
        AVStream* input_stream = m_pInputFormatCtx->streams[i];
        AVStream* output_stream = avformat_new_stream(m_pOutputFormatCtx, nullptr);
        
        if (!output_stream)
        {
            SetLastError(L"创建输出流失败");
            return VE_ERROR_CREATE_OUTPUT_FILE;
        }

        // 使用新的API复制codecpar
        ret = avcodec_parameters_copy(output_stream->codecpar, input_stream->codecpar);
        if (ret < 0)
        {
            SetLastError(L"复制编解码器参数失败 (" + CVECommon::GetAVErrorString(ret) + L")");
            return VE_ERROR_CREATE_OUTPUT_FILE;
        }

        // 重要：清理编码标签 - 参考Sepration.cpp逻辑
        // 无论什么格式都清除codec_tag，避免格式冲突
        output_stream->codecpar->codec_tag = 0;
        
        // 添加参数清理和验证
        if (output_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
        {
            // 验证和修正extradata
            if (output_stream->codecpar->extradata_size > 0)
            {
                // 检查extradata的基本有效性
                bool extradata_valid = true;
                if (output_stream->codecpar->codec_id == AV_CODEC_ID_H264)
                {
                    // H.264的extradata应该以0x01开头（avcC格式）
                    if (output_stream->codecpar->extradata[0] != 0x01)
                    {
                        extradata_valid = false;
                    }
                }
                
                if (!extradata_valid)
                {
                    // 清除无效的extradata
                    av_freep(&output_stream->codecpar->extradata);
                    output_stream->codecpar->extradata_size = 0;
                }
            }
        }

        // 对H.263编码进行特殊处理
        if (output_stream->codecpar->codec_id == AV_CODEC_ID_H263)
        {
            // 加密时和解密时都需要清除可能导致兼容性问题的编码标签
            output_stream->codecpar->codec_tag = 0;
            
            // 确保时间基准设置正确
            if (input_stream->time_base.num > 0 && input_stream->time_base.den > 0)
            {
                output_stream->time_base = input_stream->time_base;
            }
            else
            {
                // 使用默认的时间基准
                output_stream->time_base = av_make_q(1, 25); // 25fps默认
            }
        }
        else
        {
                    // 复制流的重要属性以保持视频方向等元数据
        output_stream->time_base = input_stream->time_base;
        }
        
        output_stream->r_frame_rate = input_stream->r_frame_rate;
        output_stream->avg_frame_rate = input_stream->avg_frame_rate;
        output_stream->disposition = input_stream->disposition;
        
        // 重要：处理流duration - 参考Sepration.cpp逻辑
        if (input_stream->duration < 0 && m_pInputFormatCtx->duration != AV_NOPTS_VALUE)
        {
            // 如果流duration无效，使用容器duration
            output_stream->duration = m_pInputFormatCtx->duration;
        }
        else
        {
            output_stream->duration = input_stream->duration;
        }
        
        // 复制流的元数据字典
        if (input_stream->metadata)
        {
            av_dict_copy(&output_stream->metadata, input_stream->metadata, 0);
        }
        
        // 使用新 API 统一复制 side-data
        CopyStreamSideData(input_stream, output_stream);

        // 处理附件流（如封面图片等）
        if (input_stream->disposition & AV_DISPOSITION_ATTACHED_PIC)
        {
            // 分配数据包
            int ret_packet = av_packet_ref(&output_stream->attached_pic, &input_stream->attached_pic);
            if (ret_packet >= 0)
            {
                // 设置流索引
                output_stream->attached_pic.stream_index = output_stream->index;
            }
        }

        // 清除编码标签以避免格式冲突
        if (output_stream->codecpar->codec_tag == 0 && m_pOutputFormatCtx->oformat->flags & AVFMT_GLOBALHEADER)
        {
            // 新版本中不需要直接设置codec flags
        }
    }

    // 检查输入格式与输出格式的兼容性
    if (m_pInputFormatCtx && m_videoStreamIndex >= 0)
    {
        AVCodecID input_codec = m_pInputFormatCtx->streams[m_videoStreamIndex]->codecpar->codec_id;
        const char* output_format_name = m_pOutputFormatCtx->oformat->name;
        
        // 检查特定的不兼容组合
        if ((input_codec == AV_CODEC_ID_H263 || input_codec == AV_CODEC_ID_FLV1) &&
            (strcmp(output_format_name, "mp4") == 0 || strcmp(output_format_name, "mov") == 0))
        {
            // 只设置警告，但继续处理
            SetLastError(L"警告：当前编解码器与MP4/MOV格式可能不兼容，建议使用MKV格式");
            // 不返回错误，允许继续
        }
        
        // 检查其他可能的不兼容情况
        if (input_codec == AV_CODEC_ID_VP9 && strcmp(output_format_name, "mp4") == 0)
        {
            // 老版本的MP4容器可能不支持VP9
            SetLastError(L"警告：VP9编解码器可能不被某些MP4播放器支持");
        }
        else if ((input_codec == AV_CODEC_ID_AV1 || input_codec == AV_CODEC_ID_VP8) && 
                 strcmp(output_format_name, "avi") == 0)
        {
            SetLastError(L"警告：现代编解码器可能与AVI容器不兼容");
        }
    }

    if (!(m_pOutputFormatCtx->oformat->flags & AVFMT_NOFILE))
    {
        ret = avio_open(&m_pOutputFormatCtx->pb, final_filepath.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0)
        {
            SetLastError(L"打开输出文件失败: " + std::wstring(final_filepath.begin(), final_filepath.end()) + L" (" + CVECommon::GetAVErrorString(ret) + L")");
            return VE_ERROR_CREATE_OUTPUT_FILE;
        }
    }

    // 重要：验证输出元数据完整性
    if (!VerifyOutputMetadata())
    {
        SetLastError(L"输出文件元数据验证失败，可能导致播放问题");
        // 不返回错误，只是警告
    }

    return VE_SUCCESS;
}

void CVideoProcessor::CloseFiles()
{
    if (m_pInputFormatCtx)
    {
        avformat_close_input(&m_pInputFormatCtx);
        m_pInputFormatCtx = nullptr;
    }

    if (m_pOutputFormatCtx)
    {
        if (!(m_pOutputFormatCtx->oformat->flags & AVFMT_NOFILE))
        {
            avio_closep(&m_pOutputFormatCtx->pb);
        }
        avformat_free_context(m_pOutputFormatCtx);
        m_pOutputFormatCtx = nullptr;
    }

    m_pVideoCodecCtx = nullptr;
    m_pAudioCodecCtx = nullptr;
    m_videoStreamIndex = -1;
    m_audioStreamIndex = -1;
}

// 新增：异常安全的包处理循环
int CVideoProcessor::ProcessPacketsWithErrorHandling(bool encrypt)
{
    try {
        AVPacketWrapper packet_wrapper;
        if (!packet_wrapper.is_valid()) {
            SetLastError(L"分配AVPacket失败");
            return VE_ERROR_MEMORY_ALLOCATION;
        }
    
        // 重新定位到文件开始
        int seek_ret = av_seek_frame(m_pInputFormatCtx, -1, 0, AVSEEK_FLAG_BACKWARD);
        if (seek_ret < 0) {
            // 继续处理，即使定位失败
        }

        // 进度条范围固定 0-100，避免 32 位溢出
        UpdateProgress(0, 100, encrypt ? L"开始加密视频文件..." : L"开始解密视频文件...");
        
        // 异常安全的数据包处理循环
        int prevPercent = -1; // 记录上次上报的百分比，避免频繁刷新
        int read_result;
        while ((read_result = av_read_frame(m_pInputFormatCtx, packet_wrapper.get())) >= 0)
        {
            // ✅ av_read_frame成功后立即标记，确保异常安全
            packet_wrapper.mark_has_data();
            
            try {
                // 处理数据包
                if (!ProcessPacket(packet_wrapper.get(), encrypt))
                {
                    SetLastError(L"处理数据包失败");
                    return encrypt ? VE_ERROR_ENCRYPTION_FAILED : VE_ERROR_DECRYPTION_FAILED;
                }

                // 修复时间戳
                FixStreamTimestamps(packet_wrapper.get());

                // 记录当前包大小（写入后 FFmpeg 可能会清空 packet ->size）
                int pktSize = packet_wrapper.get()->size > 0 ? packet_wrapper.get()->size : 0;

                // 写入输出文件
                int ret = av_interleaved_write_frame(m_pOutputFormatCtx, packet_wrapper.get());
                if (ret < 0)
                {
                    SetLastError(std::wstring(L"写入输出文件失败 (") + CVECommon::GetAVErrorString(ret) + std::wstring(L")"));
                    return encrypt ? VE_ERROR_ENCRYPTION_FAILED : VE_ERROR_DECRYPTION_FAILED;
                }

                // 更新进度（在unref之前获取size）
                m_processedPackets++;
                m_processedBytes += pktSize;
                
                // ✅ 正常流程：手动释放数据引用，为下次循环做准备
                packet_wrapper.unref();
            }
            catch (...) {
                // ⚠️ 异常情况：packet_wrapper析构函数会自动调用unref释放数据
                throw; // 重新抛出异常，让上层处理
            }
            
            // 基于已处理字节的百分比统一刷新进度
            if (m_totalFileSize > 0)
            {
                int progress_percent = (int)((m_processedBytes * 100) / m_totalFileSize);
                if (progress_percent > 100) progress_percent = 100;

                // 每提升 1% 或到 100% 时刷新一次 UI
                if (progress_percent != prevPercent)
                {
                    prevPercent = progress_percent;
                    UpdateProgress(progress_percent, 100,
                        encrypt ? L"正在加密..." : L"正在解密...");
                }
            }
        }
        
        return VE_SUCCESS;
    }
    catch (const std::exception& e)
    {
        SetLastError(L"包处理过程中发生异常");
        return encrypt ? VE_ERROR_ENCRYPTION_FAILED : VE_ERROR_DECRYPTION_FAILED;
    }
    catch (...)
    {
        SetLastError(L"包处理过程中发生未知异常");
        return encrypt ? VE_ERROR_ENCRYPTION_FAILED : VE_ERROR_DECRYPTION_FAILED;
    }
}

bool CVideoProcessor::ProcessPacket(AVPacket* packet, bool encrypt)
{
    if (!packet) {
        return false;
    }

    // 处理数据包主体
    bool result = true;
    if (packet->stream_index == m_videoStreamIndex)
    {
        result = ProcessVideoPacket(packet, encrypt);
    }
    else if (packet->stream_index == m_audioStreamIndex)
    {
        result = ProcessAudioPacket(packet, encrypt);
    }

    // 注意：数据包侧数据已经通过av_interleaved_write_frame自动处理
    // 侧数据包含旋转矩阵、时间码、3D信息等不需要加密的元数据
    // 在FFmpeg的实现中，这些数据不会被修改，而是直接从输入传递到输出

    return result;
}

bool CVideoProcessor::ProcessVideoPacket(AVPacket* packet, bool encrypt)
{
    if (!packet || !packet->data || packet->size <= 0)
        return false;

    // 只处理数据包主体，不处理侧数据
    if (encrypt)
    {
        return m_pVideoAES->EncryptVideoData(packet->data, packet->size, m_videoEncryptType);
    }
    else
    {
        return m_pVideoAES->DecryptVideoData(packet->data, packet->size, m_videoEncryptType);
    }
}

bool CVideoProcessor::ProcessAudioPacket(AVPacket* packet, bool encrypt)
{
    if (!packet || !packet->data || packet->size <= 0)
        return false;

    // 只处理数据包主体，不处理侧数据
    if (encrypt)
    {
        m_pVideoAES->EncryptAudioData(packet->data, packet->size, m_audioEncryptType);
    }
    else
    {
        m_pVideoAES->DecryptAudioData(packet->data, packet->size, m_audioEncryptType);
    }

    return true;
}



std::string CVideoProcessor::GenerateOutputPath(const std::string& input_path,
    const std::string& output_path , 
    bool encrypt)
{
    // 提取路径和文件名
    size_t last_slash = input_path.find_last_of("/\\");
    std::string directory = output_path;
    
    // 检查output_path后面是否有反斜杠，如果没有就添加一个反斜杠
    if (!directory.empty() && directory.back() != '\\' && directory.back() != '/')
    {
        directory += "\\";
    }
    
    std::string filename = (last_slash != std::string::npos) ? input_path.substr(last_slash + 1) : input_path;
    
    // 提取文件扩展名
    size_t last_dot = filename.find_last_of(".");
    std::string basename = (last_dot != std::string::npos) ? filename.substr(0, last_dot) : filename;
    std::string extension = (last_dot != std::string::npos) ? filename.substr(last_dot) : "";
    
    // 转换扩展名为小写进行比较
    std::string ext_lower = extension;
    std::transform(ext_lower.begin(), ext_lower.end(), ext_lower.begin(), ::tolower);
    
    // 构建新的文件名
    std::string new_filename;
    if (encrypt) {
        // 加密文件: 使用临时文件逻辑 - 参考Sepration.cpp
        return GenerateTempOutputPath( directory , basename , extension );
        }
    
    // 解密文件:
    if (ext_lower.find( ".ve" ) != std::string::npos) {
        // 处理.ve*格式
        size_t ve_pos = ext_lower.find( ".ve" );
        if (ve_pos != std::string::npos) {
            std::string original_ext = extension.substr( ve_pos + 3 );
            new_filename = basename + "." + original_ext;
            }
        else {
            new_filename = basename + "_dec" + extension;
            }
        }
    else {
        // 如果不是加密文件，添加"_dec"后缀
        new_filename = basename + "_dec" + extension;
        }
    
    return directory + new_filename;
}

void CVideoProcessor::UpdateProgress(unsigned long current, unsigned long total, const std::wstring& message)
{
    if (m_progressCallback)
    {
        std::wstring wmsg(message.begin(), message.end());
        m_progressCallback(current, total, const_cast<LPTSTR>(wmsg.c_str()), m_progressUserData);
    }
}

void CVideoProcessor::SetProgressCallback(PROGRESS_CALLBACK callback, void* userdata)
{
    m_progressCallback = callback;
    m_progressUserData = userdata;
}

const wchar_t* CVideoProcessor::GetLastError() const
{
    return m_lastError.c_str();
}

int CVideoProcessor::GetFileInfo(const wchar_t* filepath, VideoInfo* info)
{
    if (!filepath || !info)
    {
        SetLastError(L"参数不能为空");
        return VE_ERROR_PARAMETER;
    }

    // 清零结构体
    memset(info, 0, sizeof(VideoInfo));

    // 转换文件路径
    int len = WideCharToMultiByte(CP_UTF8, 0, filepath, -1, nullptr, 0, nullptr, nullptr);
    std::string utf8_path(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, filepath, -1, &utf8_path[0], len, nullptr, nullptr);

    // 打开输入文件
    int ret = OpenInputFile(utf8_path.c_str());
    if (ret != VE_SUCCESS)
    {
        return ret;
    }

    try
    {
        // 获取文件大小 - 使用_stat64支持大文件
        struct _stat64 file_stat;
        if (_wstat64(filepath, &file_stat) == 0)
        {
            info->size = file_stat.st_size;
            
            // 转换时间格式 - 与Sepration.cpp保持一致的格式
            struct tm timeinfo;
            errno_t err = _localtime64_s(&timeinfo, &file_stat.st_mtime);
            if (err == 0)
            {
                swprintf_s(info->modify_time, sizeof(info->modify_time)/sizeof(wchar_t), 
                          L"%d-%d-%d %d:%d:%d", 
                          timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, 
                          timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);
            }
            
            err = _localtime64_s(&timeinfo, &file_stat.st_ctime);
            if (err == 0)
            {
                swprintf_s(info->create_time, sizeof(info->create_time)/sizeof(wchar_t), 
                          L"%d-%d-%d %d:%d:%d", 
                          timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, 
                          timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);
            }
        }

        // 获取视频信息
        if (m_videoStreamIndex >= 0)
        {
            AVStream* video_stream = m_pInputFormatCtx->streams[m_videoStreamIndex];
            AVCodecParameters* codecpar = video_stream->codecpar;

            info->width = codecpar->width;
            info->height = codecpar->height;
            info->bit_rate = static_cast<int>(codecpar->bit_rate);

            // 计算帧率
            if (video_stream->avg_frame_rate.den != 0)
            {
                info->frame_rate = video_stream->avg_frame_rate.num / video_stream->avg_frame_rate.den;
            }

            // 获取编解码器名称
            const AVCodec* codec = avcodec_find_decoder(codecpar->codec_id);
            if (codec)
            {
                MultiByteToWideChar(CP_UTF8, 0, codec->name, -1, info->codec, sizeof(info->codec)/sizeof(wchar_t));
            }
            
            // 默认不计算关键帧数量，将其设置为0
            info->key_frame_count = 0;
        }

        // 获取时长
        if (m_pInputFormatCtx->duration != AV_NOPTS_VALUE)
        {
            info->duration = m_pInputFormatCtx->duration / AV_TIME_BASE;
        }

        // 获取格式信息
        if (m_pInputFormatCtx->iformat && m_pInputFormatCtx->iformat->name)
        {
            MultiByteToWideChar(CP_UTF8, 0, m_pInputFormatCtx->iformat->name, -1, info->format, sizeof(info->format)/sizeof(wchar_t));
        }

        CloseFiles();
        return VE_SUCCESS;
    }
    catch (...)
    {
        CloseFiles();
        SetLastError(L"获取文件信息时发生异常");
        return VE_ERROR_PARAMETER;
    }
}

// 单独获取视频关键帧数量的函数
int CVideoProcessor::GetVideoKeyFrameCount(const wchar_t* filepath, int* keyFrameCount)
{
    if (!filepath || !keyFrameCount)
    {
        SetLastError(L"参数不能为空");
        return VE_ERROR_PARAMETER;
    }

    // 初始化
    *keyFrameCount = 0;

    // 转换文件路径
    int len = WideCharToMultiByte(CP_UTF8, 0, filepath, -1, nullptr, 0, nullptr, nullptr);
    std::string utf8_path(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, filepath, -1, &utf8_path[0], len, nullptr, nullptr);

    // 创建特别优化的选项
    AVDictionary* options = nullptr;
    av_dict_set(&options, "buffer_size", "262144", 0); // 增大缓冲区
    av_dict_set(&options, "analyzeduration", "10000000", 0); // 10秒分析时间
    av_dict_set(&options, "probesize", "5000000", 0); // 5MB探测大小
    
    // 临时打开文件，不使用成员变量，避免干扰其他操作
    AVFormatContext* tempFormatCtx = nullptr;
    int ret = avformat_open_input(&tempFormatCtx, utf8_path.c_str(), nullptr, &options);
    av_dict_free(&options);
    
    if (ret != 0)
    {
        SetLastError(L"打开输入文件失败: " + CVECommon::GetAVErrorString(ret));
        return VE_ERROR_OPEN_INPUT_FILE;
    }
    
    // 限制分析时间
    tempFormatCtx->max_analyze_duration = 5 * AV_TIME_BASE;
    
    // 仅查找视频流信息
    AVDictionary* streamOptions = nullptr;
    av_dict_set(&streamOptions, "video_find_stream_info", "1", 0);
    av_dict_set(&streamOptions, "audio_find_stream_info", "0", 0); // 不需要音频流信息
    
    ret = avformat_find_stream_info(tempFormatCtx, nullptr);
    av_dict_free(&streamOptions);
    
    if (ret < 0)
    {
        avformat_close_input(&tempFormatCtx);
        SetLastError(L"获取流信息失败: " + CVECommon::GetAVErrorString(ret));
        return VE_ERROR_NO_STREAM_INFO;
    }

    try
    {
        // 查找视频流
        int videoStreamIndex = av_find_best_stream(tempFormatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
        if (videoStreamIndex < 0)
        {
            avformat_close_input(&tempFormatCtx);
            SetLastError(L"找不到视频流");
            return VE_ERROR_NO_VIDEO_STREAM;
        }
        
        // 计算关键帧（采用优化的算法）
        *keyFrameCount = CountKeyFramesOptimized(tempFormatCtx, videoStreamIndex);
        
        avformat_close_input(&tempFormatCtx);
        return VE_SUCCESS;
    }
    catch (...)
    {
        if (tempFormatCtx) {
            avformat_close_input(&tempFormatCtx);
        }
        SetLastError(L"获取关键帧数量时发生异常");
        return VE_ERROR_PARAMETER;
    }
}

// 优化的关键帧计数函数，专为大文件设计
int CVideoProcessor::CountKeyFramesOptimized(AVFormatContext* formatCtx, int videoStreamIndex)
{
    if (!formatCtx || videoStreamIndex < 0)
        return 0;

    int keyFrameCount = 0;
    const int MAX_PACKETS_TO_READ = 100;  // 仅读取前100个包估算关键帧比例
    int packetsRead = 0;
    int totalSamplePoints = 10; // 在文件中取10个采样点
    int64_t fileSize = avio_size(formatCtx->pb);
    
    // 使用新的API创建数据包
    AVPacket* packet = av_packet_alloc();
    if (!packet) {
        return 0;
    }
    
    // 保存当前位置
    int64_t originalPos = avio_tell(formatCtx->pb);
    
    // 在文件的不同位置采样，避免只从文件开头读取
    for (int sample = 0; sample < totalSamplePoints; sample++) {
        // 计算采样位置
        int64_t targetPos = (fileSize * sample) / totalSamplePoints;
        
        // 对于大文件，使用AVSEEK_FLAG_BYTE可能更有效
        int seekResult = av_seek_frame(formatCtx, -1, targetPos, AVSEEK_FLAG_BYTE);
        if (seekResult < 0) {
            // 如果定位失败，尝试使用时间戳定位
            if (formatCtx->duration != AV_NOPTS_VALUE) {
                int64_t targetTs = (formatCtx->duration * sample) / totalSamplePoints;
                av_seek_frame(formatCtx, -1, targetTs, AVSEEK_FLAG_BACKWARD);
            }
        }
        
        // 清空所有流的解码器缓冲区
        avformat_flush(formatCtx);
        
        int localKeyFrames = 0;
        int localPacketsRead = 0;
        
        // 读取这个位置的一些包
        int packetsToReadAtThisPoint = MAX_PACKETS_TO_READ / totalSamplePoints;
        while (localPacketsRead < packetsToReadAtThisPoint && av_read_frame(formatCtx, packet) >= 0) {
            if (packet->stream_index == videoStreamIndex) {
                localPacketsRead++;
                packetsRead++;
                if (packet->flags & AV_PKT_FLAG_KEY) {
                    localKeyFrames++;
                    keyFrameCount++;
                }
            }
            av_packet_unref(packet);
        }
    }
    
    // 释放数据包
    av_packet_free(&packet);
    
    // 恢复原始位置
    avio_seek(formatCtx->pb, originalPos, SEEK_SET);
    avformat_flush(formatCtx);
    
    // 如果视频有时长，根据已读取的关键帧密度估算总关键帧数
    if (packetsRead > 0 && formatCtx->duration != AV_NOPTS_VALUE) {
        AVStream* videoStream = formatCtx->streams[videoStreamIndex];
        double frameRate = 0.0;
        
        // 尝试获取帧率
        if (videoStream->avg_frame_rate.den != 0) {
            frameRate = (double)videoStream->avg_frame_rate.num / videoStream->avg_frame_rate.den;
        } else if (videoStream->r_frame_rate.den != 0) {
            frameRate = (double)videoStream->r_frame_rate.num / videoStream->r_frame_rate.den;
        } else {
            frameRate = 25.0;  // 默认值
        }
        
        // 估算总帧数
        double duration = (double)formatCtx->duration / AV_TIME_BASE;
        double totalFrames = frameRate * duration;
        
        // 估算总关键帧数
        double keyFrameRatio = (double)keyFrameCount / packetsRead;
        // 使用安全转换，避免int64_t到int的潜在数据丢失
        double estimatedKeyFrames = keyFrameRatio * totalFrames;
        if (estimatedKeyFrames > INT_MAX) {
            return INT_MAX;
        }
        return static_cast<int>(estimatedKeyFrames);
    } else if (packetsRead > 0) {
        // 无法估算，返回基于采样的结果
        return keyFrameCount * 10; // 因为我们只采样了文件的一小部分
    }
    
    return keyFrameCount;
}

void CVideoProcessor::SetLastError(const std::wstring& error)
{
    m_lastError = error;
    
#ifdef _DEBUG
    // 在调试版本中输出错误信息到调试窗口
    std::wstring debugMsg = L"[VideoProcessor Error]: " + error + L"\n";
    OutputDebugStringW(debugMsg.c_str());
#endif
}



// 辅助函数：为输出格式预分配足够的内存
void CVideoProcessor::PrepareOutputMemory()
{
    if (!m_pOutputFormatCtx)
        return;
    
    // 对于MP4, MOV等容器格式，预先设置扩展参数的内存分配
    const char* format_name = m_pOutputFormatCtx->oformat->name;
    if (!format_name)
        return;
    
    // 处理各个流
    for (unsigned int i = 0; i < m_pOutputFormatCtx->nb_streams; i++)
    {
        AVStream* stream = m_pOutputFormatCtx->streams[i];
        if (stream && stream->codecpar)
        {
            // 对于视频流，确保各种视频相关参数已正确设置
            if (stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
            {
                // 特别处理H.264编码
                if (stream->codecpar->codec_id == AV_CODEC_ID_H264)
                {
                    // 设置h264相关参数
                    stream->codecpar->codec_tag = 0; // 让FFmpeg自动选择最佳标签
                    
                    // 处理extradata (SPS/PPS)
                    if (stream->codecpar->extradata_size > 0)
                    {
                        // 确保extradata正确对齐
                        int extra_size = stream->codecpar->extradata_size + AV_INPUT_BUFFER_PADDING_SIZE;
                        uint8_t* new_extra = (uint8_t*)av_mallocz(extra_size);
                        if (new_extra)
                        {
                            // 复制现有的扩展数据
                            memcpy(new_extra, stream->codecpar->extradata, stream->codecpar->extradata_size);
                            // 释放旧的数据
                            av_freep(&stream->codecpar->extradata);
                            // 设置新的数据
                            stream->codecpar->extradata = new_extra;
                            stream->codecpar->extradata_size = stream->codecpar->extradata_size;
                        }
                    }
                }
                // 特别处理H.265 (HEVC) 编码
                else if (stream->codecpar->codec_id == AV_CODEC_ID_HEVC)
                {
                    // 设置HEVC相关参数
                    stream->codecpar->codec_tag = 0; // 让FFmpeg自动选择最佳标签
                    
                    // 处理extradata (VPS/SPS/PPS)
                    if (stream->codecpar->extradata_size > 0)
                    {
                        // HEVC的extradata通常比H.264更大，确保有足够空间
                        int extra_size = stream->codecpar->extradata_size + AV_INPUT_BUFFER_PADDING_SIZE;
                        uint8_t* new_extra = (uint8_t*)av_mallocz(extra_size);
                        if (new_extra)
                        {
                            // 复制现有的扩展数据
                            memcpy(new_extra, stream->codecpar->extradata, stream->codecpar->extradata_size);
                            // 释放旧的数据
                            av_freep(&stream->codecpar->extradata);
                            // 设置新的数据
                            stream->codecpar->extradata = new_extra;
                            stream->codecpar->extradata_size = stream->codecpar->extradata_size;
                        }
                    }
                }
                
                // 检查并设置合理的比特率
                if (stream->codecpar->bit_rate <= 0)
                {
                    // 根据分辨率估算一个合理的比特率
                    int width = stream->codecpar->width;
                    int height = stream->codecpar->height;
                    if (width > 0 && height > 0)
                    {
                        // 高清视频采用更高的比特率估算
                        if (width >= 1280 || height >= 720)
                        {
                            // 高清视频平均4-8Mbps
                            stream->codecpar->bit_rate = 5000000; // 5Mbps
                        }
                        else
                        {
                            // 标清视频
                            stream->codecpar->bit_rate = 2000000; // 2Mbps
                        }
                    }
                }
            }
            
            // 对于音频流也做一些基本检查
            else if (stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO)
            {
                // 对AAC编码特殊处理
                if (stream->codecpar->codec_id == AV_CODEC_ID_AAC)
                {
                    // 确保正确标签
                    stream->codecpar->codec_tag = 0;
                }
            }
        }
    }
}

// 辅助函数：检查输入文件流的有效性并提供详细诊断
bool CVideoProcessor::ValidateInputStreams(std::wstring& errorDetail)
{
    if (!m_pInputFormatCtx)
    {
        errorDetail = L"输入格式上下文无效";
        return false;
    }

    if (m_pInputFormatCtx->nb_streams == 0)
    {
        errorDetail = L"输入文件没有任何媒体流";
        return false;
    }

    // 检查视频流
    if (m_videoStreamIndex >= 0 && m_videoStreamIndex < (int)m_pInputFormatCtx->nb_streams)
    {
        AVStream* video_stream = m_pInputFormatCtx->streams[m_videoStreamIndex];
        if (!video_stream)
        {
            errorDetail = L"视频流指针无效";
            return false;
        }

        // 检查视频编解码器参数
        if (!video_stream->codecpar)
        {
            errorDetail = L"视频流编解码器参数无效";
            return false;
        }

        // 检查视频编解码器是否受支持
        const AVCodec* codec = avcodec_find_decoder(video_stream->codecpar->codec_id);
        if (!codec)
        {
            wchar_t codec_name[64] = {0};
            swprintf_s(codec_name, L"%d", video_stream->codecpar->codec_id);
            errorDetail = L"视频编解码器不受支持，编解码器ID: " + std::wstring(codec_name);
            return false;
        }

        // 检查视频流参数是否有效
        if (video_stream->codecpar->width <= 0 || video_stream->codecpar->height <= 0)
        {
            errorDetail = std::wstring(L"视频流尺寸无效，宽: ") + std::to_wstring(video_stream->codecpar->width) +
                         std::wstring(L", 高: ") + std::to_wstring(video_stream->codecpar->height);
            return false;
        }

        // 检查时间基是否有效
        if (video_stream->time_base.num <= 0 || video_stream->time_base.den <= 0)
        {
            errorDetail = L"视频流时间基无效";
            return false;
        }
    }
    else if (m_videoStreamIndex >= 0)
    {
        errorDetail = std::wstring(L"视频流索引超出范围: ") + std::to_wstring(m_videoStreamIndex);
        return false;
    }

    // 检查音频流
    if (m_audioStreamIndex >= 0 && m_audioStreamIndex < (int)m_pInputFormatCtx->nb_streams)
    {
        AVStream* audio_stream = m_pInputFormatCtx->streams[m_audioStreamIndex];
        if (!audio_stream)
        {
            errorDetail = L"音频流指针无效";
            return false;
        }

        // 检查音频编解码器参数
        if (!audio_stream->codecpar)
        {
            errorDetail = L"音频流编解码器参数无效";
            return false;
        }

        // 检查音频编解码器是否受支持
        const AVCodec* codec = avcodec_find_decoder(audio_stream->codecpar->codec_id);
        if (!codec)
        {
            wchar_t codec_name[64] = {0};
            swprintf_s(codec_name, L"%d", audio_stream->codecpar->codec_id);
            errorDetail = L"音频编解码器不受支持，编解码器ID: " + std::wstring(codec_name);
            return false;
        }

        // 检查音频流基本参数
        if (audio_stream->codecpar->sample_rate <= 0)
        {
            errorDetail = std::wstring(L"音频流采样率无效: ") + std::to_wstring(audio_stream->codecpar->sample_rate);
            return false;
        }
    }
    else if (m_audioStreamIndex >= 0)
    {
        errorDetail = std::wstring(L"音频流索引超出范围: ") + std::to_wstring(m_audioStreamIndex);
        return false;
    }

    // 检查流索引相关
    if (m_videoStreamIndex < 0 && m_audioStreamIndex < 0)
    {
        errorDetail = L"文件中没有找到有效的视频或音频流";
        return false;
    }

    return true;
}

// 辅助函数：尝试修复输入流中的问题
bool CVideoProcessor::TryRepairInputStreams()
{
    if (!m_pInputFormatCtx || m_pInputFormatCtx->nb_streams == 0)
        return false;

    bool repaired = false;

    // 检查并修复视频流
    if (m_videoStreamIndex >= 0 && m_videoStreamIndex < (int)m_pInputFormatCtx->nb_streams)
    {
        AVStream* video_stream = m_pInputFormatCtx->streams[m_videoStreamIndex];
        if (video_stream && video_stream->codecpar)
        {
            // 检查并修复无效的时间基
            if (video_stream->time_base.num <= 0 || video_stream->time_base.den <= 0)
            {
                video_stream->time_base.num = 1;
                video_stream->time_base.den = 90000; // 常见的视频时间基
                repaired = true;
            }

            // 检查并修复无效的帧率
            if (video_stream->avg_frame_rate.num <= 0 || video_stream->avg_frame_rate.den <= 0)
            {
                video_stream->avg_frame_rate.num = 25;
                video_stream->avg_frame_rate.den = 1; // 默认25fps
                repaired = true;
            }
            
            if (video_stream->r_frame_rate.num <= 0 || video_stream->r_frame_rate.den <= 0)
            {
                video_stream->r_frame_rate.num = 25;
                video_stream->r_frame_rate.den = 1; // 默认25fps
                repaired = true;
            }

            // 检查并修复无效的比特率
            if (video_stream->codecpar->bit_rate <= 0)
            {
                // 根据分辨率估算比特率
                if (video_stream->codecpar->width > 0 && video_stream->codecpar->height > 0)
                {
                    int width = video_stream->codecpar->width;
                    int height = video_stream->codecpar->height;
                    video_stream->codecpar->bit_rate = width * height * 4; // 简单估算
                    repaired = true;
                }
            }

            // 检查并修复视频流的extradata
            if (video_stream->codecpar->extradata_size > 0 && video_stream->codecpar->extradata != nullptr)
            {
                // 针对特定编解码器进行专门检查
                if (video_stream->codecpar->codec_id == AV_CODEC_ID_H264)
                {
                    // H.264的extradata应该以0x01开头（avcC格式）
                    if (video_stream->codecpar->extradata[0] != 0x01)
                    {
                        // H.264格式的extradata无效，需要清除
                        av_freep(&video_stream->codecpar->extradata);
                        video_stream->codecpar->extradata_size = 0;
                        repaired = true;
                    }
                }
                else
                {
                    // 对其他编解码器的通用检查
                    bool invalid_extradata = false;
                    
                    // 简单检查extradata的前几个字节是否有效
                    for (int i = 0; i < video_stream->codecpar->extradata_size && i < 10; i++)
                    {
                        if (video_stream->codecpar->extradata[i] == 0xFF) // 假设0xFF是无效数据的一个指示
                        {
                            invalid_extradata = true;
                            break;
                        }
                    }
                    
                    if (invalid_extradata)
                    {
                        // 释放有问题的extradata
                        av_freep(&video_stream->codecpar->extradata);
                        video_stream->codecpar->extradata_size = 0;
                        repaired = true;
                    }
                }
            }
            
            // 修复可能的编解码器标签问题
            if (video_stream->codecpar->codec_tag != 0)
            {
                // 对于MP4/MOV格式，清除编解码器标签让FFmpeg自动选择
                const char* format_name = m_pInputFormatCtx->iformat->name;
                if (format_name && (strcmp(format_name, "mp4") == 0 || 
                                   strcmp(format_name, "mov") == 0 ||
                                   strcmp(format_name, "matroska") == 0 ||
                                   strcmp(format_name, "mkv") == 0))
                {
                    video_stream->codecpar->codec_tag = 0;
                    repaired = true;
                }
            }
        }
    }

    // 检查并修复音频流
    if (m_audioStreamIndex >= 0 && m_audioStreamIndex < (int)m_pInputFormatCtx->nb_streams)
    {
        AVStream* audio_stream = m_pInputFormatCtx->streams[m_audioStreamIndex];
        if (audio_stream && audio_stream->codecpar)
        {
            // 检查并修复无效的时间基
            if (audio_stream->time_base.num <= 0 || audio_stream->time_base.den <= 0)
            {
                audio_stream->time_base.num = 1;
                audio_stream->time_base.den = 48000; // 常见的音频时间基
                repaired = true;
            }

            // 检查并修复无效的采样率
            if (audio_stream->codecpar->sample_rate <= 0)
            {
                audio_stream->codecpar->sample_rate = 48000; // 默认采样率
                repaired = true;
            }

            // 检查并修复无效的声道数（新 API 使用 ch_layout）
            if (audio_stream->codecpar->ch_layout.nb_channels <= 0)
            {
                av_channel_layout_default(&audio_stream->codecpar->ch_layout, 2); // 立体声
                repaired = true;
            }

            // 检查并修复音频流的extradata
            if (audio_stream->codecpar->extradata_size > 0 && audio_stream->codecpar->extradata != nullptr)
            {
                // 类似视频流的处理
                bool invalid_extradata = false;
                for (int i = 0; i < audio_stream->codecpar->extradata_size && i < 10; i++)
                {
                    if (audio_stream->codecpar->extradata[i] == 0xFF)
                    {
                        invalid_extradata = true;
                        break;
                    }
                }
                
                if (invalid_extradata)
                {
                    av_freep(&audio_stream->codecpar->extradata);
                    audio_stream->codecpar->extradata_size = 0;
                    repaired = true;
                }
            }
        }
    }

    return repaired;
}

// 辅助函数：根据编解码器选择兼容的输出格式
const char* CVideoProcessor::GetCompatibleOutputFormat(AVCodecID codec_id)
{
    // 默认格式
    const char* compatible_format = nullptr;  // 返回nullptr表示使用原始扩展名
    
    // 根据编解码器推荐合适的容器格式
    switch (codec_id)
    {
    case AV_CODEC_ID_H264:
    case AV_CODEC_ID_HEVC:
    case AV_CODEC_ID_AAC:
        compatible_format = "mp4";  // 这些编解码器最适合MP4
        break;
        
    case AV_CODEC_ID_H263:
    case AV_CODEC_ID_FLV1:
    case AV_CODEC_ID_VP8:
    case AV_CODEC_ID_VP9:
    case AV_CODEC_ID_AV1:
    case AV_CODEC_ID_OPUS:
    case AV_CODEC_ID_VORBIS:
        compatible_format = "matroska";  // 这些编解码器最适合MKV
        break;
        
    case AV_CODEC_ID_MPEG4:
    case AV_CODEC_ID_MPEG2VIDEO:
    case AV_CODEC_ID_MP3:
        // 这些格式MP4和MKV都可以，但MP4兼容性更好
        compatible_format = "mp4";
        break;
        
    case AV_CODEC_ID_MJPEG:
    case AV_CODEC_ID_PCM_S16LE:
        compatible_format = "avi";  // 这些老编解码器适合AVI
        break;
        
    default:
        // 如果是未知编解码器，使用最通用的MKV格式
        compatible_format = "matroska";
        break;
    }
    
    return compatible_format;
}


// 新增：根据编解码器选择最优加密类型
VideoEncryptType CVideoProcessor::GetOptimalEncryptType(AVCodecID codec_id)
{
    switch (codec_id) {
    case AV_CODEC_ID_H264:
        return VIDEO_ENCRYPT_KEYFRAME; // H.264适合关键帧加密
    case AV_CODEC_ID_HEVC:  // H.265/HEVC是同一个codec ID
        return VIDEO_ENCRYPT_KEYFRAME; // H.265适合关键帧加密
    case AV_CODEC_ID_MPEG2VIDEO:
        return VIDEO_ENCRYPT_PARTIAL;  // MPEG-2使用部分加密
    case AV_CODEC_ID_MPEG4:
        return VIDEO_ENCRYPT_PARTIAL;  // MPEG-4使用部分加密
    case AV_CODEC_ID_H263:
        return VIDEO_ENCRYPT_FULL;     // H.263使用完整加密（3GP常用）
    case AV_CODEC_ID_WMV3:
    case AV_CODEC_ID_VC1:
        return VIDEO_ENCRYPT_PARTIAL;  // WMV使用部分加密
    case AV_CODEC_ID_FLV1:
        return VIDEO_ENCRYPT_PARTIAL;  // FLV使用部分加密
    default:
        return VIDEO_ENCRYPT_PARTIAL;  // 默认使用部分加密
    }
}

// 新增：文件重命名逻辑 - 参考Sepration.cpp
int CVideoProcessor::RenameOutputFile(const std::string& temp_path, const std::string& final_path)
{
    // 删除目标文件（如果存在）
    CVECommon::DeleteFileIfExists(final_path);
    
    // 将UTF-8路径转换为Unicode (UTF-16)，以支持中文路径
    wchar_t temp_path_w[MAX_PATH] = { 0 };
    wchar_t final_path_w[MAX_PATH] = { 0 };
    
    MultiByteToWideChar(CP_UTF8, 0, temp_path.c_str(), -1, temp_path_w, MAX_PATH);
    MultiByteToWideChar(CP_UTF8, 0, final_path.c_str(), -1, final_path_w, MAX_PATH);
    
    // 使用Windows API的MoveFileW函数替代rename函数，以支持中文路径
    if (!MoveFileW(temp_path_w, final_path_w))
    {
        DWORD error = ::GetLastError();
        SetLastError(std::wstring(L"文件重命名失败，错误代码: ") + std::to_wstring(error));
        return VE_ERROR_CREATE_OUTPUT_FILE;
    }
    
    return VE_SUCCESS;
}

// 新增：生成临时输出路径 - 参考Sepration.cpp
std::string CVideoProcessor::GenerateTempOutputPath(const std::string& directory, 
    const std::string& basename, 
    const std::string& extension)
{
    // 处理DAT文件特殊情况
    std::string target_ext = extension;
    
    std::string ext_lower = extension;
    std::transform(ext_lower.begin(), ext_lower.end(), ext_lower.begin(), ::tolower);
    
    if (ext_lower == ".dat")
    {
        target_ext = ".mp4";
        m_isDatToMp4 = true;
    }
    else
    {
        m_isDatToMp4 = false;
    }
    
    m_originalExt = extension;
    
    // 生成临时文件名，使用数字序号避免冲突（参考Sepration.cpp逻辑）
    for (int i = 0; i < 999; i++)
    {
        char temp_filename[512];
        sprintf_s(temp_filename, sizeof(temp_filename), "%s%03d%s", basename.c_str(), i, target_ext.c_str());
        
        std::string temp_path = directory + temp_filename;
        
        // 检查文件是否存在
        if (GetFileAttributesA(temp_path.c_str()) == INVALID_FILE_ATTRIBUTES)
        {
            // 文件不存在，可以使用这个路径
            m_tempFilePath = temp_path;
            return temp_path;
        }
    }
    
    // 如果999个都存在，使用时间戳
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    
    std::string fallback_path = directory + basename + "_" + std::to_string(timestamp) + target_ext;
    m_tempFilePath = fallback_path;
    return fallback_path;
}

// 新增：最终化临时文件 - 参考Sepration.cpp的重命名逻辑
int CVideoProcessor::FinalizeTempFile(const std::string& temp_path, 
    const std::string& original_ext, 
    bool is_dat_to_mp4)
{
    if (temp_path.empty( )) {
        SetLastError( L"临时文件路径为空" );
        return VE_ERROR_PARAMETER;
        }
    
    // 生成最终文件名 - 参考Sepration.cpp逻辑
    std::string final_path;
    
    // 找到文件名和路径分离点
    size_t last_slash = temp_path.find_last_of("/\\");
    std::string directory = (last_slash != std::string::npos) ? temp_path.substr(0, last_slash + 1) : "";
    std::string temp_filename = (last_slash != std::string::npos) ? temp_path.substr(last_slash + 1) : temp_path;
    
    // 找到扩展名
    size_t last_dot = temp_filename.find_last_of(".");
    std::string basename_with_num = (last_dot != std::string::npos) ? temp_filename.substr(0, last_dot) : temp_filename;
    std::string current_ext = (last_dot != std::string::npos) ? temp_filename.substr(last_dot) : "";
    
    // 移除三位数字编号 - 参考Sepration.cpp: nlen - nlenEx - 3
   if (basename_with_num.length( ) >= 3) {
        std::string last_three = basename_with_num.substr( basename_with_num.length( ) - 3 );
        bool is_numeric = true;
        for (char c : last_three) {
            if (!std::isdigit( c )) {
                is_numeric = false;
                break;
                }
            }

        if (is_numeric) {
            basename_with_num = basename_with_num.substr( 0 , basename_with_num.length( ) - 3 );
            }
        }
    
    // 生成最终文件名
    if (is_dat_to_mp4 && current_ext == ".mp4") {
        // DAT转MP4的情况，在原始扩展名前加"ve"
        std::string ve_ext = original_ext;
        if (ve_ext.length( ) > 1 && ve_ext [ 0 ] == '.') {
            ve_ext = ".ve" + ve_ext.substr( 1 );
            }
        final_path = directory + basename_with_num + ve_ext;
        }
    else {
        // 其他情况，在扩展名前加"ve"
        std::string ve_ext = current_ext;
        if (ve_ext.length( ) > 1 && ve_ext [ 0 ] == '.') {
            ve_ext = ".ve" + ve_ext.substr( 1 );
            }
        final_path = directory + basename_with_num + ve_ext;
        }
    
    // 将UTF-8路径转换为Unicode (UTF-16)，以支持中文路径
    wchar_t temp_path_w[MAX_PATH] = { 0 };
    wchar_t final_path_w[MAX_PATH] = { 0 };
    
    MultiByteToWideChar(CP_UTF8, 0, temp_path.c_str(), -1, temp_path_w, MAX_PATH);
    MultiByteToWideChar(CP_UTF8, 0, final_path.c_str(), -1, final_path_w, MAX_PATH);
    
    // 使用Windows API的MoveFileW函数替代rename函数，以支持中文路径
    if (!MoveFileW( temp_path_w , final_path_w )) {
        DWORD error = ::GetLastError( );
        SetLastError( std::wstring( L"文件重命名失败: " ) + final_path_w +
            std::wstring( L", 错误代码: " ) + std::to_wstring( error ) );
        return VE_ERROR_CREATE_OUTPUT_FILE;
        }
    
    m_finalOutputPath = final_path_w;

    return VE_SUCCESS;
}



// 新增：验证流元数据
bool CVideoProcessor::ValidateStreamMetadata(int stream_index)
{
    if (!m_pInputFormatCtx || !m_pOutputFormatCtx)
        return false;
        
    if (stream_index < 0 || stream_index >= (int)m_pInputFormatCtx->nb_streams ||
        stream_index >= (int)m_pOutputFormatCtx->nb_streams)
        return false;
        
    AVStream* in_stream = m_pInputFormatCtx->streams[stream_index];
    AVStream* out_stream = m_pOutputFormatCtx->streams[stream_index];
    
    // 验证关键参数是否正确复制
    if (in_stream->codecpar->codec_type != out_stream->codecpar->codec_type)
        return false;
        
    if (in_stream->codecpar->codec_id != out_stream->codecpar->codec_id)
        return false;
        
    // 验证时间基准是否合理
    if (out_stream->time_base.den == 0)
        return false;
        
    return true;
}

// 新增：修复流时间戳
void CVideoProcessor::FixStreamTimestamps(AVPacket* packet)
{
    if (!packet || !m_pInputFormatCtx || !m_pOutputFormatCtx)
        return;
        
    if (packet->stream_index < 0 || 
        packet->stream_index >= (int)m_pInputFormatCtx->nb_streams ||
        packet->stream_index >= (int)m_pOutputFormatCtx->nb_streams)
        return;
        
    AVStream* in_stream = m_pInputFormatCtx->streams[packet->stream_index];
    AVStream* out_stream = m_pOutputFormatCtx->streams[packet->stream_index];
    
    // 重新计算时间戳 - 参考Sepration.cpp逻辑
    if (packet->pts != AV_NOPTS_VALUE)
    {
        packet->pts = av_rescale_q_rnd(packet->pts, in_stream->time_base, out_stream->time_base, 
                                      (AVRounding)(AV_ROUND_NEAR_INF|AV_ROUND_PASS_MINMAX));
    }
    
    if (packet->dts != AV_NOPTS_VALUE)
    {
        packet->dts = av_rescale_q_rnd(packet->dts, in_stream->time_base, out_stream->time_base, 
                                      (AVRounding)(AV_ROUND_NEAR_INF|AV_ROUND_PASS_MINMAX));
    }
    
    if (packet->duration > 0)
    {
        packet->duration = av_rescale_q(packet->duration, in_stream->time_base, out_stream->time_base);
    }
    
    // 重置位置信息
    packet->pos = -1;
}

// 新增：验证输出元数据完整性
bool CVideoProcessor::VerifyOutputMetadata()
{
    if (!m_pInputFormatCtx || !m_pOutputFormatCtx)
        return false;
        
    // 验证流数量
    if (m_pInputFormatCtx->nb_streams != m_pOutputFormatCtx->nb_streams)
        return false;
        
    // 验证每个流的元数据
    for (unsigned int i = 0; i < m_pInputFormatCtx->nb_streams; i++)
    {
        if (!ValidateStreamMetadata(i))
            return false;
    }
    
    // 验证容器级别的元数据
    if (m_pInputFormatCtx->metadata && !m_pOutputFormatCtx->metadata)
    {
        // 警告：容器元数据可能丢失
        SetLastError(L"警告：容器元数据可能丢失");
    }
    
    return true;
}

std::wstring CVideoProcessor::GetFinalOuptPath( )
    {
    return m_finalOutputPath;
    }