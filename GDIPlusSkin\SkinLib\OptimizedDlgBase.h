#pragma once
#include "MemoryDCPool.h"
#include "DirtyRegionManager.h"
#include "FastImageRenderer.h"

// 优化后的对话框基类示例
class COptimizedDlgBase : public CDialogEx
{
public:
    COptimizedDlgBase(UINT nIDTemplate, CWnd* pParent = nullptr) 
        : CDialogEx(nIDTemplate, pParent) {}

protected:
    // 优化的背景绘制
    void DrawOptimizedBackground(CDC* pDC) {
        CRect rtClient;
        GetClientRect(&rtClient);
        
        // 使用DC池
        auto dcItem = CMemoryDCPool::Instance().AcquireDC(pDC, rtClient.Width(), rtClient.Height());
        
        // 检查是否需要重绘
        if (m_dirtyMgr.NeedsRedraw(rtClient)) {
            Gdiplus::Graphics graphics(dcItem->dc.GetSafeHdc());
            
            // 使用快速渲染器
            CFastImageRenderer::Instance().BeginBatchDraw(&graphics);
            
            // 绘制背景
            if (m_pBackground) {
                CFastImageRenderer::Instance().FastDrawImage(&graphics, L"dlg_background", 
                    Gdiplus::RectF(0, 0, rtClient.Width(), rtClient.Height()));
            }
            
            CFastImageRenderer::Instance().EndBatchDraw(&graphics);
            
            // 清除脏标记
            m_dirtyMgr.ClearDirty();
        }
        
        // 复制到屏幕
        pDC->BitBlt(0, 0, rtClient.Width(), rtClient.Height(), 
                    &dcItem->dc, 0, 0, SRCCOPY);
        
        // 归还DC
        CMemoryDCPool::Instance().ReleaseDC(dcItem);
    }

    // 优化的无效化
    void OptimizedInvalidate(const CRect* pRect = nullptr) {
        if (pRect) {
            m_dirtyMgr.InvalidateRect(*pRect);
            InvalidateRect(pRect, FALSE);
        } else {
            m_dirtyMgr.InvalidateAll();
            Invalidate(FALSE);
        }
    }

    // 预处理常用图片
    void PreprocessCommonImages() {
        // 在程序启动时调用
        struct ImageInfo {
            LPCTSTR path;
            LPCTSTR key;
        } images[] = {
            {L"FrameBack\\Background.png", L"dlg_background"},
            {L"Button\\n_lan_xian.png", L"button_normal"},
            {L"Button\\Progress_lan.png", L"progress_bg"},
            // ... 添加更多图片
        };
        
        for (const auto& img : images) {
            Gdiplus::Image* pImage = CGPImageInfo::Instance()->ImageFromFile(img.path);
            if (pImage) {
                CFastImageRenderer::Instance().PreprocessImage(pImage, img.key);
            }
        }
    }

    virtual BOOL OnInitDialog() override {
        CDialogEx::OnInitDialog();
        
        // 预处理图片
        PreprocessCommonImages();
        
        return TRUE;
    }

    afx_msg BOOL OnEraseBkgnd(CDC* pDC) {
        // 使用优化的背景绘制
        DrawOptimizedBackground(pDC);
        return TRUE;
    }

    DECLARE_MESSAGE_MAP()

private:
    CDirtyRegionManager m_dirtyMgr;
    Gdiplus::Image* m_pBackground = nullptr;
};

// 消息映射
BEGIN_MESSAGE_MAP(COptimizedDlgBase, CDialogEx)
    ON_WM_ERASEBKGND()
END_MESSAGE_MAP()
