#include "pch.h"
#include "CAppEx.h"
#include <tlhelp32.h>
#include <memory>  
#include <shlwapi.h>
#include <ShlObj.h>
#include <strsafe.h>

CAppEx::CAppEx( ) : m_iArgcs(0), m_dwPID(0), m_dwLastError(ERROR_SUCCESS), m_hr(S_OK)
    {
    LPWSTR *szArglist = CommandLineToArgvW( GetCommandLine( ), &m_iArgcs );

    if ( szArglist != NULL ) {
        for ( int i = 0; i < m_iArgcs; i++ ) {
            m_vArg.push_back( szArglist[i] );
            }
        LocalFree( szArglist );
        }

    GetAppPathAndFileName( );

    m_dwPID = GetCurrentProcessId( );

    m_hr = CoInitializeEx( NULL, COINIT_MULTITHREADED | COINIT_DISABLE_OLE1DDE );
    }
//================================================================
CAppEx::~CAppEx( )
    {
    if ( SUCCEEDED( m_hr ) ) {
        CoUninitialize( );
        }
    }
//
std::wstring CAppEx::GetComputerName( )
    {
    WCHAR computerName [ MAX_COMPUTERNAME_LENGTH + 1 ] = { 0 };
    DWORD size = MAX_COMPUTERNAME_LENGTH + 1;

    if ( ::GetComputerNameW( computerName, &size ) ) {
        return std::wstring( computerName );
        }
    else {
        return L"";
        }
    }
//================================================================
void CAppEx::GetAppPathAndFileName(void )
    {
    TCHAR szPath [ MAX_PATH ] = { 0 };
    if ( GetModuleFileName( NULL, szPath, MAX_PATH ) ) {
        m_wsAppFilePath = szPath;

        TCHAR *ptrEnd = wcsrchr( szPath, L'\\' );
        if ( ptrEnd != NULL ) {
            TCHAR *ptrName = ptrEnd;
            m_wsAppFileName = ++ptrName;

            *ptrEnd = '\0';
            m_wsAppPath = szPath;
            }
        }
    }
//================================================================
std::wstring CAppEx::GetFolderPathEx(const int & nFolder )
    {
    std::wstring  wsVar;
    TCHAR     szPath [ MAX_PATH ] = { 0 };

    if ( SUCCEEDED( SHGetFolderPath( NULL , nFolder , NULL , 
                                          0 , 
                                          szPath ) ) ) {
        wsVar = szPath;
        }
  

    return wsVar;
    }
//================================================================
// 
std::wstring CAppEx::GetUserDeskTopPath( )
    {
    if ( m_wsUserDeskTopPath.empty( )){
        m_wsUserDeskTopPath = GetFolderPathEx( CSIDL_DESKTOPDIRECTORY );
        }

    return m_wsUserDeskTopPath;
    }
//================================================================
// 
std::wstring CAppEx::GetUserPath( )
    {
    if ( m_wsUserPath.empty( ) ) {
        std::wstring wsPath = GetUserDeskTopPath( );
        TCHAR szPath [ MAX_PATH ] = { 0 };
        StringCchCopy( szPath , MAX_PATH , wsPath.c_str( ) );
        PathRemoveFileSpec(szPath );
        m_wsUserPath = szPath;
        }

    return m_wsUserPath;
    }
//================================================================
std::wstring CAppEx::GetPublicDocuments( )
    {
    if ( m_wsPublicDocuments.empty ( ) ) {
        m_wsPublicDocuments = GetFolderPathEx( CSIDL_COMMON_DOCUMENTS );
        }

    return m_wsPublicDocuments;
    }
//================================================================
std::wstring CAppEx::GetUserDocuments( )
    {
    if ( m_wsUserDocuments.empty( ) ) {
        m_wsUserDocuments = GetFolderPathEx( CSIDL_PERSONAL );
        }
   
    return m_wsUserDocuments;
    }
//================================================================
std::wstring CAppEx::GetPublicDataPath( )
    {
    if ( m_wsPublicDataPath.empty( ) ) {
        m_wsPublicDataPath = GetFolderPathEx( CSIDL_COMMON_APPDATA );
        }

    return m_wsPublicDataPath;
    }
//================================================================
std::wstring CAppEx::GetUserDataPath( )
    {
    if ( m_wsUserDataPath.empty( ) ) {
        m_wsUserDataPath = GetFolderPathEx( CSIDL_APPDATA );
        }

    return m_wsUserDataPath;
    }
//================================================================
std::wstring CAppEx::GetUserLocalDataPath( )
    {
    if ( m_wsUserLocalDataPath.empty( ) ) {
        m_wsUserLocalDataPath = GetFolderPathEx(CSIDL_LOCAL_APPDATA);
        }

    return m_wsUserLocalDataPath;
    }
//================================================================
std::wstring CAppEx::GetUserTempPath( )
    {
    if ( m_wsUserTempPath.empty( ) ) {
        TCHAR szPath [ MAX_PATH ] = { 0 };

        DWORD dwRet = ::GetTempPath( MAX_PATH , szPath );
        if ( dwRet > MAX_PATH || ( dwRet == 0 ) ) {
            m_wsUserTempPath = TEXT( "" );
            }
        else {
            m_wsUserTempPath = szPath;
            }
        }

    return m_wsUserTempPath;
    }
//=================================================================
std::wstring CAppEx::GetUserName( )
    {
    if ( m_wsUserName.empty( ) ) {
        TCHAR szUseName [ MAX_PATH ] = { 0 };
        DWORD dwSize( MAX_PATH );

        if ( ::GetUserName( szUseName, &dwSize ) ) {
            m_wsUserName = szUseName;
            }
        }

    return m_wsUserName;
    }
//===================================================================
std::wstring CAppEx::GetSystemPath( )
    {
    if ( m_wsSystemPath.empty( ) ) {
        TCHAR szInfoBuf [ MAX_PATH ] = { 0 };
        UINT dwSize( MAX_PATH );

        if ( GetSystemDirectory( szInfoBuf, dwSize ) ) {
            m_wsSystemPath = szInfoBuf;
            }
        }

    return m_wsSystemPath;
    }
//===================================================================
std::wstring CAppEx::GetWindowsPath( )
    {
    if ( m_wsWindowsPath.empty( ) ) {
        TCHAR szInfoBuf [ MAX_PATH ] = { 0 };
        UINT dwSize( MAX_PATH );

        if ( GetWindowsDirectory( szInfoBuf, dwSize ) ) {
            m_wsWindowsPath = szInfoBuf;
            }
        }

    return m_wsWindowsPath;
    }
//======================================================================
std::wstring CAppEx::GetProgramFilePath( )
    {
    if ( m_wsProgramFilePath.empty( ) ) {
        m_wsProgramFilePath = GetFolderPathEx(CSIDL_PROGRAM_FILES );
        }

    return m_wsProgramFilePath;
    }
//======================================================================
BOOL CAppEx::IsSystemFolder( const  std::wstring &wsPath )
    {
    return wsPath == GetSystemPath( )
        || wsPath == GetProgramFilePath( )
        || wsPath == GetWindowsPath  ()
        || wsPath == GetUserPath( )
        || wsPath == GetPublicDataPath( )
        || wsPath == GetUserDocuments( )
        || wsPath == GetPublicDocuments( )
        || wsPath == GetUserDataPath( )
        || wsPath == GetUserDeskTopPath( )
        || wsPath == GetUserLocalDataPath( );
    }
//========================================================================
DWORD CAppEx::IsProcessExist( DWORD processID )
    {
    // 
    DWORD result = CheckProcessByHandle( processID );

    // 
    if (result == ERROR_ACCESS_DENIED) {
        result = CheckProcessBySnapshot( processID );
        }

    return result;
    }

//
DWORD CAppEx::CheckProcessBySnapshot( DWORD processID )
    {
    //
    HANDLE hSnapshot = CreateToolhelp32Snapshot( TH32CS_SNAPPROCESS , 0 );
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return GetLastError( );
        }

    //
    std::unique_ptr<void , decltype( &CloseHandle )> snapshotHandle( hSnapshot , CloseHandle );

    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof( pe32 );

    //
    if (Process32FirstW( hSnapshot , &pe32 )) {
        do {
            if (pe32.th32ProcessID == processID) {
                return ERROR_SUCCESS; 
                }
            }
        while (Process32NextW( hSnapshot , &pe32 ));
        }

    return ERROR_NOT_FOUND;
    }

//
DWORD CAppEx::CheckProcessByHandle( DWORD processID )
    {
    HANDLE hProcess = OpenProcess( PROCESS_QUERY_LIMITED_INFORMATION , FALSE , processID );
    if (hProcess != NULL) {
        CloseHandle( hProcess );
        return ERROR_SUCCESS; 
        }

    return GetLastError( );
    }