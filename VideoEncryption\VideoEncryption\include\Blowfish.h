// _THE BlowFishEnc ENCRYPTION ALGORITHM_
// by <PERSON>
// Revised code--3/20/94
// Converted to C++ class 5/96, <PERSON>
// Updated to support FileEncryption Utility by <PERSON><PERSON>, 9/02
#pragma once

#include "Encryption_i.h"

class BlowFishEnc : public EncryptionInterface
{
public:
	BlowFishEnc(const char *pwd);
	~BlowFishEnc();

	DWORD encryptStream(const char *plain, const DWORD size, char *cipher);
	DWORD decryptStream(const char *cipher, const DWORD size, char *plain);
	DWORD GetOutputLength(DWORD lInputLong);

private:
	

	DWORD 	PArray[18];
	DWORD	SBoxes[4][256];
	void 	BlowFishEnc_encipher(DWORD *xl, DWORD *xr);
	void 	BlowFishEnc_decipher(DWORD *xl, DWORD *xr);
};

union aword {
	DWORD dword;
	BYTE byte [4];
	struct {
	unsigned int byte3:8;
	unsigned int byte2:8;
	unsigned int byte1:8;
	unsigned int byte0:8;
	} w;
};
