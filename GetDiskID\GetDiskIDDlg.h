﻿
// GetDiskIDDlg.h: 头文件
//

#pragma once

#include <ui/StaticEx.h>
// CGetDiskIDDlg 对话框
class CGetDiskIDDlg : public CDialogEx
{
// 构造
public:
	CGetDiskIDDlg(CWnd* pParent = nullptr);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_GETDISKID_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持


// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	DECLARE_MESSAGE_MAP()
    afx_msg void OnBnClickedCopy();
	public:
	StaticEx m_serial;
	};
