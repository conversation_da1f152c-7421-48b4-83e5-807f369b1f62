#pragma once
#include <vector>
#include <map>

// 预加载策略类，管理应用程序启动时的图片加载优化
class CPreloadStrategy
{
public:
    // 获取单例
    static CPreloadStrategy& Instance() {
        static CPreloadStrategy instance;
        return instance;
    }
    
    // 初始化预加载策略
    void Initialize(HWND hMainWnd) {
        // 初始化延迟加载器
        CLazyImageLoader::Instance().Initialize(hMainWnd);
        
        // 定义预加载的图片列表
        DefinePreloadImages();
        
        // 启动预加载
        StartPreload();
    }
    
    // 清理资源
    void Cleanup() {
        CLazyImageLoader::Instance().Stop();
        CLazyImageLoader::Instance().ClearCache();
    }
    
private:
    CPreloadStrategy() {}
    ~CPreloadStrategy() {}
    
    // 定义需要预加载的图片
    void DefinePreloadImages() {
        // 关键图片 - 立即加载
        m_criticalImages = {
            _T("FrameBack\\lan-8.jpg"),      // 默认背景
            _T("Button\\n_lan_xian.png"),    // 默认按钮样式
            _T("Button\\Progress_lan.png")   // 默认进度条样式
        };
        
        // 高优先级图片 - 启动后立即加载
        m_highPriorityImages = {
            _T("Button\\n_zi.png"),
            _T("Button\\n_lv.png"),
            _T("Button\\Progress_zi.png"),
            _T("Button\\Progress_lv.png")
        };
        
        // 普通优先级图片 - 空闲时加载
        m_normalPriorityImages = {
            _T("FrameBack\\zs-1.jpg"),
            _T("FrameBack\\zs-2.jpg"),
            _T("FrameBack\\zs-3.jpg"),
            _T("FrameBack\\zs-4.jpg"),
            _T("FrameBack\\lv-1.jpg"),
            _T("FrameBack\\lv-2.jpg"),
            _T("FrameBack\\lv-3.jpg"),
            _T("FrameBack\\lv-4.jpg"),
            _T("FrameBack\\lan-1.jpg"),
            _T("FrameBack\\lan-2.jpg"),
            _T("FrameBack\\lan-3.jpg"),
            _T("FrameBack\\lan-4.jpg"),
            _T("FrameBack\\lan-5.jpg"),
            _T("FrameBack\\lan-6.jpg"),
            _T("FrameBack\\lan-7.jpg")
        };
    }
    
    // 开始预加载
    void StartPreload() {
        // 1. 立即加载关键图片（同步）
        for (const auto& image : m_criticalImages) {
            CGPImageInfo::Instance()->ImageFromFile(image);
        }
        
        // 2. 异步加载高优先级图片
        for (const auto& image : m_highPriorityImages) {
            CGPImageInfo::Instance()->ImageFromFileAsync(image, nullptr, 
                CLazyImageLoader::LoadPriority::High);
        }
        
        // 3. 空闲时预加载其他图片
        CLazyImageLoader::Instance().PreloadImagesWhenIdle(m_normalPriorityImages);
    }
    
    // 成员变量
    std::vector<CString> m_criticalImages;
    std::vector<CString> m_highPriorityImages;
    std::vector<CString> m_normalPriorityImages;
};

// 使用示例：
// 在应用程序的 InitInstance 中：
// CPreloadStrategy::Instance().Initialize(m_pMainWnd->GetSafeHwnd());
//
// 在应用程序的 ExitInstance 中：
// CPreloadStrategy::Instance().Cleanup();
