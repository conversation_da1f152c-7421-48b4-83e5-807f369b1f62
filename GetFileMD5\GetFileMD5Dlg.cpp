﻿
// GetFileMD5Dlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "GetFileMD5.h"
#include "GetFileMD5Dlg.h"
#include "afxdialogex.h"
#include <HALVault/halvault_api.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CGetFileMD5Dlg 对话框



CGetFileMD5Dlg::CGetFileMD5Dlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_GETFILEMD5_DIALOG, pParent)
	, m_strFilePzath( _T( "" ) )
	, m_strFileMD5( _T( "" ) )
	{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CGetFileMD5Dlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange( pDX );
	DDX_Text( pDX , IDC_MFCEDITBROWSE_FILE_PATH , m_strFilePzath );
	DDX_Text( pDX , IDC_EDIT_MD5 , m_strFileMD5 );
	}

BEGIN_MESSAGE_MAP(CGetFileMD5Dlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_BN_CLICKED( IDC_MFCBUTTON_COPY , &CGetFileMD5Dlg::OnBnClickedMfcbuttonCopy )
	ON_EN_CHANGE( IDC_MFCEDITBROWSE_FILE_PATH , &CGetFileMD5Dlg::OnEnChangeMfceditbrowseFilePath )
END_MESSAGE_MAP()


// CGetFileMD5Dlg 消息处理程序

BOOL CGetFileMD5Dlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CGetFileMD5Dlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CGetFileMD5Dlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CGetFileMD5Dlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}



void CGetFileMD5Dlg::OnBnClickedMfcbuttonCopy( )
	{
    if (OpenClipboard( )) {
        EmptyClipboard( );
        size_t size = ( m_strFileMD5.GetLength( ) + 1 ) * sizeof( wchar_t );
        HGLOBAL hMem = GlobalAlloc( GMEM_MOVEABLE , size );
        if (hMem) {
            memcpy( GlobalLock( hMem ) , ( LPCTSTR )m_strFileMD5, size );
            GlobalUnlock( hMem );
            if (NULL != SetClipboardData( CF_UNICODETEXT , hMem )) {
                AfxMessageBox( L"MD5复制成功。" );
                }
            else {
                AfxMessageBox(  L"MD5复制失败。" );
                }
            }
        CloseClipboard( );
        }
	}
//850A421BC83D368A18FD67349DA31D41

void CGetFileMD5Dlg::OnEnChangeMfceditbrowseFilePath( )
	{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialogEx::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
    UpdateData( );
    wchar_t wMD5 [ 33 ] {};
    HALVault_GetFileMD5( m_strFilePzath.GetString( ) , wMD5 , 33 );
	m_strFileMD5 = wMD5;
	UpdateData( FALSE );
	}
