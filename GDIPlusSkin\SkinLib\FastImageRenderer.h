#pragma once
#include <gdiplus.h>
#include <memory>
#include <map>
#include <mutex>

// 快速图片渲染器，优化GDI+绘制性能
class CFastImageRenderer
{
public:
    static CFastImageRenderer& Instance() {
        static CFastImageRenderer instance;
        return instance;
    }

    // 预处理图片以提高绘制性能
    void PreprocessImage(Gdiplus::Image* pImage, const CString& key) {
        if (!pImage) return;
        
        // 获取图片尺寸
        int width = pImage->GetWidth();
        int height = pImage->GetHeight();
        
        // 创建预渲染的位图（使用32位ARGB格式以获得最佳性能）
        auto pBitmap = std::make_unique<Gdiplus::Bitmap>(width, height, PixelFormat32bppARGB);
        
        // 预渲染图片到位图
        Gdiplus::Graphics graphics(pBitmap.get());
        graphics.SetInterpolationMode(Gdiplus::InterpolationModeNearestNeighbor); // 最快的插值模式
        graphics.SetCompositingMode(Gdiplus::CompositingModeSourceCopy);
        graphics.SetCompositingQuality(Gdiplus::CompositingQualityHighSpeed);
        graphics.SetSmoothingMode(Gdiplus::SmoothingModeNone); // 关闭抗锯齿以提高速度
        
        graphics.DrawImage(pImage, 0, 0, width, height);
        
        // 缓存预处理的位图 - 加锁保护
        std::lock_guard<std::mutex> lock(m_mutex);
        m_preprocessedImages[key] = std::move(pBitmap);
    }

    // 快速绘制图片
    void FastDrawImage(Gdiplus::Graphics* pGraphics, const CString& key, 
                      const Gdiplus::RectF& destRect, 
                      float srcX = 0, float srcY = 0, 
                      float srcWidth = -1, float srcHeight = -1) {
        
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_preprocessedImages.find(key);
        if (it == m_preprocessedImages.end()) {
            return;
        }
        
        Gdiplus::Bitmap* pBitmap = it->second.get();
        if (!pBitmap) return;
        
        // 如果没有指定源区域，使用整个图片
        if (srcWidth < 0) srcWidth = (float)pBitmap->GetWidth();
        if (srcHeight < 0) srcHeight = (float)pBitmap->GetHeight();
        
        // 使用缓存的位图进行快速绘制
        pGraphics->DrawImage(pBitmap, destRect, srcX, srcY, srcWidth, srcHeight, Gdiplus::UnitPixel);
    }

    // 批量绘制优化（减少状态切换）
    void BeginBatchDraw(Gdiplus::Graphics* pGraphics) {
        // 设置最优性能的绘制参数
        pGraphics->SetInterpolationMode(Gdiplus::InterpolationModeNearestNeighbor);
        pGraphics->SetCompositingMode(Gdiplus::CompositingModeSourceOver);
        pGraphics->SetCompositingQuality(Gdiplus::CompositingQualityHighSpeed);
        pGraphics->SetSmoothingMode(Gdiplus::SmoothingModeNone);
        pGraphics->SetPixelOffsetMode(Gdiplus::PixelOffsetModeHalf);
    }

    void EndBatchDraw(Gdiplus::Graphics* pGraphics) {
        // 恢复默认设置（如果需要）
        pGraphics->SetInterpolationMode(Gdiplus::InterpolationModeDefault);
        pGraphics->SetCompositingQuality(Gdiplus::CompositingQualityDefault);
        pGraphics->SetSmoothingMode(Gdiplus::SmoothingModeDefault);
    }

    // 清理缓存
    void ClearCache() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_preprocessedImages.clear();
    }

    // 移除特定缓存
    void RemoveFromCache(const CString& key) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_preprocessedImages.erase(key);
    }

private:
    CFastImageRenderer() = default;
    ~CFastImageRenderer() = default;
    
    std::map<CString, std::unique_ptr<Gdiplus::Bitmap>> m_preprocessedImages;
    mutable std::mutex m_mutex;  // 保护图片缓存的互斥锁
};
