#pragma once
#include "definition.h"

int read_thread( void* arg );

int subtitle_thread( void* arg );

int video_thread( void* arg );

int audio_thread( void* arg );

int queue_picture( VideoState* is , AVFrame* src_frame , double pts , double duration , int64_t pos , int serial );

int get_video_frame( VideoState* is , AVFrame* frame );

void set_default_window_size( int width , int height , AVRational sar );

int is_realtime( AVFormatContext* s );