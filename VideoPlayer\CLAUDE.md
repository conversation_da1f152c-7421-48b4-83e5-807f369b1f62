# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Windows-based encrypted video player application built with C++/MFC. The project is part of a larger video encryption system ("金钻视频加密专家") that provides secure video playback capabilities with DRM-like features.

## Build System

### Solution Structure
- Main solution file: `VideoPlayer.sln` (Visual Studio 2019/2022)
- Primary project: `VideoPlayer.vcxproj` - The main video player application
- Dependencies include multiple libraries:
  - `FFPlayerLib` - FFmpeg-based video decoding
  - `VideoEncryption` - Video encryption/decryption logic  
  - `GDIPlusSkin` - UI skinning and graphics
  - `VELib` - Video encoding library
  - `EP-common` - Common encryption primitives
  - `ve-common` - Common video utilities
  - `FunLib` - General utility functions
  - `GetDiskID` - Hardware identification
  - `HALVault` - Hardware abstraction layer for protection

### Build Configurations
- Platforms: Win32, x64
- Configurations: Debug, Release, Release_64
- Toolset: v142 (Visual Studio 2019)
- C++ Standard: C++17
- Character Set: Unicode
- MFC Usage: Static linking

### Build Commands
```bash
# Build entire solution (Release, x64)
msbuild VideoPlayer.sln -p:Configuration=Release -p:Platform=x64

# Build specific project
msbuild VideoPlayer.vcxproj -p:Configuration=Release -p:Platform=x64

# Build Debug version
msbuild VideoPlayer.sln -p:Configuration=Debug -p:Platform=Win32
```

### Dependencies and Libraries
External dependencies include:
- **FFmpeg** - Video/audio decoding (`A:\ffmpeg\msvc\`)
- **Crypto++** - Cryptographic functions (`A:\crypto++\8.90\`)
- **WebView2** - Web browser control (`A:\WebView2\`)
- **VMProtect SDK** - Software protection (`A:\VMProtectSDK\`)
- **SDL2** - Cross-platform multimedia

## Architecture

### Core Components

#### Main Application (`VideoPlayer.cpp/h`)
- Entry point and application lifecycle management
- MFC `CWinApp` derived class

#### Main Dialog (`VideoPlayerDlg.cpp/h`) 
- Primary UI window and video player interface
- Handles video playback, controls, fullscreen, volume
- Manages encrypted video decryption and authentication
- Implements anti-piracy features like random verification dialogs
- Custom UI controls including bitmap sliders and styled buttons

#### Key Features
1. **Encrypted Video Playback** - Supports AES-encrypted video files
2. **Hardware Binding** - Uses disk ID and hardware fingerprinting
3. **Anti-Piracy** - Random verification dialogs, process monitoring
4. **Custom UI** - Skinned interface with PNG-based buttons
5. **Fullscreen Support** - Toggle between windowed and fullscreen modes
6. **Volume Control** - System volume integration
7. **Progress Control** - Seeking and timeline navigation

#### UI Components (`UI/` directory)
- `StyleButton` - PNG-based custom buttons with hover states
- `BitmapSlider` - Custom slider control for progress/volume
- `CaptchaStatic` - Anti-piracy verification controls
- `DUIButton` - Additional UI button implementations

### File Organization
```
VideoPlayer/
├── VideoPlayer.cpp/h          # Application entry point
├── VideoPlayerDlg.cpp/h       # Main player window
├── UI/                        # Custom UI controls
│   ├── BitmapSlider.*         # Progress/volume sliders
│   ├── StyleButton.*          # PNG-based buttons  
│   └── pngButton/             # Button rendering system
├── res/                       # Resources (icons, images)
├── CDlgInfo.*                 # Information dialogs
├── CDlgOpenFile.*             # File open dialogs
├── funLib.*                   # Utility functions
└── progressHand.*             # Progress handling
```

## Development Guidelines

### Code Style
- Uses MFC (Microsoft Foundation Classes)
- Windows-specific code with Win32 APIs
- Chinese comments and UI text
- Mixed case with Hungarian notation for some variables

### Security Considerations
- Handles encrypted video content with AES encryption
- Implements hardware-based DRM protection
- Contains anti-debugging and anti-piracy measures
- Uses VMProtect SDK for code obfuscation

### Common Tasks

#### Building the Project
1. Open `VideoPlayer.sln` in Visual Studio 2019/2022
2. Ensure all dependency paths are correctly configured
3. Select appropriate configuration (Debug/Release) and platform (Win32/x64)
4. Build solution or specific projects as needed

#### Adding New UI Controls
1. Add control declarations to `VideoPlayerDlg.h`
2. Initialize in `OnInitDialog()`
3. Add to layout system via `AddCtrl()` for resizing support
4. Handle events in message map

#### Modifying Video Playback
- Core playback logic is in `VideoPlayerDlg::PlayVideoThread()`
- Encryption/decryption handled through `VideoAESCommon.h`
- FFmpeg integration via `FFPlayerLib` project

### Cursor Rules Integration
The project includes Cursor IDE rules in `../.cursor/rules/`:
- `cpp.md` - Comprehensive C++ development guidelines for Windows/Visual Studio
- `em.md` - PHP/web development rules (not applicable to this project)

These rules provide detailed guidance for C++ development practices, build systems, security considerations, and Windows-specific development patterns.