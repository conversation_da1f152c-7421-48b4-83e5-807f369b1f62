// A16A16Label.cpp : implementation file
//

#include "pch.h"
#include "GPLabel.h"
#include "command/MemoryDC.h"
#include "command/Functions.h"
#include "GDIPlusSkinH.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

BEGIN_MESSAGE_MAP(CGPLabel, CStatic)
	//{{AFX_MSG_MAP(CGPLabel)
	ON_WM_TIMER()
	ON_WM_LBUTTONDOWN()
	ON_WM_SETCURSOR()
	ON_WM_PAINT()
	ON_WM_SYSCOLORCHANGE()
	ON_WM_CREATE()
	#ifdef WM_DPICHANGED
	ON_MESSAGE(WM_DPICHANGED, OnDpiChanged)
	#endif
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

///////////////////////////////////////////////////////////////////////////
CGPLabel::CGPLabel()
{
	m_crText = GetSysColor(COLOR_WINDOWTEXT);

// 1.1
	m_hBackBrush = NULL;


	// 统一：使用系统默认消息字体（不额外按 DPI 放大）
	CFunctions::GetDefaultFont(m_lf);
	m_font.CreateFontIndirect(&m_lf);
	m_bTimer =			FALSE;
	m_bState =			FALSE;
	m_bTransparent =	FALSE;
	m_bLink =			TRUE;
	m_hCursor =			NULL;
	m_Type =			None;
	m_bFont3d =			FALSE;
	m_bNotifyParent =	FALSE;
	m_bToolTips =		FALSE;
	m_bRotation =		FALSE;
	
	m_hwndBrush = ::CreateSolidBrush(GetSysColor(COLOR_3DFACE));
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::~CGPLabel
//
// Description:		
//////////////////////////////////////////////////////////////////////////
CGPLabel::~CGPLabel()
{
	// Clean up
	m_font.DeleteObject();
	::DeleteObject(m_hwndBrush);
	::DeleteObject(m_hBackBrush);
	
}


//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::ReconstructFont
//
// Description:		Helper function to build font after it was changed

//////////////////////////////////////////////////////////////////////////
void CGPLabel::ReconstructFont()
{
	m_font.DeleteObject();
	// 统一：使用系统默认消息字体（不额外按 DPI 放大）
	CFunctions::GetDefaultFont(m_lf);
	m_font.CreateFontIndirect(&m_lf);
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::OnPaint
//
// Description:		Handles all the drawing code for the A16Label
//////////////////////////////////////////////////////////////////////////

void CGPLabel::OnPaint() 
{
	CPaintDC dc(this); // device context for painting


	CRect rc;
	GetClientRect(rc);
	CString strText;
	GetWindowText(strText);

	///////////////////////////////////////////////////////
	//
	// Set up for double buffering...
	//
	CDC* pDCMem;

	if (!m_bTransparent)
	{
		pDCMem = new CDC;
		pDCMem->CreateCompatibleDC(&dc);
		CBitmap bmp;
		bmp.CreateCompatibleBitmap(&dc,rc.Width(),rc.Height());
		pDCMem->SelectObject(&bmp);
	}
	else
	{
		pDCMem = &dc;
	}


	COLORREF crText = pDCMem->SetTextColor(m_crText);
	CFont *pOldFont = pDCMem->SelectObject(&m_font);


	// Fill in backgound if not transparent
	if (!m_bTransparent)
	{
		CBrush br;
		
		if (m_bState && m_Type == Background)
		{
			if (m_hBackBrush != NULL)
				br.Attach(m_hBackBrush);
			else
				br.Attach(m_hwndBrush);
		}
		else
		{
			if (m_hBackBrush != NULL)
				br.Attach(m_hBackBrush);
			else
				br.Attach(m_hwndBrush);
		}
				
		pDCMem->FillRect(rc,&br);
		br.Detach();
	}
	

	UINT nMode = pDCMem->SetBkMode(TRANSPARENT);

	// If the text is flashing turn the text color on
	// then to the color of the window background.

	if (0 != m_hBackBrush)
		{
		LOGBRUSH lb;
		ZeroMemory(&lb,sizeof(lb));
		::GetObject(m_hBackBrush,sizeof(lb),&lb);

		// Something to do with flashing
		if (!m_bState && m_Type == Text)
			pDCMem->SetTextColor(lb.lbColor);
		}


	DWORD dwFlags = 0;

	if (GetStyle() == SS_LEFT)
		dwFlags = DT_LEFT;

	if (GetStyle() & SS_RIGHT)
		dwFlags = DT_RIGHT;

	// If the text centered make an assumtion that
	// the will want to center verticly as well
	if (GetStyle() & SS_CENTERIMAGE)
	{
		dwFlags = DT_CENTER;

		// Apply 
		if (strText.Find(TEXT("\r\n")) == -1)
		{
			dwFlags |= DT_VCENTER;

			// And because DT_VCENTER only works with single lines
			dwFlags |= DT_SINGLELINE; 
		}
	}


	if (m_bRotation)
	{
		int nAlign = pDCMem->SetTextAlign (TA_BASELINE);

		CPoint pt;
		GetViewportOrgEx (pDCMem->m_hDC,&pt) ;
		SetViewportOrgEx (pDCMem->m_hDC,rc.Width() / 2, rc.Height() / 2, NULL) ;
		pDCMem->TextOut (0, 0, strText) ;
		SetViewportOrgEx (pDCMem->m_hDC,pt.x / 2, pt.y / 2, NULL) ;
		pDCMem->SetTextAlign (nAlign);
	}
	else
	{
		pDCMem->DrawText(strText,rc,dwFlags);
		if (m_bFont3d)
		{
			pDCMem->SetTextColor(RGB(255,255,255));

			if (m_3dType == Raised)
				rc.OffsetRect(-1,-1);
			else
				rc.OffsetRect(1,1);

			pDCMem->DrawText(strText,rc,dwFlags);
			m_3dType;

		}
	}

	// Restore DC's State
	pDCMem->SetBkMode(nMode);
	pDCMem->SelectObject(pOldFont);
	pDCMem->SetTextColor(crText);

	if (!m_bTransparent)
	{
		dc.BitBlt(0,0,rc.Width(),rc.Height(),pDCMem,0,0,SRCCOPY);
		delete pDCMem;
	}
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::OnTimer
//
// Description:		Used in conjunction with 'FLASH' functions

//////////////////////////////////////////////////////////////////////////
void CGPLabel::OnTimer(UINT nIDEvent) 
{
	m_bState = !m_bState;

	InvalidateRect(NULL,TRUE);
	UpdateWindow();
	
	CStatic::OnTimer(nIDEvent);
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::OnSetCursor
//
// Description:		Used in conjunction with 'LINK' function
//////////////////////////////////////////////////////////////////////////
BOOL CGPLabel::OnSetCursor(CWnd* pWnd, UINT nHitTest, UINT message) 
{
	if (m_hCursor)
	{
		::SetCursor(m_hCursor);
		return TRUE;
	}

	return CStatic::OnSetCursor(pWnd, nHitTest, message);
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::OnLButtonDown
//
// Description:		Called when a link is click on
//////////////////////////////////////////////////////////////////////////
void CGPLabel::OnLButtonDown(UINT nFlags, CPoint point) 
{
	if (m_bNotifyParent)
	{
		CString strLink;

		GetWindowText(strLink);
		ShellExecute(NULL,TEXT("open"),strLink,NULL,NULL,SW_SHOWNORMAL);
	}
	else
	{
		// To use notification in parent window
		// Respond to a OnNotify in parent and disassemble the message
		//
		NMHDR nm;

		nm.hwndFrom = GetSafeHwnd();
		nm.idFrom  = GetDlgCtrlID();
		nm.code = NM_LINKCLICK;
		GetParent()->SendMessage(WM_NOTIFY,nm.idFrom,(LPARAM) &nm);
	}
		
	CStatic::OnLButtonDown(nFlags, point);
}


//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetText
//
// Description:		Short cut to set window text - caption - A16Label

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetText(const CString& strText)
{
	SetWindowText(strText);
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetTextColor
//
// Description:		Sets the text color 

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetTextColor(COLORREF crText)
{
	m_crText = crText;
	RedrawWindow();
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFontBold
//
// Description:		Sets the font ot bold 

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFontBold(BOOL bBold)
{	
	m_lf.lfWeight = bBold ? FW_BOLD : FW_NORMAL;
	ReconstructFont();
	RedrawWindow();
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFontUnderline
//
// Description:		Sets font underline attribue

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFontUnderline(BOOL bSet)
{	
	m_lf.lfUnderline = (BYTE)bSet;
	ReconstructFont();
	RedrawWindow();
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFontItalic
//
// Description:		Sets font italic attribute

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFontItalic(BOOL bSet)
{
	m_lf.lfItalic = (BYTE)bSet;
	ReconstructFont();
	RedrawWindow();
	return *this;	
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetSunken
//
// Description:		Sets sunken effect on border

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetSunken(BOOL bSet)
{
	if (!bSet)
		ModifyStyleEx(WS_EX_STATICEDGE,0,SWP_DRAWFRAME);
	else
		ModifyStyleEx(0,WS_EX_STATICEDGE,SWP_DRAWFRAME);
		
	return *this;	
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetBorder

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetBorder(BOOL bSet)
{
	if (!bSet)
		ModifyStyle(WS_BORDER,0,SWP_DRAWFRAME);
	else
		ModifyStyle(0,WS_BORDER,SWP_DRAWFRAME);
		
	return *this;	
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFontSize
//
// Description:		Sets the font size

//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFontSize(int nSize)
{
	nSize*=-1;
	m_lf.lfHeight = nSize;
	ReconstructFont();
	RedrawWindow();
	return *this;
}


//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetBkColor
//
// Description:		Sets background color
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetBkColor(COLORREF crBkgnd)
{
	if (m_hBackBrush)
		::DeleteObject(m_hBackBrush);
	
	m_hBackBrush = ::CreateSolidBrush(crBkgnd);

	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFontName
//
// Description:		Sets the fonts face name
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFontName(const CString& strFont)
{	
	wcscpy_s(m_lf.lfFaceName, strFont);
	ReconstructFont();
	RedrawWindow();
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::FlashText
//
// Description:		As the function states
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::FlashText(BOOL bActivate)
{
	if (m_bTimer)
		KillTimer(1);

	if (bActivate)
	{
		m_bState = FALSE;
		
		m_bTimer = TRUE;
		
		SetTimer(1,500,NULL);

		m_Type = Text;
	}
	else
		m_Type = None; // Fix

	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::FlashBackground
//
// Description:		As the function states
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::FlashBackground(BOOL bActivate)
{

	if (m_bTimer)
		KillTimer(1);

	if (bActivate)
	{
		m_bState = FALSE;

		m_bTimer = TRUE;
		SetTimer(1,500,NULL);

		m_Type = Background;
	}

	return *this;
}


//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetLink
//
// Description:		Indicates the string is a link
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetLink(BOOL bLink,BOOL bNotifyParent)
{
	m_bLink = bLink;
	m_bNotifyParent = bNotifyParent;

	if (bLink)
		ModifyStyle(0,SS_NOTIFY);
	else
		ModifyStyle(SS_NOTIFY,0);

	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetLinkCursor
//
// Description:		Sets the internet browers link
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetLinkCursor(HCURSOR hCursor)
{
	m_hCursor = hCursor;
	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetTransparent
//
// Description:		Sets the A16Label window to be transpaent
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetTransparent(BOOL bSet)
{
	m_bTransparent = bSet;
	InvalidateRect(NULL,TRUE);
	UpdateWindow();

	return *this;
}

//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetFont3D
//
// Description:		Sets the 3D attribute of the font.
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetFont3D(BOOL bSet,Type3D type)
{
	m_bFont3d = bSet;
	m_3dType = type;
	RedrawWindow();

	return *this;
}

void CGPLabel::OnSysColorChange() 
{
//	CStatic::OnSysColorChange();
	if (m_hwndBrush)
		::DeleteObject(m_hwndBrush);

	m_hwndBrush = ::CreateSolidBrush(GetSysColor(COLOR_3DFACE));
	
	RedrawWindow();
		
}



//////////////////////////////////////////////////////////////////////////
//
// Function:		CGPLabel::SetRotationAngle
//
// Description:		Sets the 3D attribute of the font.
//////////////////////////////////////////////////////////////////////////
CGPLabel& CGPLabel::SetRotationAngle(UINT nAngle,BOOL bRotation)
{
	// Arrrrh...
	// Your looking in here why the font is rotating, aren't you?
	// Well try setting the font name to 'Arial' or 'Times New Roman'
	// Make the Angle 180 and set bRotation to true.
	//
	// Font rotation _ONLY_ works with TrueType fonts...
	//
	// 
	m_lf.lfEscapement = m_lf.lfOrientation = (nAngle * 10);
	m_bRotation = bRotation;
	
	ReconstructFont();
	
	RedrawWindow();

	return *this;
}

