﻿#pragma once
#include <net\WebViewWnd.h>

// CDlgWed 对话框

class CDlgWed : public CDialogEx
{
	DECLARE_DYNAMIC(CDlgWed)

public:
	CDlgWed(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~CDlgWed();

	void SetURL( const CString& strURL )
		{
		m_strURL = strURL;
		}
// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_WED };
#endif
    public:
    virtual BOOL OnInitDialog( );
    afx_msg void OnSize( UINT nType , int cx , int cy );
protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
	private:
	CWebViewWnd m_vw;
	CString     m_strURL;
	
	};
