#pragma once
#include <functional>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <map>
#include <atomic>

// 延迟图片加载器，避免启动时加载所有资源
class CLazyImageLoader
{
public:
    // 加载优先级
    enum class LoadPriority {
        Low = 0,      // 低优先级，用于预加载
        Normal = 1,   // 普通优先级
        High = 2,     // 高优先级，用于立即需要显示的图片
        Critical = 3  // 关键优先级，用于必须立即加载的图片
    };
    
    // 图片加载回调
    typedef std::function<void(Gdiplus::Image*, const CString&)> ImageLoadCallback;
    
    // 获取单例实例
    static CLazyImageLoader& Instance();
    
    // 销毁单例
    static void Destroy();
    
    // 初始化（需要传入主窗口句柄用于消息通知）
    void Initialize(HWND hNotifyWnd);

    // 异步加载图片
    void LoadImageAsync(const CString& imagePath, ImageLoadCallback callback, 
                       LoadPriority priority = LoadPriority::Normal);

    // 预加载关键图片（在程序空闲时）
    void PreloadImagesWhenIdle(const std::vector<CString>& imagePaths);
    
    // 检查图片是否已加载
    bool IsImageLoaded(const CString& imagePath) const;
    
    // 获取已缓存的图片（如果存在）
    Gdiplus::Image* GetCachedImage(const CString& imagePath) const;
    
    // 清理缓存
    void ClearCache();

    // 启动加载线程
    void Start();

    // 停止加载线程
    void Stop();
    
    // 消息处理（需要在主窗口的消息映射中调用）
    static LRESULT OnImageLoaded(WPARAM wParam, LPARAM lParam);

private:
    CLazyImageLoader();
    ~CLazyImageLoader();
    
    // 禁用拷贝
    CLazyImageLoader(const CLazyImageLoader&) = delete;
    CLazyImageLoader& operator=(const CLazyImageLoader&) = delete;

    // 工作线程函数
    void WorkerThread();
    
    // 获取最高优先级任务
    struct LoadTask {
        CString imagePath;
        ImageLoadCallback callback;
        LoadPriority priority;
        DWORD timestamp; // 用于调试和性能统计
        
        // 优先级比较（用于优先队列）
        bool operator<(const LoadTask& other) const {
            return priority < other.priority;
        }
    };
    
    LoadTask GetHighestPriorityTask();
    
    // 在主线程中执行回调
    void PostCallback(Gdiplus::Image* pImage, const CString& imagePath, ImageLoadCallback callback);
    
    // 回调数据结构
    struct CallbackData {
        Gdiplus::Image* pImage;
        CString imagePath;
        ImageLoadCallback callback;
    };

    // 成员变量
    std::queue<LoadTask> m_loadQueue;
    mutable std::mutex m_queueMutex;
    std::condition_variable m_condition;
    std::thread m_workerThread;
    std::atomic<bool> m_running;
    
    // 图片缓存
    std::map<CString, Gdiplus::Image*> m_imageCache;
    mutable std::mutex m_cacheMutex;
    
    // 主线程信息
    DWORD m_mainThreadId;
    HWND m_hNotifyWnd;
    
    // 单例实例
    static CLazyImageLoader* s_pInstance;
    static std::mutex s_instanceMutex;
};
