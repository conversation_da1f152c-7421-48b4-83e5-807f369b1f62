#pragma once
#include "definition.h"


int decoder_init( Decoder* d , AVCodecContext* avctx , PacketQueue* queue , SDL_cond* empty_queue_cond );
int decoder_start( Decoder* d , int ( *fn )( void* ) , const char* thread_name , void* arg );
int decode_interrupt_cb( void* ctx );
void fp_decoder_destroy( Decoder* d );
void decoder_abort( Decoder* d , FrameQueue* fq );
int decoder_decode_frame( Decoder* d , AVFrame* frame , AVSubtitle* sub );