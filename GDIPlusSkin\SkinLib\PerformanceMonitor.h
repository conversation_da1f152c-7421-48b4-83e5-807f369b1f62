#pragma once
#include <chrono>
#include <map>
#include <string>

// UI性能监控器
class CPerformanceMonitor
{
public:
    static CPerformanceMonitor& Instance() {
        static CPerformanceMonitor instance;
        return instance;
    }

    // 开始计时
    void StartTimer(const std::string& name) {
        m_timers[name] = std::chrono::high_resolution_clock::now();
    }

    // 结束计时并返回耗时（毫秒）
    double EndTimer(const std::string& name) {
        auto it = m_timers.find(name);
        if (it == m_timers.end()) return 0;
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - it->second);
        
        double ms = duration.count() / 1000.0;
        m_timers.erase(it);
        
        // 记录统计信息
        m_stats[name].totalTime += ms;
        m_stats[name].count++;
        m_stats[name].maxTime = max(m_stats[name].maxTime, ms);
        m_stats[name].minTime = min(m_stats[name].minTime, ms);
        
        return ms;
    }

    // 获取GDI资源使用情况
    void GetGDIResourceInfo(DWORD& gdiObjects, DWORD& userObjects) {
        HANDLE hProcess = GetCurrentProcess();
        gdiObjects = GetGuiResources(hProcess, GR_GDIOBJECTS);
        userObjects = GetGuiResources(hProcess, GR_USEROBJECTS);
    }

    // 获取内存使用情况
    SIZE_T GetMemoryUsage() {
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
            return pmc.WorkingSetSize;
        }
        return 0;
    }

    // 输出性能报告
    void PrintReport() {
        TRACE(L"\n=== UI Performance Report ===\n");
        
        for (const auto& pair : m_stats) {
            const auto& stat = pair.second;
            double avg = stat.totalTime / stat.count;
            
            TRACE(L"%s: avg=%.2fms, min=%.2fms, max=%.2fms, count=%d\n",
                  CString(pair.first.c_str()),
                  avg, stat.minTime, stat.maxTime, stat.count);
        }
        
        DWORD gdiObjects, userObjects;
        GetGDIResourceInfo(gdiObjects, userObjects);
        TRACE(L"GDI Objects: %d, User Objects: %d\n", gdiObjects, userObjects);
        
        SIZE_T memUsage = GetMemoryUsage() / (1024 * 1024);
        TRACE(L"Memory Usage: %d MB\n", memUsage);
        
        TRACE(L"==========================\n");
    }

    // 清除统计数据
    void Reset() {
        m_timers.clear();
        m_stats.clear();
    }

    // RAII计时器辅助类
    class ScopedTimer {
    public:
        ScopedTimer(const std::string& name) : m_name(name) {
            CPerformanceMonitor::Instance().StartTimer(name);
        }
        ~ScopedTimer() {
            double ms = CPerformanceMonitor::Instance().EndTimer(m_name);
            if (ms > 16.67) { // 超过一帧时间(60fps)则警告
                TRACE(L"[PERF WARNING] %s took %.2fms\n", CString(m_name.c_str()), ms);
            }
        }
    private:
        std::string m_name;
    };

private:
    CPerformanceMonitor() = default;
    
    struct Stats {
        double totalTime = 0;
        double minTime = DBL_MAX;
        double maxTime = 0;
        int count = 0;
    };
    
    std::map<std::string, std::chrono::high_resolution_clock::time_point> m_timers;
    std::map<std::string, Stats> m_stats;
};

// 方便的宏定义
#define PERF_TIMER(name) CPerformanceMonitor::ScopedTimer _timer_##__LINE__(name)
#define PERF_START(name) CPerformanceMonitor::Instance().StartTimer(name)
#define PERF_END(name) CPerformanceMonitor::Instance().EndTimer(name)
#define PERF_REPORT() CPerformanceMonitor::Instance().PrintReport()
