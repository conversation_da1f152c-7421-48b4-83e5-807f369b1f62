﻿// pch.h: 这是预编译标头文件。
// 下方列出的文件仅编译一次，提高了将来生成的生成性能。
// 这还将影响 IntelliSense 性能，包括代码完成和许多代码浏览功能。
// 但是，如果此处列出的文件中的任何一个在生成之间有更新，它们全部都将被重新编译。
// 请勿在此处添加要频繁更新的文件，这将使得性能优势无效。

#ifndef PCH_H
#define PCH_H

// 添加要在此处预编译的标头
#pragma once
#define _WINSOCKAPI_    // 阻止 Windows.h 引入旧版 winsock.h
#undef _WIN32_WINNT
#define _WIN32_WINNT 0x0601   // 目标最低版本 Windows 7

#include "framework.h"   // 引入 MFC/Windows.h

#include <winsock2.h>     // 在 Windows.h 之后包含，并确保未引入 winsock.h
#include <ws2tcpip.h>

#endif //PCH_H
