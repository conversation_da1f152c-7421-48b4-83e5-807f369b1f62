﻿#pragma once

#include "../EP-common/CommonDefinition.h"

#ifdef VE_EXPORTS
#define VE_API __declspec(dllexport)
#else
#define VE_API __declspec(dllimport)
#endif


// 外部导出函数声明
extern "C" {
    // 主要功能函数
    VE_API int EncryptVideo(EncryptParams* params);
    VE_API int EncryptVideoFile(EncryptParams* params);  // 兼容Sepration.cpp接口
#ifdef DECRYPT_VIDEO
    VE_API int DecryptVideo(EncryptParams* params);
#endif
    VE_API int GetVideoFileInfo(const wchar_t* filepath, VideoFileInfo* info);
    VE_API int GetVideoInfo(const wchar_t* filepath, VideoInfo* info); // 兼容性别名
    VE_API  const wchar_t * GetFinalOuptPath( );
    VE_API  const wchar_t* GetFileMD5( );
    VE_API  const wchar_t* FileMD5( const wchar_t* filepath );
    
    // 辅助函数
    VE_API const wchar_t* GetErrorDescription(int error_code);
    VE_API const wchar_t* GetErrorContent(int error_code);  // 兼容Sepration.cpp接口
    VE_API const wchar_t* GetLastErrorMessage();
    VE_API int InitializeVE();
    VE_API void CleanupVE();
    
    // 批处理功能
    VE_API int BatchEncryptFiles(const wchar_t** input_files, int file_count, 
                                const wchar_t* password, PROGRESS_CALLBACK callback, void* userdata);

    // C++类导出函数
    VE_API class CVideoProcessor* CreateVideoProcessor();
    VE_API void DestroyVideoProcessor(class CVideoProcessor* processor);
    VE_API int VideoProcessor_EncryptVideo(class CVideoProcessor* processor, EncryptParams* params);
#ifdef DECRYPT_VIDEO
   VE_API int VideoProcessor_DecryptVideo(class CVideoProcessor* processor, EncryptParams* params);
#endif
    VE_API int VideoProcessor_GetFileInfo(class CVideoProcessor* processor, const wchar_t* filepath, VideoInfo* info);
}