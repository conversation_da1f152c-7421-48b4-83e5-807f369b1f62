﻿#pragma once

#ifndef VC_EXTRALEAN
#define VC_EXTRALEAN            // 从 Windows 头中排除极少使用的资料
#endif

#include "targetver.h"

#define _ATL_CSTRING_EXPLICIT_CONSTRUCTORS      // 某些 CString 构造函数将是显式的

// 关闭 MFC 的一些常见且经常可放心忽略的隐藏警告消息
#define _AFX_ALL_WARNINGS

#include <afxwin.h>         // MFC 核心组件和标准组件
#include <afxext.h>         // MFC 扩展


#include <afxdisp.h>        // MFC 自动化类



#ifndef _AFX_NO_OLE_SUPPORT
#include <afxdtctl.h>           // MFC 对 Internet Explorer 4 公共控件的支持
#endif
#ifndef _AFX_NO_AFXCMN_SUPPORT
#include <afxcmn.h>             // MFC 对 Windows 公共控件的支持
#endif // _AFX_NO_AFXCMN_SUPPORT

#include <afxcontrolbars.h>     // MFC 支持功能区和控制条

#include <image/GdiPlusSingleton.h>

#include <stdint.h>
#include <thread>
#include <string>
#include <vector>

#define  PLAYER_VER 2
#define  APP_NAME   L"金钻视频加密专家视频播放器"

typedef enum {
    FF_EVT_OPEN_OK = 0 ,
    FF_EVT_OPEN_FAIL ,
    FF_EVT_PLAY_END ,
    FF_EVT_ERROR ,
    FF_EVT_BUFFERING ,
    FF_EVT_SEEK_DONE ,
    FF_EVT_TRACK_CHANGED ,  /* p1 = type, p2 = index */
    FF_EVT_VIDEO_SIZE      /* p1 = w, p2 = h */
    } FFEventID;

typedef void ( *FFEventCB )( FFEventID id , int64_t p1 , int64_t p2 , void* userdata );

#ifdef __cplusplus
extern "C" {
#endif
    int FF_Open( const wchar_t* wFilePath );
    int FF_SetPassword( const char* password , int vt , int at );
    void FF_SetRenderWindow( HWND hwnd );
    void FF_Pause( int pause );
    void FF_Stop( );
    void FF_Seek( double pos_sec );
    /* 当前文件总时长（秒），若未知返回 -1 */
    double FF_GetDuration( );
    /* 当前播放时间（秒），用 master clock 计算 */
    double FF_GetCurrentTime( );
    /* 音量控制：percent 取值 0~100 */
    void FF_SetVolume( int percent );
    int FF_GetVolume( );
    /* 全屏切换：on=1 进入全屏；on=0 退出全屏 */
    void FF_SetFullScreen( int on );

    /* ================= 扩展控制接口 ================= */
    int FF_Close( void );                                 /* 关闭当前流，但保留 SDL/FFmpeg 环境 */
    int FF_SeekRelative( double delta_sec );              /* 相对 Seek */
    int FF_SetMute( int on );
    int FF_GetMute( void );
    int FF_SetPlaybackRate( double rate );                /* 0.5 ~ 2.0， */
    double FF_GetPlaybackRate( void );
    int FF_CaptureBitmapPNG( const wchar_t* savePath );   /* 截图为 PNG，T */

    /* 轨道与字幕（Stub 实现） */
    typedef enum { FF_TRACK_AUDIO , FF_TRACK_VIDEO , FF_TRACK_SUBTITLE } FFTrackType;
    int FF_GetTrackCount( FFTrackType type );
    int FF_SelectTrack( FFTrackType type , int index );
    int FF_GetCurrentTrack( FFTrackType type );
    int FF_LoadSubtitle( const wchar_t* subtitlePath );

    /* 水印文字 */
    int FF_SetWatermarkText( const char* text , int position , const char* color , double alpha );
    int FF_SetWatermarkTextW( const wchar_t* text , int position , const char* color , double alpha );
    int FF_SetWatermarkTextEx( const char* text , int position , const char* color , double alpha , const char* fontfile , int fontsize );
    int FF_SetWatermarkTextExW( const wchar_t* text , int position , const char* color , double alpha , const wchar_t* fontfile , int fontsize );

    void FF_SetEventCallback( FFEventCB cb , void* userdata );

    int FF_SetWatermarkText( const char* text , int position , const char* color , double alpha );

    int FF_SetStopPosition( double seconds );
#ifdef __cplusplus
    }
#endif


#ifdef _UNICODE
#if defined _M_IX86
#pragma comment(linker,"/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='x86' publicKeyToken='6595b64144ccf1df' language='*'\"")
#elif defined _M_X64
#pragma comment(linker,"/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='amd64' publicKeyToken='6595b64144ccf1df' language='*'\"")
#else
#pragma comment(linker,"/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'\"")
#endif
#endif


