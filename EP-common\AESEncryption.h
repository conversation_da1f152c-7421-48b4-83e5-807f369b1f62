﻿#pragma once

#include <string>
#include <vector>
// Crypto++ 头文件 (所有项目都需要)
#include <aes.h>
#include <modes.h>
#include <filters.h>
#include <hex.h>
#include <osrng.h>
#include <sha.h>

// AES加密接口基类
class IEncryption
{
public:
    IEncryption(const std::string& password) : m_password(password) {}
    virtual ~IEncryption() {}

    virtual bool EncryptData(const unsigned char* input, size_t input_len, 
                           unsigned char* output, size_t& output_len) = 0;
    virtual bool DecryptData(const unsigned char* input, size_t input_len, 
                           unsigned char* output, size_t& output_len) = 0;
    virtual size_t GetOutputSize(size_t input_size) = 0;

protected:
    std::string m_password;
};

// AES-256-CTR
class CAESEncryption : public IEncryption
{
public:
    CAESEncryption(const std::string& password);
    virtual ~CAESEncryption();
    // 实现接口函数
    virtual bool EncryptData(const unsigned char* input, size_t input_len, 
                           unsigned char* output, size_t& output_len) override;
    virtual bool DecryptData(const unsigned char* input, size_t input_len, 
                           unsigned char* output, size_t& output_len) override;
    virtual size_t GetOutputSize(size_t input_size) override;


private:
    void DeriveKeyFromPassword(const std::string& password, 
                              unsigned char* key, unsigned char* iv);

    bool InitializeEncryption( const std::string& password );
private:
    unsigned char m_key[32];     // AES-256 需要32字节密钥
    unsigned char m_iv[16];      // CTR模式初始向量
    unsigned char m_counter[16]; // CTR模式计数器
    bool m_bInitialized;
};