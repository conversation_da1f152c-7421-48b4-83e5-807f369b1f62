﻿/*
 * Copyright (c) 2003 F<PERSON>rice <PERSON>
 *
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

/**
 * @file
 * simple media player based on the FFmpeg libraries
 */
#include "config.h"
#include <math.h>
#include <limits.h>
#include <signal.h>
#include <stdint.h>
#include "ffplay_renderer.h"
#include "cmdutils.h"
#include "ffplay.h"
#include "audio.h"
#include "clock_sync.h"
#include "control.h"
#include "decoder.h"
#include "fame_queue.h"
#include "pkt_queue.h"
#include "filter.h"
#include "stream.h"
#include "render.h"
#include "thread.h"
#include "subtitle.h"      /* 字幕加载接口 */
#include <libswscale/swscale.h> /* 用于截图格式转换 */
#include <libavutil/imgutils.h>
#include <windows.h>
#include <ShlObj.h>      // SHGetKnownFolderPath
#include <KnownFolders.h> // FOLDERID_Fonts


#ifdef _WIN32

static HWND g_externalHwnd = NULL;
VideoState* g_vs = NULL; // 当前播放实例

/* forward declaration to avoid implicit int */
static void ff_dispatch_event( FFEventID id , int64_t p1 , int64_t p2 );


int FF_Open(const wchar_t* wFilePath)
{
    if (!wFilePath || !*wFilePath)
        return -1;                       /* 参数无效 */

    /* 将 UTF-16(LE) 路径转换为 UTF-8，以便 FFmpeg/SDL 使用 */
    int lenUtf8 = WideCharToMultiByte(CP_UTF8, 0, wFilePath, -1, NULL, 0, NULL, NULL);
    if (lenUtf8 <= 0)
        return -2;                       /* 转码失败 */

    char* fileName = (char*)av_malloc(lenUtf8);
    if (!fileName)
        return -3;                       /* 内存不足 */

    WideCharToMultiByte(CP_UTF8, 0, wFilePath, -1, fileName, lenUtf8, NULL, NULL);

    int ret = PlayOpen(fileName);

    av_free(fileName);
    return ret;
}

void FF_SetRenderWindow(HWND hwnd)
{
    g_externalHwnd = hwnd;
}
// 播放控制接口
void FF_Pause(int pause)
{
    if ( !g_vs ) {
        return;
        }

    if (pause && !g_vs->paused) {
        stream_toggle_pause(g_vs);
    } else if (!pause && g_vs->paused) {
        stream_toggle_pause(g_vs);
    }
}

void FF_Stop()
{
    if (!g_vs) {
        return;
        }

    //SDL_Event evt;
    //evt.type = FF_QUIT_EVENT;
    //evt.user.data1 = g_vs;
    //SDL_PushEvent(&evt);

    //// 等待解码线程退出，但最多 3 秒，防止阻塞 UI
    //Uint32 start = SDL_GetTicks();
    //while (g_vs) {
    //    SDL_Delay(10);
    //    if (SDL_GetTicks() - start > 3000) {
    //        av_log(NULL, AV_LOG_WARNING, "FF_Stop timeout, force exit\n");
    //        break;
    //    }
    //}

    VideoState* is = g_vs;

    // 设置终止标志
    is->abort_request = 1;

    // 推送退出事件
    SDL_Event evt;
    evt.type = FF_QUIT_EVENT;
    evt.user.data1 = is;
    SDL_PushEvent ( &evt );

    // 直接等待读取线程结束
    if ( is->read_tid ) {
        SDL_WaitThread ( is->read_tid, NULL );
        is->read_tid = NULL;
        }
}

void FF_Seek(double pos_sec)
{
    if (!g_vs) return;
    int64_t ts = (int64_t)(pos_sec * AV_TIME_BASE);
    /* 允许 ffmpeg 在 ±0.5 秒内选择最近的关键帧，减小"回跳" */
    int64_t rel = AV_TIME_BASE / 2;  /* 0.5s */
    stream_seek(g_vs, ts, rel, 0);
}

/* 当前文件总时长（秒），若未知返回 -1 */
double FF_GetDuration( )
    {
    if (!g_vs || !g_vs->ic) return -1.0;
    if (g_vs->ic->duration <= 0) return -1.0;
    return g_vs->ic->duration / ( double ) AV_TIME_BASE;
    }

/* 当前播放时间（秒），用 master clock 计算 */
double FF_GetCurrentTime( )
    {
    if (!g_vs || !g_vs->ic) return 0.0;
    return get_master_clock( g_vs );   // ffplay 原有函数
    }

/* 音量控制：percent 取值 0~100 */
void FF_SetVolume(int percent)
{
    if (!g_vs) return;
    if (percent < 0) percent = 0;
    if (percent > 100) percent = 100;
    g_vs->audio_volume = SDL_MIX_MAXVOLUME * percent / 100;
}

int FF_GetVolume()
{
    if (!g_vs) return 0;
    return g_vs->audio_volume * 100 / SDL_MIX_MAXVOLUME;
}

/* 全屏切换：on=1 进入全屏；on=0 退出全屏 */
void FF_SetFullScreen(int on)
{
    if (!window) return;
    if (on) {
        is_full_screen = 1;
        SDL_SetWindowFullscreen(window, SDL_WINDOW_FULLSCREEN_DESKTOP);
    } else {
        is_full_screen = 0;
        SDL_SetWindowFullscreen(window, 0);
    }
}
#endif



const char program_name[] = "ffplay";
const int program_birth_year = 2003;








/* options specified by the user */
static const AVInputFormat *file_iformat;
char *input_filename;
const char *window_title;
int default_width  = 640;
int default_height = 480;
int screen_width  = 0;
int screen_height = 0;
int screen_left = SDL_WINDOWPOS_CENTERED;
int screen_top = SDL_WINDOWPOS_CENTERED;
int audio_disable;
int video_disable;


int seek_by_bytes = -1;

int display_disable;
static int borderless;
static int alwaysontop;

int show_status = -1;
int64_t start_time = AV_NOPTS_VALUE;

int filter_nbthreads = 0;


int decoder_reorder_pts = -1;




int framedrop = -1;


char *audio_codec_name;
char *subtitle_codec_name;
char *video_codec_name;
double rdftspeed = 0.02;


char **vfilters_list = NULL;
int nb_vfilters = 0;
char *afilters = NULL;


static int enable_vulkan = 0;
static char *vulkan_params = NULL;
const char *hwaccel = NULL;

/* current context */
int is_full_screen;
int64_t audio_callback_time;



SDL_Window *window;
SDL_Renderer *renderer;
SDL_RendererInfo renderer_info = {0};
SDL_AudioDeviceID audio_dev;

VkRenderer *vk_renderer;

static void sigterm_handler(int sig)
{
    exit(123);
}

static int decoder_start(Decoder *d, int (*fn)(void *), const char *thread_name, void* arg)
{
    packet_queue_start(d->queue);
    d->decoder_tid = SDL_CreateThread(fn, thread_name, arg);
    if (!d->decoder_tid) {
        av_log(NULL, AV_LOG_ERROR, "SDL_CreateThread(): %s\n", SDL_GetError());
        return AVERROR(ENOMEM);
    }
    return 0;
}

static void gui_log_callback( void* ptr , int level , const char* fmt , va_list vl )
    {
    if (level > av_log_get_level( )) return;
    char buf [ 1024 ] = { 0 };
    int print_prefix = 1;
    av_log_format_line( ptr , level , fmt , vl , buf , sizeof( buf ) , &print_prefix );
    OutputDebugStringA( buf );
    }

int PlayOpen( char* fileName )
    {
    /* 若已有播放实例在运行，先停止并等待完全退出 */
    if (g_vs) {
        FF_Stop();
    }

    int flags , ret;
    VideoState* is;

    init_dynload( );
    av_log_set_level( AV_LOG_INFO );   // 播放前调用，或在 PlayOpen 开头加
    show_status = 0;                 // 让 ffplay 打印缓冲/时钟状态
    av_log_set_flags( AV_LOG_SKIP_REPEATED );
    av_log_set_callback( gui_log_callback );
 
    /* register all codecs, demux and protocols */
#if CONFIG_AVDEVICE
    // avdevice_register_all( );
#endif
    avformat_network_init( );

    input_filename = av_strdup(fileName);
    if (!input_filename) {
        av_log(NULL, AV_LOG_FATAL, "Cannot allocate input filename string\n");
        return 1;
    }

    if (display_disable) {
        video_disable = 1;
        }
        
    flags = SDL_INIT_VIDEO | SDL_INIT_AUDIO | SDL_INIT_TIMER;

    if (audio_disable)
        flags &= ~SDL_INIT_AUDIO;  /* ALSA 相关补丁已删除，Windows 下无此问题 */
    else {
        /* Try to work around an occasional ALSA buffer underflow issue when the
         * period size is NPOT due to ALSA resampling by forcing the buffer size. */
        if (!SDL_getenv( "SDL_AUDIO_ALSA_SET_BUFFER_SIZE" ))
            SDL_setenv( "SDL_AUDIO_ALSA_SET_BUFFER_SIZE" , "1" , 1 );
        }

    if (display_disable)
        flags &= ~SDL_INIT_VIDEO;

    if (SDL_Init( flags )) {
        av_log( NULL , AV_LOG_FATAL , "Could not initialize SDL - %s\n" , SDL_GetError( ) );
        av_log( NULL , AV_LOG_FATAL , "(Did you set the DISPLAY variable?)\n" );
        return 1;
        }

    SDL_EventState( SDL_SYSWMEVENT , SDL_IGNORE );
    SDL_EventState( SDL_USEREVENT , SDL_IGNORE );

    if (!display_disable) {
#ifdef _WIN32
        /* 如果调用方已经传入外部窗口句柄，则直接基于该 HWND 创建 SDL_Window */
        if (g_externalHwnd) {
            window = SDL_CreateWindowFrom((void*)g_externalHwnd);
            if (!window) {
                av_log(NULL, AV_LOG_FATAL, "Failed to wrap external window: %s", SDL_GetError());
                do_exit(NULL);
            }
            RECT rc;
            GetClientRect(g_externalHwnd, &rc);
            default_width  = screen_width  = rc.right  - rc.left;
            default_height = screen_height = rc.bottom - rc.top;
        }
#endif
        int flags = SDL_WINDOW_HIDDEN;

        if (alwaysontop)
#if SDL_VERSION_ATLEAST(2,0,5)
            flags |= SDL_WINDOW_ALWAYS_ON_TOP;
#else
            av_log( NULL , AV_LOG_WARNING , "Your SDL version doesn't support SDL_WINDOW_ALWAYS_ON_TOP. Feature will be inactive.\n" );
#endif
        if (borderless)
            flags |= SDL_WINDOW_BORDERLESS;
        else
            flags |= SDL_WINDOW_RESIZABLE;

#ifdef SDL_HINT_VIDEO_X11_NET_WM_BYPASS_COMPOSITOR
        SDL_SetHint( SDL_HINT_VIDEO_X11_NET_WM_BYPASS_COMPOSITOR , "0" );
#endif
        if (hwaccel && !enable_vulkan) {
            av_log( NULL , AV_LOG_INFO , "Enable vulkan renderer to support hwaccel %s\n" , hwaccel );
            enable_vulkan = 1;
            }

        if (enable_vulkan) {
            vk_renderer = vk_get_renderer( );
            if (vk_renderer) {
#if SDL_VERSION_ATLEAST(2, 0, 6)
                flags |= SDL_WINDOW_VULKAN;
#endif
                }
            else {
                av_log( NULL , AV_LOG_WARNING , "Doesn't support vulkan renderer, fallback to SDL renderer\n" );
                enable_vulkan = 0;
                }
            }

        if (!window)
            window = SDL_CreateWindow( program_name , SDL_WINDOWPOS_UNDEFINED , SDL_WINDOWPOS_UNDEFINED , default_width , default_height , flags );

        SDL_SetHint( SDL_HINT_RENDER_SCALE_QUALITY , "linear" );

        if (!window) {
            av_log( NULL , AV_LOG_FATAL , "Failed to create window: %s" , SDL_GetError( ) );
            do_exit( NULL );
            }

        if (vk_renderer) {
            AVDictionary* dict = NULL;

            if (vulkan_params) {

                int ret = av_dict_parse_string( &dict , vulkan_params , "=" , ":" , 0 );

                if (ret < 0) {
                    av_log( NULL , AV_LOG_FATAL , "Failed to parse, %s\n" , vulkan_params );
                    do_exit( NULL );
                    }
                }

            ret = vk_renderer_create( vk_renderer , window , dict );
            av_dict_free( &dict );

            if (ret < 0) {
                av_log( NULL , AV_LOG_FATAL , "Failed to create vulkan renderer, %s\n" , av_err2str( ret ) );
                do_exit( NULL );
                }
            }
        else {

            renderer = SDL_CreateRenderer( window , -1 , SDL_RENDERER_ACCELERATED | SDL_RENDERER_PRESENTVSYNC );
            
            if (!renderer) {
                av_log( NULL , AV_LOG_WARNING , "Failed to initialize a hardware accelerated renderer: %s\n" , SDL_GetError( ) );
                renderer = SDL_CreateRenderer( window , -1 , 0 );
                }

            if (renderer) {
                if (!SDL_GetRendererInfo( renderer , &renderer_info ))
                    av_log( NULL , AV_LOG_VERBOSE , "Initialized %s renderer.\n" , renderer_info.name );
                }

            if (!renderer || !renderer_info.num_texture_formats) {
                av_log( NULL , AV_LOG_FATAL , "Failed to create window or renderer: %s" , SDL_GetError( ) );
                do_exit( NULL );
                }
            }
        }

    is = stream_open( input_filename , file_iformat );

    if (!is) {
        av_log( NULL , AV_LOG_FATAL , "Failed to initialize VideoState!\n" );
        ff_dispatch_event( FF_EVT_OPEN_FAIL , 0 , 0 );
        do_exit( NULL );
        }

    g_vs = is; // 暴露给外部控制接口

    ff_dispatch_event( FF_EVT_OPEN_OK , 0 , 0 );

    event_loop( is );

    /* never returns */

    return 0;
    }

/* ============================================================
 * 扩展 API 实现
 * ==========================================================*/
static FFEventCB g_event_cb = NULL;
static void*     g_event_ud = NULL;



const char* FF_StrError( int err )
    {
    switch (err) {
        case FFERR_OK:        return "ok";
        case FFERR_NOFILE:    return "file not found";
        case FFERR_UNSUPPORT: return "unsupported";
        case FFERR_RANGE:     return "out of range";
        default:              return "fail";
        }
    }

void FF_SetEventCallback( FFEventCB cb , void* userdata )
    {
    g_event_cb = cb;
    g_event_ud = userdata;
    }

static void ff_dispatch_event( FFEventID id , int64_t p1 , int64_t p2 )
    {
    if (g_event_cb)
        g_event_cb( id , p1 , p2 , g_event_ud );
    }

/* 新增内部辅助函数：用于在库内部统一分发播放结束事件 */
double g_stop_pos_sec = -1.0;
int g_stop_reason = 0; /* 0 = normal/end-of-file/user stop; 1 = reach scheduled stop */

void FF_Internal_PlayEnd( void )
    {
    ff_dispatch_event( FF_EVT_PLAY_END , g_stop_reason , 0 );
    g_stop_reason = 0; /* reset */
    }

/* 内部辅助函数：Seek 完成后由解码线程调用 */
void FF_Internal_SeekDone( void )
    {
    ff_dispatch_event( FF_EVT_SEEK_DONE , 0 , 0 );
    }

int FF_Close( void )
    {
    if ( !g_vs ) {
        return FFERR_FAIL;
        }

    /* 模仿 FF_Stop 但不退出 SDL/FFmpeg 全局 */
    SDL_Event evt; 
    evt.type = FF_QUIT_EVENT; 
    evt.user.data1 = g_vs; 
    SDL_PushEvent(&evt);
    while ( g_vs ) {
        SDL_Delay ( 10 );
        }
    ff_dispatch_event( FF_EVT_PLAY_END , 0 , 0 );
    return FFERR_OK;
    }

int FF_SeekRelative( double delta_sec )
    {
    if (!g_vs) return FFERR_FAIL;
    double pos = get_master_clock( g_vs );
    if (isnan(pos)) pos = 0.0;
    pos += delta_sec;
    if (pos < 0) pos = 0;
    int64_t ts  = (int64_t)( pos * AV_TIME_BASE );
    int64_t rel = (int64_t)( delta_sec * AV_TIME_BASE );
    stream_seek( g_vs , ts , rel , 0 );
    return FFERR_OK;
    }

int FF_SetMute( int on )
    {
    if (!g_vs) return FFERR_FAIL;
    g_vs->muted = on ? 1 : 0;
    return FFERR_OK;
    }

int FF_GetMute( void )
    {
    return ( g_vs && g_vs->muted ) ? 1 : 0;
    }

int FF_GetTrackCount( FFTrackType type )
{
    if (!g_vs || !g_vs->ic) return 0;
    enum AVMediaType mt;
    switch (type) {
        case FF_TRACK_AUDIO:   mt = AVMEDIA_TYPE_AUDIO;    break;
        case FF_TRACK_VIDEO:   mt = AVMEDIA_TYPE_VIDEO;    break;
        case FF_TRACK_SUBTITLE:mt = AVMEDIA_TYPE_SUBTITLE; break;
        default: return 0;
    }
    int cnt = 0;
    for (unsigned i = 0; i < g_vs->ic->nb_streams; ++i)
        if (g_vs->ic->streams[i]->codecpar->codec_type == mt)
            ++cnt;
    return cnt;
}

int FF_GetCurrentTrack( FFTrackType type )
{
    if (!g_vs) return -1;
    switch (type) {
        case FF_TRACK_AUDIO:   return g_vs->audio_stream;
        case FF_TRACK_VIDEO:   return g_vs->video_stream;
        case FF_TRACK_SUBTITLE:return g_vs->subtitle_stream;
        default: return -1;
    }
}

int FF_SelectTrack( FFTrackType type , int index )
{
    if (!g_vs || !g_vs->ic) return FFERR_FAIL;
    int cur = FF_GetCurrentTrack( type );
    if (index == cur) return FFERR_OK;
    if (index < -1) return FFERR_RANGE;
    enum AVMediaType mt;
    switch (type) {
        case FF_TRACK_AUDIO:   mt = AVMEDIA_TYPE_AUDIO;    break;
        case FF_TRACK_VIDEO:   mt = AVMEDIA_TYPE_VIDEO;    break;
        case FF_TRACK_SUBTITLE:mt = AVMEDIA_TYPE_SUBTITLE; break;
        default: return FFERR_RANGE;
    }
    if (index >= 0 && (unsigned)index >= g_vs->ic->nb_streams) return FFERR_RANGE;
    if (index >= 0 && g_vs->ic->streams[index]->codecpar->codec_type != mt) return FFERR_RANGE;

    /* 关闭旧流 */
    stream_component_close( g_vs , cur );
    /* 打开新流（index==-1 时只关闭） */
    if (index >= 0) {
        if (stream_component_open( g_vs , index ) < 0) return FFERR_FAIL;
    }

    if (g_event_cb) ff_dispatch_event( FF_EVT_TRACK_CHANGED , type , index );
    return FFERR_OK;
}


/* 当前播放速度（0.5~2.0），默认 1.0 */
static double g_playback_rate = 1.0;

/*
 * 加载外挂字幕文件（目前仅支持 ASS/SSA/SRT，由 libass 解析）。
 * 参数：UTF-16LE 路径；
 * 返回：FFERR_OK / FFERR_NOFILE / FFERR_UNSUPPORT / FFERR_FAIL。
 */
int FF_LoadSubtitle( const wchar_t* path )
    {
    if (!path || !*path)
        return FFERR_RANGE;

    int ret = subtitle_open( path );

    if (ret == 0) {
        /* 成功后通知上层（可选），此处使用 TRACK_CHANGED 事件 */
        ff_dispatch_event( FF_EVT_TRACK_CHANGED , FF_TRACK_SUBTITLE , 0 );
        return FFERR_OK;
        }
    /* 根据 subtitle_open 的错误码做简单映射 */
    if (ret == -1)
        return FFERR_NOFILE;
    else if (ret == -2)
        return FFERR_UNSUPPORT; /* 无法创建 renderer */
    else if (ret == -3)
        return FFERR_FAIL;      /* 解析失败 */

    return FFERR_FAIL;
    }

/* 设置播放倍速（0.5~2.0）。仅调整时钟速度，不做变调处理。*/
int FF_SetPlaybackRate( double rate )
    {
    if (rate < 0.5 || rate > 2.0)
        return FFERR_RANGE;

    g_playback_rate = rate;

    if (g_vs) {
        set_clock_speed( &g_vs->audclk , rate );
        set_clock_speed( &g_vs->vidclk , rate );
        set_clock_speed( &g_vs->extclk , rate );
        }

    return FFERR_OK;
    }

double FF_GetPlaybackRate( void )
    {
    return g_playback_rate;
    }

/* 将当前视频帧保存为 PNG 文件 */
int FF_CaptureBitmapPNG( const wchar_t* path )
    {
    if (!g_vs || !path || !*path)
        return FFERR_FAIL;

    /* 取最近已显示的视频帧 */
    Frame* vp = frame_queue_peek_last( &g_vs->pictq );
    if (!vp || !vp->frame)
        return FFERR_FAIL;

    AVFrame* src = vp->frame;
    int width  = src->width;
    int height = src->height;

    /* 创建 SWS 上下文，将像素转换为 RGB24 */
    struct SwsContext* sws = sws_getContext( width , height , ( enum AVPixelFormat ) src->format ,
        width , height , AV_PIX_FMT_RGB24 , SWS_BILINEAR , NULL , NULL , NULL );
    if (!sws)
        return FFERR_FAIL;

    uint8_t* rgb_data [ 4 ];
    int      rgb_linesize [ 4 ];
    if (av_image_alloc( rgb_data , rgb_linesize , width , height , AV_PIX_FMT_RGB24 , 1 ) < 0) {
        sws_freeContext( sws );
        return FFERR_FAIL;
        }

    sws_scale( sws , ( const uint8_t* const* ) src->data , src->linesize , 0 , height , rgb_data , rgb_linesize );
    sws_freeContext( sws );

    /* 使用 FFmpeg PNG 编码器 */
    const AVCodec* codec = avcodec_find_encoder( AV_CODEC_ID_PNG );
    if (!codec) {
        av_freep( &rgb_data [ 0 ] );
        return FFERR_UNSUPPORT;
        }

    AVCodecContext* c = avcodec_alloc_context3( codec );
    if (!c) { av_freep( &rgb_data [ 0 ] ); return FFERR_FAIL; }
    c->width  = width;
    c->height = height;
    c->pix_fmt = AV_PIX_FMT_RGB24;
    c->time_base = ( AVRational ) { 1 , 25 };

    if (avcodec_open2( c , codec , NULL ) < 0) {
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_FAIL;
        }

    AVFrame* frame = av_frame_alloc( );
    frame->format = AV_PIX_FMT_RGB24;
    frame->width  = width;
    frame->height = height;
    if (av_image_fill_arrays( frame->data , frame->linesize , rgb_data [ 0 ] , AV_PIX_FMT_RGB24 , width , height , 1 ) < 0) {
        av_frame_free( &frame );
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_FAIL;
        }

    AVPacket* pkt = av_packet_alloc( );
    if (!pkt) {
        av_frame_free( &frame );
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_FAIL;
        }

    int ret = avcodec_send_frame( c , frame );
    if (ret == 0)
        ret = avcodec_receive_packet( c , pkt );

    if (ret < 0) {
        av_packet_free( &pkt );
        av_frame_free( &frame );
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_FAIL;
        }

    /* UTF-16 路径转 UTF-8 */
    int lenUtf8 = WideCharToMultiByte( CP_UTF8 , 0 , path , -1 , NULL , 0 , NULL , NULL );
    char* filename = ( char* ) av_malloc( lenUtf8 );
    if (!filename) {
        av_packet_free( &pkt );
        av_frame_free( &frame );
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_FAIL;
        }
    WideCharToMultiByte( CP_UTF8 , 0 , path , -1 , filename , lenUtf8 , NULL , NULL );

    FILE* fp = fopen( filename , "wb" );
    if (!fp) {
        av_free( filename );
        av_packet_free( &pkt );
        av_frame_free( &frame );
        avcodec_free_context( &c );
        av_freep( &rgb_data [ 0 ] );
        return FFERR_NOFILE;
        }

    fwrite( pkt->data , 1 , pkt->size , fp );
    fclose( fp );

    /* 清理资源 */
    av_free( filename );
    av_packet_free( &pkt );
    av_frame_free( &frame );
    avcodec_free_context( &c );
    av_freep( &rgb_data [ 0 ] );

    return FFERR_OK;
    }

char g_watermark_desc[512] = { 0 }; /* drawtext 参数缓存 */

/* 内部工具：将 Windows 路径中的 '\' 转成 '/'，并替换单引号为转义 */
static void sanitize_path( const char* src , char* dst , int dst_size )
    {
    int j = 0;
    for (int i = 0; src && src[i] && j < dst_size - 1; ++i) {
        char c = src[i];
        if (c == '\\') c = '/';
        if (c == '\'') {
            if (j < dst_size - 2) {
                dst[j++] = '\\';
            }
        }
        dst[j++] = c;
    }
    dst[j] = 0;
    }

// 新增函数：获取系统字体文件夹路径（UTF-8），返回动态分配字符串（需 av_free）
static char* GetSystemFontsDir( void )
    {
    PWSTR wpath = NULL;
    HRESULT hr = SHGetKnownFolderPath( &FOLDERID_Fonts , 0 , NULL , &wpath );
    if (FAILED( hr )) {
        av_log( NULL , AV_LOG_WARNING , "Failed to get fonts folder: %d\n" , hr );
        return NULL;  // 或回退到 getenv("WINDIR") + "\\Fonts"
        }

    // 将 UTF-16 转换为 UTF-8
    int lenUtf8 = WideCharToMultiByte( CP_UTF8 , 0 , wpath , -1 , NULL , 0 , NULL , NULL );
    if (lenUtf8 <= 0) {
        CoTaskMemFree( wpath );
        return NULL;
        }

    char* utf8_path = ( char* ) av_malloc( lenUtf8 );
    if (!utf8_path) {
        CoTaskMemFree( wpath );
        return NULL;
        }

    WideCharToMultiByte( CP_UTF8 , 0 , wpath , -1 , utf8_path , lenUtf8 , NULL , NULL );
    CoTaskMemFree( wpath );  // 释放 Shell API 内存

    // 将路径中的 '\' 替换为 '/' 以匹配 FFmpeg 期望
    for (char* p = utf8_path; *p; ++p) {
        if (*p == '\\') *p = '/';
        }

    return utf8_path;
    }

/* 生成 drawtext 描述字符串 */
static void build_watermark_desc( const char* utf8_text , int position , const char* color , double alpha , const char* fontfile , int fontsize )
    {
    if (!utf8_text || !*utf8_text) {
        g_watermark_desc[0] = '\0';
        return;
    }

    if (!color || !*color) {
        color = "white";
        }

    // 修改：如果 fontfile 为空，动态获取系统字体目录并拼接默认字体
    char default_fontfile [ 260 ] = { 0 };
    if (!fontfile || !*fontfile) {
        char* fonts_dir = GetSystemFontsDir( ); // 新增函数调用
        if (fonts_dir) {
            // 假设默认字体为 simsun.ttc，可自定义
            snprintf( default_fontfile , sizeof( default_fontfile ) , "%s/msyh.ttc" , fonts_dir );
            fontfile = default_fontfile;
            av_free( fonts_dir ); // 释放动态内存
            }
        else {
            // 回退到硬编码默认
            fontfile = "c:/windows/fonts/msyh.ttc";
            }

        // 检查 msyh.ttc 是否存在；不存在则回退到 msyh.ttf
        DWORD attrs = GetFileAttributesA( fontfile );
        if (attrs == INVALID_FILE_ATTRIBUTES) {
            if (fontfile == default_fontfile) {
                size_t len = strlen( default_fontfile );
                if (len >= 4 && _stricmp( default_fontfile + len - 4 , ".ttc" ) == 0) {
                    memcpy( default_fontfile + len - 4 , ".ttf" , 4 ); // 原地改后缀
                    }
                else {
                    snprintf( default_fontfile , sizeof( default_fontfile ) , "%s" , "c:/windows/fonts/msyh.ttf" );
                    }
                fontfile = default_fontfile;
                }
            else {
                // 硬编码默认路径，生成可写的 .ttf 路径
                snprintf( default_fontfile , sizeof( default_fontfile ) , "%s" , "c:/windows/fonts/msyh.ttf" );
                fontfile = default_fontfile;
                }
            }
        }

    /* 处理路径转义 */
    char fontfile_buf [ 260 ] = { 0 };
    sanitize_path( fontfile , fontfile_buf , sizeof( fontfile_buf ) );

    /* 位置表达式 */
    const char* expr_x = "10";
    const char* expr_y = "10";
    if (position == 1) {
        expr_x = "w-tw-10";
        expr_y = "h-th-10";
    }

    /* 字体大小 */
    if (fontsize <= 0) {
        fontsize = 24;
        }

    /* fontcolor+alpha */
    char fontcolor[64] = {0};
    if (alpha >= 1.0) {
        snprintf( fontcolor , sizeof( fontcolor ) , "%s" , color );
        }
    else {
        snprintf( fontcolor , sizeof( fontcolor ) , "%s@%.2f" , color , alpha );
        }

    snprintf( g_watermark_desc , sizeof( g_watermark_desc ) ,
        "fontfile='%s':text='%s':fontsize=%d:fontcolor=%s:x=%s:y=%s" ,
        fontfile_buf , utf8_text , fontsize , fontcolor , expr_x , expr_y );
    }

int FF_SetWatermarkTextEx( const char* text , 
    int position , 
    const char* color , 
    double alpha , 
    const char* fontfile , 
    int fontsize )
    {
    build_watermark_desc( text , position , color , alpha , fontfile , fontsize );
    return FFERR_OK;
    }

/* 保留旧接口，转调新实现，fontfile 设 nullptr 使用默认 */
int FF_SetWatermarkText( const char* text , int position , const char* color , double alpha )
    {
    return FF_SetWatermarkTextEx( text , position , color , alpha , NULL , 24 );
    }

/* 辅助：将 UTF-16 文本转换为 UTF-8 到指定缓冲区。返回生成的字节数（含终止符）。*/
static int wchar_to_utf8_buf( const wchar_t* ws , char* dst , int dst_size )
    {
    if (!ws || !*ws || dst_size <= 0) {
        if (dst_size>0) dst[0] = 0;
        return 0;
    }
    int len = WideCharToMultiByte( CP_UTF8 , 0 , ws , -1 , NULL , 0 , NULL , NULL );
    if (len <= 0 || len > dst_size) {
        dst[0] = 0;
        return 0;
    }
    WideCharToMultiByte( CP_UTF8 , 0 , ws , -1 , dst , dst_size , NULL , NULL );
    return len;
    }

int FF_SetWatermarkTextExW( const wchar_t* wtext , int position , const char* color , double alpha , const wchar_t* wfontfile , int fontsize )
    {
    char utf8_text[1024] = {0};
    char utf8_font[260]  = {0};

    wchar_to_utf8_buf( wtext , utf8_text , sizeof( utf8_text ) );
    wchar_to_utf8_buf( wfontfile , utf8_font , sizeof( utf8_font ) );

    const char* pTxt  = utf8_text[0] ? utf8_text : NULL;
    const char* pFont = utf8_font[0]? utf8_font : NULL;

    build_watermark_desc( pTxt , position , color , alpha , pFont , fontsize );
    return FFERR_OK;
    }

int FF_SetWatermarkTextW( const wchar_t* wtext , int position , const char* color , double alpha )
    {
    return FF_SetWatermarkTextExW( wtext , position , color , alpha , NULL , 24 );
    }

/* 自动停止播放时间点（秒）；<0 表示未启用 */
int FF_SetStopPosition( double seconds )
    {
    g_stop_pos_sec = seconds;
    return FFERR_OK;
    }