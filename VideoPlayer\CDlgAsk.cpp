﻿// CDlgAsk.cpp: 实现文件
//

#include "pch.h"
#include "VideoPlayer.h"
#include "CDlgAsk.h"
#include "afxdialogex.h"
#include <random>
#include <chrono>


// CDlgAsk 对话框

IMPLEMENT_DYNAMIC(CDlgAsk, CDialogEx)

CDlgAsk::CDlgAsk(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_ASK, pParent)
{

}

CDlgAsk::~CDlgAsk()
{
}

void CDlgAsk::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange( pDX );
	DDX_Control( pDX , IDC_STATIC_ASK , m_ask );
	DDX_Control( pDX , IDC_STATIC_1 , m_answer1 );
	DDX_Control( pDX , IDC_STATIC_3 , m_answer2 );
	DDX_Control( pDX , IDC_STATIC_4 , m_answer3 );
	}


BEGIN_MESSAGE_MAP(CDlgAsk, CDialogEx)
	ON_CONTROL(STN_CLICKED, IDC_STATIC_1, &CDlgAsk::OnClickedAnswer1)
	ON_CONTROL(STN_CLICKED, IDC_STATIC_3, &CDlgAsk::OnClickedAnswer2)
	ON_CONTROL(STN_CLICKED, IDC_STATIC_4, &CDlgAsk::OnClickedAnswer3)
END_MESSAGE_MAP()


// CDlgAsk 消息处理程序

void CDlgAsk::HandleAnswer(int nCtrlID)
	{
	if (nCtrlID == m_correctCtrlID)
		{
		EndDialog( IDOK );
		return;
		}

	++m_errorCount;

	if (m_errorCount >= 3)
		{
		EndDialog( IDCANCEL );
		}
	}

void CDlgAsk::OnClickedAnswer1() { HandleAnswer( IDC_STATIC_1 ); }
void CDlgAsk::OnClickedAnswer2() { HandleAnswer( IDC_STATIC_3 ); }
void CDlgAsk::OnClickedAnswer3() { HandleAnswer( IDC_STATIC_4 ); }

BOOL CDlgAsk::OnInitDialog( )
	{
	CDialogEx::OnInitDialog( );

	// 随机数生成器
	std::mt19937 rng( static_cast< uint32_t >( std::chrono::high_resolution_clock::now( ).time_since_epoch( ).count( ) ) );
	std::uniform_int_distribution<int> distDigit( 0 , 9 );
	std::uniform_int_distribution<int> distOp( 0 , 1 ); // 0:+ 1:-

	int a = distDigit( rng );
	int b = distDigit( rng );

	TCHAR opChar = _T('+');
	if ( distOp( rng ) == 1 )
		{
		// subtraction ensure non-negative
		if (a < b) std::swap( a , b );
		opChar = _T('-');
		m_correctAnswer = a - b;
		}
	else
		{
		m_correctAnswer = a + b;
		}

	CString strQuestion;
	strQuestion.Format( _T( "%d %c %d =" ) , a , opChar , b );
	m_ask.SetCaptchaText( strQuestion );

	// 生成三个选项，其中一个正确
	std::vector<int> options;
	options.push_back( m_correctAnswer );
	while ( options.size( ) < 3 )
		{
		int val = distDigit( rng ); // 0-9 默认范围；若结果可能 >9，需要扩大，如 18
		if (opChar == '+') val = distDigit(rng) + distDigit(rng); // 0-18
		else val = distDigit(rng) - distDigit(rng); // -9-9 但后面再调整

		if (val < 0) val = -val; // 简化，保证非负

		bool exists = false;
		for (int v : options) if (v == val) { exists = true; break; }
		if (!exists) options.push_back( val );
		}

	// 打乱顺序
	std::shuffle( options.begin( ) , options.end( ) , rng );

	// 设置到控件，并记录正确答案控件 ID
	CString strOpt;
	m_correctCtrlID = 0;

	strOpt.Format( _T("%d"), options[0] );
	m_answer1.SetCaptchaText( strOpt );
	if ( options[0] == m_correctAnswer ) m_correctCtrlID = IDC_STATIC_1;

	strOpt.Format( _T("%d"), options[1] );
	m_answer2.SetCaptchaText( strOpt );
	if ( options[1] == m_correctAnswer ) m_correctCtrlID = IDC_STATIC_3;

	strOpt.Format( _T("%d"), options[2] );
	m_answer3.SetCaptchaText( strOpt );
	if ( options[2] == m_correctAnswer ) m_correctCtrlID = IDC_STATIC_4;

	// 初始化错误计数
	m_errorCount = 0;

	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
	}

BOOL CDlgAsk::PreTranslateMessage(MSG* pMsg)
	{
	if (pMsg->message == WM_KEYDOWN || pMsg->message == WM_SYSKEYDOWN)
		{
		if (pMsg->wParam == VK_RETURN || pMsg->wParam == VK_ESCAPE ||
			(pMsg->wParam == VK_F4 && (pMsg->lParam & (1 << 29)))) // Alt+F4
			{
			return TRUE; // 吞掉
			}
		}
	return CDialogEx::PreTranslateMessage( pMsg );
	}

void CDlgAsk::OnOK()   { /* 屏蔽回车关闭 */ }
void CDlgAsk::OnCancel(){ /* 屏蔽 ESC 关闭 */ }

// 屏蔽系统菜单关闭（Alt+F4 等）
BOOL CDlgAsk::OnCommand(WPARAM wParam, LPARAM lParam)
	{
	if (wParam == SC_CLOSE)
		{
		return TRUE; // 屏蔽
		}
	return CDialogEx::OnCommand( wParam , lParam );
	}
