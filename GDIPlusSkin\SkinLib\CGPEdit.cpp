﻿#include "pch.h"
#include "CGPEdit.h"
#include "GPImageInfo.h"
#include "command/MemoryDC.h"
#include "command/Functions.h"

CGPEdit::CGPEdit( ) : m_rectNCBottom( 0 , 0 , 0 , 0 )
, m_rectNCTop( 0 , 0 , 0 , 0 )
, m_rectNCLeft( 0 , 0 , 0 , 0 )
, m_rectNCRight( 0 , 0 , 0 , 0 )
    {
    m_c = ( ( !IsWindowEnabled( ) ) || ( ( GetStyle( )&ES_READONLY ) == ES_READONLY ) ) ? GetSysColor( COLOR_BTNFACE ) : GetSysColor( COLOR_WINDOW) ;
    m_Brush.CreateSolidBrush( GetSysColor( COLOR_WINDOW ) );
    }


CGPEdit::~CGPEdit( )
    {
    }

BEGIN_MESSAGE_MAP( CGPEdit , CEdit )
    ON_WM_NCCALCSIZE( )
    ON_WM_NCPAINT( )
    ON_WM_CTLCOLOR_REFLECT( )
    //ON_WM_ERASEBKGND( )
END_MESSAGE_MAP( )

void CGPEdit::OnNcCalcSize( BOOL bCalcValidRects , NCCALCSIZE_PARAMS FAR* lpncsp )
    {
    CEdit::OnNcCalcSize( bCalcValidRects , lpncsp );
    if ( 0 == ( GetStyle( ) &ES_MULTILINE ) ) {
        CClientDC dc( this );
        CRect rcText;
        CFont *pOld = dc.SelectObject( GetFont( ) );
        dc.DrawText( TEXT( "发8Ky" ) , rcText , DT_CALCRECT | DT_LEFT );
        int nHeight = rcText.Height( );
        dc.SelectObject( pOld );


        CRect rectWnd , rectClient;
        GetClientRect( rectClient );
        ClientToScreen( rectClient );

        GetWindowRect( rectWnd );

        UINT uiCenterOffset = ( rectClient.Height( ) - nHeight ) / 2;
        UINT uiCY = ( rectWnd.Height( ) - rectClient.Height( ) ) / 2;
        UINT uiCX = ( rectWnd.Width( ) - rectClient.Width( ) ) / 2;

        rectWnd.OffsetRect( -rectWnd.left , -rectWnd.top );
        m_rectNCTop = rectWnd;

        m_rectNCTop.DeflateRect( uiCX , uiCY , uiCX , uiCenterOffset + nHeight + uiCY );

        m_rectNCBottom = rectWnd;

        m_rectNCBottom.DeflateRect( uiCX , uiCenterOffset + nHeight + uiCY , uiCX , uiCY );

        m_rectNCLeft.top = m_rectNCTop.bottom;
        m_rectNCLeft.right = uiCX + m_rectNCTop.left;
        m_rectNCLeft.bottom = m_rectNCBottom.top;
        m_rectNCLeft.left = m_rectNCTop.left;

        m_rectNCRight.top = m_rectNCTop.bottom;
        m_rectNCRight.bottom = m_rectNCBottom.top;
        m_rectNCRight.right = m_rectNCTop.right;
        m_rectNCRight.left = m_rectNCRight.right - uiCX;

        if ( m_rectNCRight.left >= m_rectNCRight.right ) {
            m_rectNCRight.left = m_rectNCRight.right - 2;
            }



        lpncsp->rgrc[ 0 ].top += uiCenterOffset;
        lpncsp->rgrc[ 0 ].bottom -= uiCenterOffset;

        lpncsp->rgrc[ 0 ].left += uiCX;
        lpncsp->rgrc[ 0 ].right -= uiCY;
        }
    }

void CGPEdit::OnNcPaint( )
    {
    Default( );

    if ( 0 == ( GetStyle( ) &ES_MULTILINE ) ) {
        CWindowDC dc( this );

        dc.FillRect( m_rectNCBottom , &m_Brush );
        dc.FillRect( m_rectNCTop , &m_Brush );
        dc.FillRect( m_rectNCLeft , &m_Brush );
        dc.FillRect( m_rectNCRight , &m_Brush );
        Invalidate( TRUE );
        }

    CRect rcWnd;
    GetWindowRect( &rcWnd );

    CWnd *pParent = GetParent( );
    pParent->ScreenToClient( &rcWnd );
    CDC *pDC = pParent->GetDC( );

    COLORREF crPen = RGB( 222 , 222 , 222 );
    CPen aPen;
    aPen.CreatePen( PS_SOLID , 1 , crPen );
    CPen* pOldPen = pDC->SelectObject( &aPen );

    CBrush* oldbrush = ( CBrush* ) pDC->SelectStockObject( NULL_BRUSH );

    pDC->Rectangle( &rcWnd );
    pDC->SelectObject( pOldPen );

    pDC->SelectObject( oldbrush );
    }

HBRUSH CGPEdit::CtlColor( CDC* pDC , UINT nCtlColor )
    {
    UNREFERENCED_PARAMETER( nCtlColor);

    if ( 0 == ( GetStyle( ) &ES_MULTILINE ) ) {
        if ( m_rectNCTop.IsRectEmpty( ) ) {
            SetWindowPos( NULL , 0 , 0 , 0 , 0 , SWP_NOOWNERZORDER | SWP_NOSIZE | SWP_NOMOVE | SWP_FRAMECHANGED );
            }
        }
    
    pDC->SetBkColor( GetSysColor( COLOR_WINDOW ) );
    return m_Brush;
    }

//
//BOOL CGPEdit::OnEraseBkgnd( CDC* pDC )
//    {
//    BOOL	bStatus = CEdit::OnEraseBkgnd( pDC );
//    if ( bStatus ) {
//        CClientDC	dcDraw( this );
//        int			iState = dcDraw.SaveDC( );
//        CRect		rRect;
//        GetClientRect( &rRect );
//        dcDraw.FillRect( rRect , &m_Brush );
//        dcDraw.RestoreDC( iState );
//       }
//    return bStatus;
//    }