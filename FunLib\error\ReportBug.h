#pragma once
#include "..\sysTools\GetSysInfo.h"

class CReportBug
	{
	public:
		CReportBug();
		~CReportBug();

        void GetSoftVerInfo( LPTSTR lpSoftVer , const DWORD dwSize , LPCTSTR lpSoftID , const float & fSoftVer );

        void GetHID( LPTSTR lpHID , DWORD dwSize , LPCTSTR lpHead , int iIndex = -1 );

        double GetSoftNewVer( void );
        double GetSoftPrice( void );

		void SetLogInfo(LPCTSTR lpszDirName, LPCTSTR lpszFleName);

		void SetSoftInfo(LPCTSTR lpszSoftID, const float &fVer);

        void HandleBug( LPCTSTR lpCodeName , LPCTSTR lpFunName, const int &iLine , 
                        const DWORD & dwError , LPCTSTR lpInfo = NULL);

        void HandleBug( LPCTSTR lpCodeName , LPCTSTR lpFunName , const int &iLine , 
                        LPCTSTR lpInfo = NULL );

		static unsigned __stdcall  ReportBugThraed(LPVOID lParam);

        LPCTSTR GetUserAgent( void )
            {
            return m_szUserAgent;
            }

		DWORD ReportBug();
        DWORD ReportSU(BOOL bIsSetup, LPCTSTR lpTime);
        BOOL  IsVistaLast( void )
            {
            return m_osve > OSVersionEnum::WIN_VISTA;
            }

        BOOL   Is64( void )
            {
            return ( 64 == m_iSysBit) ? TRUE : FALSE;
            }

        OSVersionEnum GetOSVE( void )
             {
             return m_osve;
             }
	private:
        OSVersionEnum  m_osve;
        BOOL    m_bIsLaptop;
        int     m_iSysBit;
		int     m_iLine;
		BOOL    m_bEnd;
		DWORD   m_dwError;
		float   m_fSoftVer;
		TCHAR   m_szHID[36];
		TCHAR   m_szSoftID[32];
        TCHAR   m_szScrInfo[ 64 ];
        TCHAR   m_szSysName[ 64 ];
        TCHAR   m_szBrowserName[ 64 ];
		TCHAR   m_szFileName[128];
        TCHAR   m_szFunName[ 128];
		TCHAR   m_szCodeName[512];
		TCHAR   m_szLogDirName[128];
		TCHAR   m_szInfo[1024];
        TCHAR   m_szUserAgent[256];
	};

