﻿// VideoPlayerDlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "VideoPlayer.h"
#include "VideoPlayerDlg.h"
#include "CDlgInfo.h"
#include "CDlgOpenFile.h"
#include "CDlgAsk.h"
#include "CDlgWed.h"
#include <closewindows/ProcessTools.h>
#include <disk/disk32.h>
#include <fileTools/fileTools.h>
#include <string\strTools.h>
#include <net\WebViewWnd.h>
#include <KeyboardBlocker.h>
#include "..\EP-common\VECommon.h"
#include "../FunLib/include/KeyboardBlocker.h"
#include "afxdialogex.h"
#include <thread>
#include <strsafe.h>
#include <random>
#include <chrono>
#include <commctrl.h>


#define WM_USER_SEEK_DONE   (WM_USER + 1003)

static void FFEventHandler( FFEventID id , int64_t p1 , int64_t p2 , void* userdata )
    {
	CVideoPlayerDlg* pDlg = reinterpret_cast< CVideoPlayerDlg* >( userdata );
    if (!pDlg) return;

    switch (id) {
            case FF_EVT_SEEK_DONE:
                ::PostMessage( pDlg->GetSafeHwnd( ) , WM_USER_SEEK_DONE , p1 , p2);
                break;
            case FF_EVT_OPEN_OK:
                ::PostMessage( pDlg->GetSafeHwnd( ) , WM_USER + 1002 , p1 , p2);
                break;
            case FF_EVT_PLAY_END:
                // 通过 PostMessage 切换到 UI 线程
                ::PostMessage( pDlg->GetSafeHwnd( ) , WM_USER + 1001 , p1, p2 );
                break;
            default:
                break;
        }
    }

// 自定义消息：子渲染窗口点击/双击转发到父对话框
#ifndef WM_APP
#define WM_APP 0x8000
#endif
#define WM_APP_VIDEO_CLICK   (WM_APP + 200)
#define WM_APP_VIDEO_DBLCLK  (WM_APP + 201)

// 子窗口鼠标子类过程，将点击/双击转发给父窗口
static LRESULT CALLBACK VideoMouseSubclassProc( HWND hWnd , UINT uMsg , WPARAM wParam , LPARAM lParam ,
                                               UINT_PTR uIdSubclass , DWORD_PTR dwRefData )
{
    HWND hParent = reinterpret_cast< HWND >( dwRefData );
    switch (uMsg) {
    case WM_LBUTTONDOWN:
        if (hParent) ::PostMessage( hParent , WM_APP_VIDEO_CLICK , 0 , 0 );
        break;
    case WM_LBUTTONDBLCLK:
        if (hParent) ::PostMessage( hParent , WM_APP_VIDEO_DBLCLK , 0 , 0 );
        break;
    default: break;
    }
    return DefSubclassProc( hWnd , uMsg , wParam , lParam );
}

static BOOL CALLBACK EnumChildAndHookProc( HWND hwnd , LPARAM lParam )
{
    HWND hParent = reinterpret_cast< HWND >( lParam );
    SetWindowSubclass( hwnd , VideoMouseSubclassProc , 0x7788 , reinterpret_cast< DWORD_PTR >( hParent ) );
    // 继续对子孙级也挂钩
    EnumChildWindows( hwnd , EnumChildAndHookProc , lParam );
    return TRUE;
}

void InstallVideoMouseHooks( HWND hVideo , HWND hDialog )
{
    if (!hVideo || !::IsWindow( hVideo ) || !hDialog) return;
    // 钩视频窗口自身
    SetWindowSubclass( hVideo , VideoMouseSubclassProc , 0x7788 , reinterpret_cast< DWORD_PTR >( hDialog ) );
    // 钩所有子孙窗口（覆盖 D3D/DComp 子窗）
    EnumChildWindows( hVideo , EnumChildAndHookProc , reinterpret_cast< LPARAM >( hDialog ) );
}

// 用于应用程序"关于"菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
    public:
    CString m_strID;
    afx_msg void OnBnClickedOk( );
    virtual BOOL OnInitDialog( );
    };

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
, m_strID( _T( "" ) )
    {
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
    CDialogEx::DoDataExchange( pDX );
    DDX_Text( pDX , IDC_EDIT_ID , m_strID );
    }

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
    ON_BN_CLICKED( IDOK , &CAboutDlg::OnBnClickedOk )
END_MESSAGE_MAP()


// CVideoPlayerDlg 对话框



CVideoPlayerDlg::CVideoPlayerDlg( CWnd* pParent /*=nullptr*/ )
    : CDialogEx( IDD_VIDEOPLAYER_DIALOG , pParent )
    , m_hwndVideo( nullptr )  // 用于显示视频的窗口句柄
    , m_totalTime( 0.0 )
    , m_bVolOn( true )
    , m_bFullScreen( false )
    , m_bIsSeeking( false )
    , m_bIsLimitMouse( false )
    , m_bIsSizing( false )
    , m_bMonitSucess( false )
    , m_strPlayAfter( _T( "" ) )
    , m_strPlayBefore( _T( "" ) )
    , m_bIsRunOther( FALSE )
    , m_bIsPlayAsk( FALSE )
    , m_bAskDialogShowing( false )
    , m_bIsWatermark( FALSE )
    , m_nextAskTime( std::chrono::steady_clock::now( ) )
    , m_uPlayState( PlayState::Standby )
    , m_hasPrevPlacement( false )
    , m_bCommandLineAutoPlay( false )
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
    
}

void CVideoPlayerDlg::DoDataExchange(CDataExchange* pDX)
{
    CDialogEx::DoDataExchange( pDX );
    DDX_Control( pDX , IDC_BUTTON_PLAY , m_btnPlay );
    DDX_Control( pDX , IDC_BUTTON_STOP , m_btnStop );
    DDX_Control( pDX , IDC_SLIDER_POS , m_playprogress );
    DDX_Control( pDX , IDC_STATIC_TIME , m_staticTime );
    DDX_Control( pDX , IDC_BUTTON_OPEN , m_btnOpen );
    DDX_Control( pDX , IDC_BUTTON_BACK , m_btnBack );
    DDX_Control( pDX , IDC_BUTTON_FORWARD , m_btnForward );
    DDX_Control( pDX , IDC_SLIDER_VOLUME , m_sliderVolume );
    DDX_Control( pDX , IDC_BUTTON_VOL , m_btnVol );
    }

BEGIN_MESSAGE_MAP(CVideoPlayerDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_WM_CLOSE( )
	ON_WM_SIZE( )
	ON_WM_ERASEBKGND( )
	ON_WM_CTLCOLOR( )
    ON_WM_HSCROLL( )
    ON_WM_LBUTTONDBLCLK( )
    ON_WM_PARENTNOTIFY()
    ON_WM_SETFOCUS( )
    ON_WM_ACTIVATE( )
    ON_WM_TIMER( )
    ON_WM_GETMINMAXINFO( )
    ON_WM_ENTERSIZEMOVE( )
    ON_WM_EXITSIZEMOVE( )
    ON_WM_LBUTTONDOWN()


    ON_MESSAGE( WM_USER + 1001 , &CVideoPlayerDlg::OnPlaybackComplete )  // 播放完成消息
    ON_MESSAGE( WM_USER + 1002 , &CVideoPlayerDlg::OnOpenSuccess )
    ON_MESSAGE( WM_USER_SEEK_DONE , &CVideoPlayerDlg::OnSeekDone )

    ON_MESSAGE( WM_BITMAPSLIDER_MOVING , &CVideoPlayerDlg::OnSeeking )
    ON_MESSAGE( WM_BITMAPSLIDER_MOVED , &CVideoPlayerDlg::OnSeek )
    ON_MESSAGE( WM_BITMAPSLIDER_DOWN , &CVideoPlayerDlg::OnDown )

    ON_MESSAGE( WM_USER_SHOW_ASK , &CVideoPlayerDlg::OnShowAsk )
    ON_MESSAGE( UWM_FAILED_HWND , &CVideoPlayerDlg::OnFailedWnd )
    ON_MESSAGE( UWM_FAILED_PROCESS , &CVideoPlayerDlg::OnFailedProcess )


	ON_BN_CLICKED( IDC_BUTTON_PLAY , &CVideoPlayerDlg::OnBnClickedButtonPlay )
	ON_BN_CLICKED( IDC_BUTTON_STOP , &CVideoPlayerDlg::OnBnClickedButtonStop )
   
    ON_BN_CLICKED( IDC_BUTTON_VOL , &CVideoPlayerDlg::OnBnClickedButtonVol )
    ON_BN_CLICKED( IDC_BUTTON_BACK , &CVideoPlayerDlg::OnBnClickedButtonBack )
    ON_BN_CLICKED( IDC_BUTTON_FORWARD , &CVideoPlayerDlg::OnBnClickedButtonForward )
    ON_BN_CLICKED( IDC_BUTTON_OPEN , &CVideoPlayerDlg::OnBnClickedButtonOpen )
    // 静态视频区域点击/双击
    ON_STN_CLICKED( IDC_STATIC_SHOW , &CVideoPlayerDlg::OnVideoAreaClicked )
    ON_STN_DBLCLK( IDC_STATIC_SHOW , &CVideoPlayerDlg::OnVideoAreaDblClk )
    // 自定义应用消息：子渲染窗口点击/双击
    ON_MESSAGE( WM_APP_VIDEO_CLICK , &CVideoPlayerDlg::OnVideoClickMsg )
    ON_MESSAGE( WM_APP_VIDEO_DBLCLK , &CVideoPlayerDlg::OnVideoDblClkMsg )
END_MESSAGE_MAP()


// CVideoPlayerDlg 消息处理程序
/* 获取系统主音量 (0-100) */
int CVideoPlayerDlg::GetSystemVolumePercent( )
    {
    HRESULT hr;
    int nVol = 50;

    hr = CoInitialize( NULL );
    bool bCoInit = SUCCEEDED( hr );
    if (!bCoInit && hr != RPC_E_CHANGED_MODE) bCoInit = false; // already init

    IMMDeviceEnumerator* pEnum = nullptr;
    IMMDevice* pDevice = nullptr;
    IAudioEndpointVolume* pEndpointVol = nullptr;

    do {
        hr = CoCreateInstance( __uuidof( MMDeviceEnumerator ) , NULL , CLSCTX_ALL , IID_PPV_ARGS( &pEnum ) );
        if (FAILED( hr )) break;

        hr = pEnum->GetDefaultAudioEndpoint( eRender , eMultimedia , &pDevice );
        if (FAILED( hr )) break;

        hr = pDevice->Activate( __uuidof( IAudioEndpointVolume ) , CLSCTX_ALL , NULL , ( void** ) &pEndpointVol );
        if (FAILED( hr )) break;

        float fScalar = 0.0f;
        hr = pEndpointVol->GetMasterVolumeLevelScalar( &fScalar );
        if (SUCCEEDED( hr )) nVol = ( int ) ( fScalar * 100.0f + 0.5f );
        }
    while (false);

        if (pEndpointVol) pEndpointVol->Release( );
        if (pDevice) pDevice->Release( );
        if (pEnum) pEnum->Release( );

        if (bCoInit) CoUninitialize( );

        if (nVol < 0) nVol = 0; if (nVol > 100) nVol = 100;
        return nVol;
    }
//=====================================================================================
BOOL CVideoPlayerDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将"关于..."菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

  
    m_sliderVolume.EnableWindow( FALSE );
    m_btnVol.EnableWindow( FALSE );
	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标
    GetWindowRect( &m_rcDlgOrig );

    m_szMinTrack.cx = m_rcDlgOrig.Width( );
    m_szMinTrack.cy = m_rcDlgOrig.Height( );

    auto add = [ this ]( UINT id , bool w , bool h ) { AddCtrl( id , w , h ); };
    add( IDC_STATIC_SHOW , true , true );
    add( IDC_SLIDER_POS , true , false );
    add( IDC_BUTTON_OPEN , false , false );
    add( IDC_BUTTON_PLAY , false , false );
    add( IDC_BUTTON_STOP , false , false );
    add( IDC_BUTTON_BACK , false , false );
    add( IDC_BUTTON_FORWARD , false , false );
    add( IDC_STATIC_TIME , false , false );
    add( IDC_BUTTON_VOL , false , false );
    add( IDC_SLIDER_VOLUME , false , false );


    CRect rcBtn;
    GetDlgItem( IDC_BUTTON_PLAY )->GetWindowRect( &rcBtn );
    ScreenToClient( &rcBtn );
    m_butW = rcBtn.Width( );
    m_butH = rcBtn.Height( );

    /* ---- 采集初始布局度量 ---- */
    {
    CRect rcClient; 
    GetClientRect( &rcClient );
    // 左侧第一个按钮位置
    CRect rcBtnPlay; 
    GetDlgItem( IDC_BUTTON_PLAY )->GetWindowRect( &rcBtnPlay); 
    ScreenToClient( &rcBtnPlay );

    CRect rcBtnStop; 
    GetDlgItem( IDC_BUTTON_STOP )->GetWindowRect( &rcBtnStop ); 
    ScreenToClient( &rcBtnStop);

    // 音量滑块
    CRect rcVol;
    m_sliderVolume.GetWindowRect( &rcVol );
    ScreenToClient( &rcVol );

    // 进度条
    CRect rcProg; 
    m_playprogress.GetWindowRect( &rcProg ); 
    ScreenToClient( &rcProg );

    m_marginLeft = rcBtnPlay.left;
    m_spacingBtn = rcBtnStop.left - rcBtnPlay.right;
    if (m_spacingBtn <= 0) m_spacingBtn = 8;

   m_marginBottom = rcClient.bottom - rcBtnPlay.bottom;
    m_marginRight = rcClient.right - rcVol.right;
    m_gapProgBtn = rcBtnPlay.top - rcProg.bottom;
    if (m_gapProgBtn <= 0) m_gapProgBtn = 8;

    // 高度、宽度保持原值
    m_progressH = rcProg.Height( );
    m_volumeH = rcVol.Height( );
     m_volumeW = rcVol.Width( );

    // 底部距离（音量、时间）
    m_marginBottomVol = rcClient.bottom - rcVol.bottom;
    CRect rcTime; 
    m_staticTime.GetWindowRect( &rcTime ); 
    ScreenToClient( &rcTime );
    m_marginBottomTime = rcClient.bottom - rcTime.bottom;
    }

    
    m_brush.CreateSolidBrush( RGB( 0 , 0 , 0 ) );
    m_memDC.CreateCompatibleDC( NULL );
    m_memBitmap.CreateCompatibleBitmap( GetDC( ) , GetSystemMetrics( SM_CXSCREEN ) , GetSystemMetrics( SM_CYSCREEN ) );
    m_memDC.SelectObject( &m_memBitmap );

	UpdateData( FALSE );

	m_btnPlay.LoadStdImage( IDB_PNG_PLAY , _T( "PNG" ) );
	m_btnStop.LoadStdImage( IDB_PNG_STOP , _T( "PNG" ) );
    m_btnBack.LoadStdImage( IDB_PNG_BACK , _T( "PNG" ) );
    m_btnForward.LoadStdImage( IDB_PNG_FORWARD , _T( "PNG" ) );
    m_btnOpen.LoadStdImage( IDB_PNG_OPEN , _T( "PNG" ) );
    m_btnVol.LoadStdImage( IDB_PNG_VOLON , _T( "PNG" ) );

    m_playprogress.SetBitmapChannel( IDB_MP_CHANNEL , IDB_MP_CHANNEL_ACTIVE );
    m_playprogress.SetBitmapThumb( IDB_MP_THUMB , IDB_MP_THUMB_ACTIVE , TRUE );
    m_playprogress.DrawFocusRect( FALSE );
    m_playprogress.SetRange( 0 , 10000 );

    m_sliderVolume.SetRange( 0 , 100 );
    m_sliderVolume.SetTicFreq( 10 );

    int sysVol = GetSystemVolumePercent( );
    m_sliderVolume.SetPos( sysVol );
    FF_SetVolume( sysVol );


    SetTimer( TIMER_ID_UPDATE_PROGRESS , 500 , NULL );

    m_pVideoWnd = GetDlgItem( IDC_STATIC_SHOW );
    if (m_pVideoWnd) {
        m_hwndVideo = m_pVideoWnd->GetSafeHwnd( );
        // 安装鼠标钩子，捕获子渲染窗口的点击/双击
        InstallVideoMouseHooks( m_hwndVideo , m_hWnd );
        }
    else {
        m_hwndVideo = nullptr;
        }

    LONG styleVideo = GetWindowLong( m_hwndVideo , GWL_STYLE );
    styleVideo |= WS_CLIPCHILDREN | WS_CLIPSIBLINGS | SS_NOTIFY; // 允许静态控件向父窗口发送点击/双击通知
    SetWindowLong( m_hwndVideo , GWL_STYLE , styleVideo );

    FF_SetRenderWindow( m_hwndVideo );

	FF_SetEventCallback( FFEventHandler , this );

    SetWindowText( APP_NAME );

    // 检查是否有命令行文件需要播放
    if (!m_strCommandLineFile.IsEmpty( )) {
        CString tempFile = m_strCommandLineFile;
        m_pti.filePath = tempFile;

        // 更新窗口标题为：应用名称 + 文件名
        std::wstring wt = APP_NAME;
        wt += L"-";
        wt += PathFindFileNameW( tempFile );  // 只取文件名，不包含路径
        SetWindowText( wt.c_str( ) );
        }
  
	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}
//=====================================================================================
void CVideoPlayerDlg::OnActivate( UINT nState , CWnd* pWndOther , BOOL bMinimized )
    {
    CDialogEx::OnActivate( nState , pWndOther , bMinimized );

    if (m_bIsLimitMouse && PlayState::Playing == m_uPlayState) {
        ClipCursor( NULL );
        CRect rect;
        GetWindowRect( &rect );
        ClipCursor( &rect );
        }
    }
//=====================================================================================
void CVideoPlayerDlg::OnClose( )
	{
    KillTimer ( TIMER_ID_UPDATE_PROGRESS );
    KillTimer ( TIMER_ID_SEEK_COMPLETE );
    KillTimer ( TIMER_ID_COMMAND_LINE_PLAY );

    if (m_pti.filePath.GetLength( )) {
        // 先设置播放状态为停止
        m_uPlayState = PlayState::​Stopped;
        
        // 清理事件回调，防止悬垂指针
        FF_SetEventCallback ( nullptr, nullptr );

        // 停止FFmpeg播放器
        FF_Stop( );
        
        // FF_Stop内部已经等待3秒，这里再额外等待确保资源完全释放
        // 注意：FF_Stop会设置g_vs为NULL，所以这里简单等待一段时间即可
        ::Sleep( 500 ); // 增加等待时间到500ms，确保所有线程退出和资源清理完成
        }
	CDialogEx::OnClose( );
	}
//=====================================================================================

void CVideoPlayerDlg::OnSize( UINT nType , int cx , int cy )
	{
	CDialogEx::OnSize( nType , cx , cy );
    if (nType == SIZE_MINIMIZED || !m_hWnd) {
        // 窗口最小化时取消光标限制
        if (m_bIsLimitMouse) {
            ClipCursor( NULL );
            }
        return;
        }

    if (m_vecCtrlInfo.empty( ))
        return;

    // 全屏状态下由 ToggleFullScreen 负责布局
    if (m_bFullScreen)
        return;

    // === 新布局算法：保留底部控件相对间距/高度不变 ===
    const int kMarginLeft = m_marginLeft;
    const int kMarginRight = m_marginRight;
    const int kSliderH = m_progressH;    // 进度条高度
    const int kVolH = m_volumeH;
    const int kVolW = m_volumeW;

    CRect rcClient;
    GetClientRect( &rcClient );

    // 先收集一些尺寸数据
    int timeW = 60 , timeH = 12; // 默认
    if (::IsWindow( m_staticTime.GetSafeHwnd( ) )) {
        CRect rcT;
        m_staticTime.GetWindowRect( &rcT );
        ScreenToClient( &rcT );
        timeW = rcT.Width( );
        timeH = rcT.Height( );
        }

    // 计算底部按钮条 Y 坐标
    int barY = rcClient.bottom - m_marginBottom - m_butH;

    HDWP hDwp = BeginDeferWindowPos( 10 );

    // ---- 左侧按钮按顺序排列 ----
    int xCur = kMarginLeft;
    const int ctrlSpacing = m_spacingBtn;
    auto deferMoveFixedBtn = [ & ]( CWnd* pBtn )
        {
        if (pBtn && ::IsWindow( pBtn->GetSafeHwnd( ) )) {
            hDwp = DeferWindowPos( hDwp , pBtn->GetSafeHwnd( ) , NULL , xCur , barY , m_butW , m_butH , SWP_NOZORDER | SWP_NOACTIVATE );
            xCur += m_butW + ctrlSpacing;
            }
        };
    deferMoveFixedBtn( GetDlgItem( IDC_BUTTON_PLAY ) );
    deferMoveFixedBtn( GetDlgItem( IDC_BUTTON_STOP ) );
    deferMoveFixedBtn( GetDlgItem( IDC_BUTTON_BACK ) );
    deferMoveFixedBtn( GetDlgItem( IDC_BUTTON_FORWARD ) );
    deferMoveFixedBtn( GetDlgItem( IDC_BUTTON_OPEN ) );



    // ---- 音量滑块靠右，保持到底部距离不变 ----
    int volX = rcClient.right - kMarginRight - kVolW;
    int volY = rcClient.bottom - m_marginBottomVol - kVolH;
    if (::IsWindow( m_sliderVolume.GetSafeHwnd( ) )) {
        hDwp = DeferWindowPos( hDwp , m_sliderVolume.GetSafeHwnd( ) , NULL , volX , volY , kVolW , kVolH , SWP_NOZORDER | SWP_NOACTIVATE );
        }

    volX = rcClient.right - kMarginRight - kVolW - m_butW;
    if (::IsWindow( m_btnVol.GetSafeHwnd( ) )) {
        hDwp = DeferWindowPos( hDwp , m_btnVol.GetSafeHwnd( ) , NULL , volX , barY , m_butW , m_butH , SWP_NOZORDER | SWP_NOACTIVATE );
        }

    // ---- 时间显示水平居中，保持到底部距离不变 ----
    int timeX = ( rcClient.Width( ) - timeW ) / 2;
    int timeY = rcClient.bottom - m_marginBottomTime - timeH;
    if (::IsWindow( m_staticTime.GetSafeHwnd( ) )) {
        hDwp = DeferWindowPos( hDwp , m_staticTime.GetSafeHwnd( ) , NULL , timeX , timeY , timeW , timeH , SWP_NOZORDER | SWP_NOACTIVATE );
        }

    // ---- 进度条 ----
    int progressY = barY - m_gapProgBtn - kSliderH;
    if (::IsWindow( m_playprogress.GetSafeHwnd( ) )) {
        hDwp = DeferWindowPos( hDwp , m_playprogress.GetSafeHwnd( ) , NULL , kMarginLeft , progressY , rcClient.Width( ) - kMarginLeft - kMarginRight , kSliderH , SWP_NOZORDER | SWP_NOACTIVATE );
        }

    // ---- 视频区域 ----
    if (m_pVideoWnd && ::IsWindow( m_pVideoWnd->GetSafeHwnd( ) )) {
        hDwp = DeferWindowPos( hDwp , m_pVideoWnd->GetSafeHwnd( ) , NULL , 0 , 0 , rcClient.Width( ) , max( 0 , progressY ) , SWP_NOZORDER | SWP_NOACTIVATE );
        m_rcVideoOrig.SetRect( 0 , 0 , rcClient.Width( ) , max( 0 , progressY ) );
        }

    if (hDwp) EndDeferWindowPos( hDwp );

    // 强制刷新，避免残影
    Invalidate( FALSE );

    if (m_bIsLimitMouse && !m_bIsSizing && PlayState::Playing == m_uPlayState) {
        ClipCursor( NULL );
        CRect rect;
        GetWindowRect( &rect );
        ClipCursor( &rect ); // 恢复限制
        }
	}
//=====================================================================================
BOOL CVideoPlayerDlg::OnEraseBkgnd( CDC* pDC )
	{
    if (m_pVideoWnd && ::IsWindow( m_pVideoWnd->GetSafeHwnd( ) )) {
        // 排除视频区域，避免闪烁
        CRect rcVideo; m_pVideoWnd->GetWindowRect( &rcVideo );
        ScreenToClient( &rcVideo );
        pDC->ExcludeClipRect( &rcVideo );
        }

	return CDialogEx::OnEraseBkgnd( pDC );
	}
//=====================================================================================
HBRUSH CVideoPlayerDlg::OnCtlColor( CDC* pDC , CWnd* pWnd , UINT nCtlColor )
	{
	HBRUSH hbr = CDialogEx::OnCtlColor( pDC , pWnd , nCtlColor );

    if (pWnd->GetDlgCtrlID() == IDC_STATIC_SHOW)
    {
     
        pDC->SetBkColor(RGB(0, 0, 0));              // 背景为黑色
        // 使用 OPAQUE 让系统填充我们返回的画刷颜色（黑色）
        pDC->SetBkMode(OPAQUE);

        return m_brush; // 返回预先创建好的黑色画刷
    }
	return hbr;
	}

//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonPlay( )
    {
    // 2是播放中，1 是暂停 0是播放停止
    if (PlayState::Playing == m_uPlayState) {

        
        PauseOrPlay( true );

        return;
        }

    if (PlayState::​Paused == m_uPlayState) {

        PauseOrPlay( false );

        return;
        }

    if (m_pti.filePath.IsEmpty( )) {

        m_pti.filePath = OpenFile( );

        if (m_pti.filePath.IsEmpty( )) {
            return;
            }

        if (!EvaluateFile( )) {
            m_pti.filePath.Empty( );
            return;
            }
        }

   
    m_btnPlay.LoadStdImage( IDB_PNG_PAUSE , _T( "PNG" ) );
    m_btnPlay.Invalidate( );


    if (m_pti.isEncrypted) {
        if (m_bIsLimitMouse) {
            CRect rect;
            GetWindowRect( &rect );
            ClipCursor( &rect );
            }

        if (!m_strPlayBefore.IsEmpty( )) {
            CDlgWed dw;
            dw.SetURL( m_strPlayBefore );
            dw.DoModal( );
            }

        // 水印

        if (m_bIsWatermark) {
            FF_SetWatermarkTextExW( m_strWatermark.GetString( ) , 1 , nullptr , 1 , nullptr , 36 );
            }

        if (m_pti.lTryTime) {
            FF_SetStopPosition( static_cast< double >( m_pti.lTryTime ) );
            }

        if (m_bIsPlayAsk) {// 初始化随机校验时间点
            m_nextAskTime = std::chrono::steady_clock::now( ) + RandomInterval( );
            m_bAskDialogShowing = false;
            }
        }
    else {
        FF_SetWatermarkTextExW(nullptr , 1 , nullptr , 1 , nullptr , 36 );
        }
    

    /* 4. 后台线程启动新播放，防止阻塞主线程 */
    m_pti.volume = m_sliderVolume.GetPos( );
    PlayTaskInfo pti = m_pti;
    GetSystemTimeAsFileTime( &m_playTime );
 
    m_uPlayState = PlayState::Playing;
    m_sliderVolume.EnableWindow( TRUE );
    m_btnVol.EnableWindow( FALSE );

    if (::IsWindow( m_hwndVideo )) {
        std::thread( &CVideoPlayerDlg::PlayVideoThread , this , pti ).detach( );

        if (m_pti.isEncrypted) {
            std::thread( &CVideoPlayerDlg::BackgroundWorkerThread , this ).detach( );
            }
        }
	}

//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonStop( )
	{
    if (PlayState::​Stopped != m_uPlayState ) {
        FF_Stop( );
		ClearVideoArea();
        m_sliderVolume.EnableWindow( FALSE );
        m_btnVol.EnableWindow( FALSE );
        m_btnPlay.LoadStdImage( IDB_PNG_PLAY , _T( "PNG" ) );
        m_btnPlay.Invalidate( );
        // 重置 UI
        m_playprogress.SetPos( 0 );
        m_bIsSeeking = false;
        m_uPlayState = PlayState::​Stopped;
        UpdateTimeDisplay( );
        m_bAskDialogShowing = false;
       
        if (m_bIsLimitMouse) {
            ClipCursor( NULL );
            }
        }
	}
//=====================================================================================
// 格式化时间显示
CString CVideoPlayerDlg::FormatTime( double seconds )
    {
    if (seconds < 0) seconds = 0;

    int hours = static_cast< int >( seconds / 3600 );
    int minutes = static_cast< int >( ( seconds - hours * 3600 ) / 60 );
    int secs = static_cast< int >( seconds - hours * 3600 - minutes * 60 );

    CString timeStr;
    if (hours > 0) {
        timeStr.Format( _T( "%02d:%02d:%02d" ) , hours , minutes , secs );
        }
    else {
        timeStr.Format( _T( "%02d:%02d" ) , minutes , secs );
        }

    return timeStr;
    }
//=====================================================================================
// 更新时间显示
void CVideoPlayerDlg::UpdateTimeDisplay( )
    {
    // 获取当前时间和总时长
    double currentTime = m_bIsSeeking ? m_dSeekPreviewTime : FF_GetCurrentTime( );


    if (m_totalTime <= 0) {
        m_totalTime = FF_GetDuration( );
        }

    if (m_pti.isEncrypted && m_pti.lTryTime) {
        if (currentTime >= m_pti.lTryTime) {
            FF_Stop( );
            }
        }


    if (currentTime > 0 && m_totalTime > 0) {
        // 格式化时间字符串
        CString currentTimeStr = FormatTime( currentTime );
        CString totalTimeStr = FormatTime( m_totalTime );

        CString title;
        title.Format( _T( "%s / %s" ) , currentTimeStr , totalTimeStr );


        // 更新显示时间信息
        if (m_totalTime > 0) {
            m_staticTime.SetWindowText( title );
            }
        }
    }

//=====================================================================================
LRESULT CVideoPlayerDlg::OnSeekDone( WPARAM wParam , LPARAM lParam )
    {
    // 跳转已完成（来自底层 SeekDone 事件）
    m_bIsSeeking = false;

    // 停止备用延时定时器（若仍在运行）
    KillTimer( TIMER_ID_SEEK_COMPLETE );

    // 恢复进度条定时器
    SetTimer( TIMER_ID_UPDATE_PROGRESS , 500 , NULL );

    // 立即刷新一次进度条和时间显示
    UpdateProgressBar( );

    return 0;
    }
//=====================================================================================
void CVideoPlayerDlg::OnTimer( UINT_PTR nIDEvent )
    {
    if (nIDEvent == TIMER_ID_UPDATE_PROGRESS) {
        if ( m_uPlayState == PlayState::​Stopped ) {
            KillTimer ( TIMER_ID_UPDATE_PROGRESS );
            return;
            }
        // 更新进度条
        UpdateProgressBar( );
        // 如果总时长仍为0，尝试重新获取
        if (m_totalTime <= 0) {
            m_totalTime = FF_GetDuration( );
            UpdateTimeDisplay( );
            }
        }
    else if (nIDEvent == TIMER_ID_SEEK_COMPLETE) {
        // 跳转完成，重置拖动标志
        m_bIsSeeking = false;
        KillTimer( TIMER_ID_SEEK_COMPLETE );

        // 恢复进度条更新定时器
        SetTimer( TIMER_ID_UPDATE_PROGRESS,500 , NULL );

        // 跳转后再次尝试获取时长
        if (m_totalTime <= 0) {
            m_totalTime = FF_GetDuration( );
            }
        // 立即更新一次进度条，确保显示正确位置
        UpdateProgressBar( );
        }
    else if (nIDEvent == TIMER_ID_COMMAND_LINE_PLAY) {
        // 命令行播放定时器
        KillTimer(TIMER_ID_COMMAND_LINE_PLAY);
        StartCommandLinePlayback();
        }

    CDialogEx::OnTimer(nIDEvent);
    }
//=====================================================================================
// 更新进度条
void CVideoPlayerDlg::UpdateProgressBar( )
    {
    // 如果正在拖动进度条，则不更新
    if (m_bIsSeeking) {
        return;
        }

    if (m_uPlayState != PlayState::Playing) {
        return;
        }

    double cur = FF_GetCurrentTime( );

    if (m_totalTime > 0 && cur >= 0) {
        int sliderPos = int( cur / m_totalTime * 10000.0 );
        // 限制范围
        if (sliderPos < 0) sliderPos = 0;
        if (sliderPos > 10000) sliderPos = 10000;

        // 获取当前进度条位置
        int currentSliderPos = m_playprogress.GetPos( );

        // 只有当位置变化超过一定阈值时才更新，避免微小抖动
        // 阈值设为0.1%（10/10000）
        if (abs( sliderPos - currentSliderPos ) > 10) {
            // 设置进度条位置
            m_playprogress.SetPos( sliderPos );
            }

        // 更新时间显示
        UpdateTimeDisplay( );
        }
    }
//=====================================================================================
// 播放完成消息处理
LRESULT CVideoPlayerDlg::OnPlaybackComplete( WPARAM wParam , LPARAM lParam )
    {
    // 先停止播放器，确保所有资源正确清理
    FF_Close( );
    // 更新播放状态
    m_uPlayState = PlayState::​Stopped;
    m_bAskDialogShowing = false;

    // 重置进度条到开始位置
    m_playprogress.SetPos( 0 );
    m_bIsSeeking = false;
    m_dSeekPreviewTime = 0.0;
   
    if (m_pti.isEncrypted) {
        if (!m_strPlayAfter.IsEmpty( )) {
            CDlgWed dw;
            dw.SetURL( m_strPlayAfter );
            dw.DoModal( );
            }

        if (m_bMonitSucess) {
            m_wc.StopMonitoring( );
            }

        m_pti.filePath.Empty( );
        }
    
    m_staticTime.SetWindowText(_T("00:00:00 / 00:00:00" ) );

    if (m_bFullScreen) {
        ToggleFullScreen( );
        }

    // 清屏
    ClearVideoArea( );

    m_btnPlay.LoadStdImage( IDB_PNG_PLAY , _T( "PNG" ) );

    if (m_bIsLimitMouse) {
        ClipCursor( NULL );
        }

    if (1 == wParam) {
        MessageBox( _T( "试播结束。" ), APP_NAME, MB_OK);
        }

    return 0;
    }
//=====================================================================================
// 打开成功后设置音量
LRESULT CVideoPlayerDlg::OnOpenSuccess( WPARAM , LPARAM )
    {
    int vol = m_sliderVolume.GetPos( );
    FF_SetVolume( vol );
    // 在视频打开成功后重新获取总时长，确保值准确
    m_totalTime = FF_GetDuration();
    // 更新一次时间显示
    UpdateTimeDisplay();
    return 0;
    }
//=====================================================================================
LRESULT CVideoPlayerDlg::OnSeeking( WPARAM wParam , LPARAM lParam )
    { 
    if (IDC_SLIDER_POS == wParam) {
        if (m_uPlayState == PlayState::Standby || m_uPlayState == PlayState::​Stopped) {
            return 1;
            }

        m_bIsSeeking = true;
        // 获取最终位置
        int pos = lParam;
    
        KillTimer( TIMER_ID_UPDATE_PROGRESS );

        // 计算对应的时间位置
        //double duration = FF_GetDuration( );
        m_dSeekPreviewTime = m_totalTime * pos / 10000.0;

        // 更新时间显示（预览）
        UpdateTimeDisplay( );
        }
    return 0;
    }
//=====================================================================================
LRESULT CVideoPlayerDlg::OnSeek( WPARAM wParam , LPARAM lParam )
    {
    if (IDC_SLIDER_POS == wParam) {

       if (m_uPlayState == PlayState::Standby 
           || m_uPlayState == PlayState::​Stopped 
           || m_uPlayState == PlayState::Standby) {
            return 1;
            }

        m_bIsSeeking = true;
        // 获取最终位置
        int pos = lParam;
        //double duration = FF_GetDuration( );
        m_dSeekPreviewTime = m_totalTime * pos / 10000.0;

        // 执行精确跳转
        FF_Seek( m_dSeekPreviewTime );

        // 跳转后恢复播放
        PauseOrPlay(false );

        // 延迟重置标志，确保跳转完成
        // 增加延迟时间到800ms，给 Seek 操作更多时间
        SetTimer( TIMER_ID_SEEK_COMPLETE , 800 , NULL );
        }
    return 0;
    }
//=====================================================================================
LRESULT CVideoPlayerDlg::OnDown( WPARAM wParam , LPARAM lParam )
    {
    if (IDC_SLIDER_POS == wParam) {
        m_bIsSeeking = TRUE;
        PauseOrPlay( true );

        // 停止更新定时器
        KillTimer( TIMER_ID_UPDATE_PROGRESS );
        }
    return 0;
    }
//=====================================================================================
// 滑块事件处理
void CVideoPlayerDlg::OnHScroll( UINT nSBCode , UINT nPos , CScrollBar* pScrollBar )
    { 
    // 获取滑块控件ID
    int nID = pScrollBar->GetDlgCtrlID( );

    // 音量滑块
    if (nID == IDC_SLIDER_VOLUME && m_uPlayState == PlayState::Playing) {
        int volume = m_sliderVolume.GetPos( );
        FF_SetVolume( volume );
        }

    CDialogEx::OnHScroll( nSBCode , nPos , pScrollBar );
    }
/*----------------------窗口布局--------------------------------------------------*/
void CVideoPlayerDlg::AddCtrl( UINT id , bool scaleW , bool scaleH )
    {
    CWnd* p = GetDlgItem( id );
    if (!p || !::IsWindow( p->GetSafeHwnd( ) )) {
        return;
        }
    CRect rc; 
    p->GetWindowRect( &rc ); 
    ScreenToClient( &rc );
    CtrlInfo info { id, rc, scaleW, scaleH };
    m_vecCtrlInfo.push_back( info );
    }
//=====================================================================================
void CVideoPlayerDlg::OnGetMinMaxInfo( MINMAXINFO* lpMMI )
    {
    // 设置最小窗口尺寸，防止过小
    lpMMI->ptMinTrackSize.x = m_szMinTrack.cx;
    lpMMI->ptMinTrackSize.y = m_szMinTrack.cy;
    CDialogEx::OnGetMinMaxInfo( lpMMI );
    }
//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonVol( )
    {
    m_bVolOn =  !m_bVolOn;

    m_btnVol.LoadStdImage(m_bVolOn? IDB_PNG_VOLON : IDB_PNG_VOLOFF , _T( "PNG" ) );

    m_sliderVolume.EnableWindow( m_bVolOn );

    FF_SetVolume( m_bVolOn? m_sliderVolume.GetPos( ) :0);
    }
//=====================================================================================
void CVideoPlayerDlg::OnLButtonDblClk( UINT nFlags , CPoint point )
    {
    if (m_uPlayState == PlayState::Playing) {
        ToggleFullScreen( );
        }
 
    CDialogEx::OnLButtonDblClk( nFlags , point );
    }
//=====================================================================================
void CVideoPlayerDlg::OnLButtonDown(UINT nFlags, CPoint point)
{
    if (!m_pti.filePath.IsEmpty( )) {
        // 判断点击是否落在视频显示区域（对抗渲染子窗口遮挡）
        if (m_pVideoWnd && ::IsWindow(m_pVideoWnd->GetSafeHwnd())) {
            CRect rcVideo; m_pVideoWnd->GetWindowRect(&rcVideo);
            CPoint ptScreen = point; ClientToScreen(&ptScreen);
            if (rcVideo.PtInRect(ptScreen)) {
            // 仅在播放/暂停两种状态下响应单击
            if (m_uPlayState == PlayState::Playing) {
                PauseOrPlay( true ); // 播放中 -> 暂停
                return;
            }
            else if (m_uPlayState == PlayState::​Paused) {
                PauseOrPlay( false ); // 暂停中 -> 继续播放
                return;
            }
            }
        }
        }
    
    CDialogEx::OnLButtonDown(nFlags, point);
}

// 静态控件点击（备用渠道，某些主题下父窗口鼠标路由可能被覆盖时）
void CVideoPlayerDlg::OnVideoAreaClicked()
{
    if (m_uPlayState == PlayState::Playing) {
        PauseOrPlay( true );
    } else if (m_uPlayState == PlayState::​Paused) {
        PauseOrPlay( false );
    }
}

void CVideoPlayerDlg::OnVideoAreaDblClk()
{
    if (m_uPlayState == PlayState::Playing) {
        ToggleFullScreen();
    }
}

void CVideoPlayerDlg::OnParentNotify(UINT message, LPARAM lParam)
{
    // 兜底：某些主题/渲染路径可能将点击作为子控件事件上送到父窗口
    switch (message)
    {
    case WM_LBUTTONDOWN:
    case WM_LBUTTONUP:
    case WM_LBUTTONDBLCLK:
    case WM_RBUTTONDOWN:
    case WM_RBUTTONUP:
    case WM_RBUTTONDBLCLK:
        {
            // 将命中逻辑与静态控件保持一致
            POINTS pts = MAKEPOINTS(lParam);
            CPoint pt(pts.x, pts.y);
            if (m_pVideoWnd && ::IsWindow(m_pVideoWnd->GetSafeHwnd())) {
                CRect rcVideo; m_pVideoWnd->GetWindowRect(&rcVideo);
                CPoint ptScreen = pt; ClientToScreen(&ptScreen);
                if (rcVideo.PtInRect(ptScreen)) {
                    if (message == WM_LBUTTONDBLCLK) {
                        OnVideoAreaDblClk();
                        return;
                    }
                    if (message == WM_LBUTTONDOWN) {
                        OnVideoAreaClicked();
                        return;
                    }
                }
            }
        }
        break;
    default: break;
    }
    CDialogEx::OnParentNotify(message, lParam);
}

LRESULT CVideoPlayerDlg::OnVideoClickMsg(WPARAM, LPARAM)
{
    OnVideoAreaClicked();
    return 0;
}

LRESULT CVideoPlayerDlg::OnVideoDblClkMsg(WPARAM, LPARAM)
{
    OnVideoAreaDblClk();
    return 0;
}
//=====================================================================================
void CVideoPlayerDlg::ToggleFullScreen( )
    {
    m_bFullScreen = !m_bFullScreen;
    FF_SetFullScreen( m_bFullScreen ? 1 : 0 );

    // 需要隐藏/显示的控件 ID
    UINT ctrlIDs [ ] = { IDC_BUTTON_OPEN , 
        IDC_BUTTON_PLAY , 
        IDC_STATIC_TIME , 
        IDC_SLIDER_POS,
        IDC_SLIDER_VOLUME,
        IDC_BUTTON_VOL,
        IDC_BUTTON_BACK,
        IDC_BUTTON_FORWARD};

    auto showCtrls = [ & ]( int cmd )
        {
        for (UINT id : ctrlIDs) {
            CWnd* p = GetDlgItem( id );
            if (p && ::IsWindow( p->GetSafeHwnd( ) )) p->ShowWindow( cmd );
            }
        m_btnStop.ShowWindow( cmd );
        m_sliderVolume.ShowWindow( cmd );
        };

    LONG style = GetWindowLong( m_hWnd , GWL_STYLE );
    if (m_bFullScreen) {
        // 隐藏下方控制条
        showCtrls( SW_HIDE );

        // 进入全屏前保存当前窗口位置/状态，便于退出全屏时精确还原
        m_prevPlacement.length = sizeof( m_prevPlacement );
        if (GetWindowPlacement( &m_prevPlacement )) {
            m_hasPrevPlacement = true;
        }

        // 去除边框并使用 WS_POPUP | WS_EX_TRANSPARENT 鼠标穿透仅限子窗口，父窗口可命中
        style &= ~( WS_CAPTION | WS_THICKFRAME );
        style |= WS_POPUP;
        SetWindowLong( m_hWnd , GWL_STYLE , style );

        // 获取窗口所在显示器的物理区域，铺满覆盖任务栏
        MONITORINFO mi = { sizeof( mi ) };
        HMONITOR hMon = MonitorFromWindow( m_hWnd , MONITOR_DEFAULTTONEAREST );
        if (GetMonitorInfo( hMon , &mi )) {
            const RECT& r = mi.rcMonitor;
            ::SetWindowPos(m_hWnd, HWND_TOPMOST , r.left , r.top ,
                          r.right - r.left , r.bottom - r.top ,
                          SWP_FRAMECHANGED | SWP_SHOWWINDOW );
        }
        else {
            // 兜底：仍然最大化并置顶
            ShowWindow( SW_MAXIMIZE );
            ::SetWindowPos( m_hWnd , HWND_TOPMOST , 0 , 0 , 0 , 0 ,
                            SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE | SWP_FRAMECHANGED );
        }

        // 视频铺满
        if (m_pVideoWnd && ::IsWindow( m_pVideoWnd->GetSafeHwnd( ) )) {
            CRect rcClient; GetClientRect( &rcClient );
            m_pVideoWnd->SetWindowPos( NULL , rcClient.left , rcClient.top , rcClient.Width( ) , rcClient.Height( ) , SWP_NOZORDER );

            LONG s = GetWindowLong( m_pVideoWnd->GetSafeHwnd( ) , GWL_STYLE );
            s &= ~( WS_BORDER );
            s |= SS_NOTIFY; // 确保全屏下仍接收点击/双击通知
            SetWindowLong( m_pVideoWnd->GetSafeHwnd( ) , GWL_STYLE , s );
            ::SetWindowPos( m_pVideoWnd->GetSafeHwnd( ) , NULL , 0 , 0 , 0 , 0 , SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED );
            }
        }
    else {
        showCtrls( SW_SHOW );

        // 恢复边框并去除 WS_POPUP
        style |= WS_CAPTION | WS_THICKFRAME;
        style &= ~( WS_POPUP );
        SetWindowLong( m_hWnd , GWL_STYLE , style );

        // 取消置顶并刷新非客户区
        ::SetWindowPos( m_hWnd , HWND_NOTOPMOST , 0 , 0 , 0 , 0 ,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE | SWP_FRAMECHANGED );

        // 若已保存原始窗口位置/状态，按原样恢复；否则回退为 SW_RESTORE
        if (m_hasPrevPlacement) {
            SetWindowPlacement( &m_prevPlacement );
            m_hasPrevPlacement = false;
        } else {
            ShowWindow( SW_RESTORE );
        }

        if (m_pVideoWnd && ::IsWindow( m_pVideoWnd->GetSafeHwnd( ) )) {
            m_pVideoWnd->SetWindowPos( NULL , m_rcVideoOrig.left , m_rcVideoOrig.top , m_rcVideoOrig.Width( ) , m_rcVideoOrig.Height( ) , SWP_NOZORDER | SWP_NOACTIVATE );

            LONG s = GetWindowLong( m_pVideoWnd->GetSafeHwnd( ) , GWL_STYLE );
            s &= ~( WS_BORDER );
            SetWindowLong( m_pVideoWnd->GetSafeHwnd( ) , GWL_STYLE , s );
            }
        }
    }
//=====================================================================================
void CVideoPlayerDlg::PauseOrPlay( bool pause )
    {
    FF_Pause( pause );
    m_uPlayState = pause? PlayState::​Paused: PlayState::Playing;
    m_btnPlay.LoadStdImage(pause?IDB_PNG_PLAY: IDB_PNG_PAUSE , _T( "PNG" ) );

    if (m_bIsLimitMouse) {
        if (pause) {
            ClipCursor( NULL );
            }
        else {
            CRect rect;
            GetWindowRect( &rect );
            ClipCursor( &rect );
            }
        }

    }
//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonBack( )
    {
    FF_SeekRelative( -5.0 );
    if (PlayState::​Paused == m_uPlayState) {
        PauseOrPlay( false );
        }
    }
//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonForward( )
    {
    FF_SeekRelative( 5.0 );
    if (PlayState::​Paused == m_uPlayState) {
        PauseOrPlay( false );
        }
    }
//=====================================================================================
BOOL CVideoPlayerDlg::PreTranslateMessage( MSG* pMsg )
    {
    if (pMsg->message >= WM_IME_SETCONTEXT && pMsg->message <= WM_IME_KEYUP) {
        return TRUE; // Ignore IME messages
        }

    // 统一拦截鼠标单击/双击，确保全屏与渲染子窗场景可用
    if (pMsg->message == WM_LBUTTONDOWN || pMsg->message == WM_LBUTTONDBLCLK) {
        if (!m_pti.filePath.IsEmpty()) {
            if (m_pVideoWnd && ::IsWindow(m_pVideoWnd->GetSafeHwnd())) {
                CRect rcVideo; m_pVideoWnd->GetWindowRect(&rcVideo);
                POINT pt; ::GetCursorPos(&pt);
                if (rcVideo.PtInRect(pt)) {
                    if (pMsg->message == WM_LBUTTONDBLCLK) {
                        if (m_uPlayState == PlayState::Playing) {
                            ToggleFullScreen();
                        }
                        return TRUE;
                    } else {
                        if (m_uPlayState == PlayState::Playing) {
                            PauseOrPlay(true);
                            return TRUE;
                        } else if (m_uPlayState == PlayState::​Paused) {
                            PauseOrPlay(false);
                            return TRUE;
                        }
                    }
                }
            }
        }
    }

    if (pMsg->message == WM_KEYDOWN) {
        switch (pMsg->wParam) {
                case VK_SPACE:
                    OnBnClickedButtonPlay( );
                    return TRUE;
                case VK_LEFT:
                    OnBnClickedButtonBack( );
                    
                    return TRUE;
                case VK_RIGHT:
                    OnBnClickedButtonForward( );
                    return TRUE;
                case VK_UP:
                    {
                    int vol = FF_GetVolume( );
                    vol += 5;
                    if (vol > 100)
                        vol = 100;
                    FF_SetVolume( vol );
                    m_sliderVolume.SetPos( vol );
                    return TRUE;
                    }
                case VK_DOWN:
                    {
                    int vol = FF_GetVolume( );
                    vol -= 5;
                    if (vol < 0) vol = 0;
                    FF_SetVolume( vol );
                    m_sliderVolume.SetPos( vol );
                    return TRUE;
                    }
                case VK_F11:
                    if (m_uPlayState == PlayState::Playing) {
                        ToggleFullScreen( );
                        }
                    return TRUE;
                case VK_ESCAPE:
                    if (m_bFullScreen && PlayState::Playing == m_uPlayState) {
                        ToggleFullScreen( );
                        }
                    else {
                        SendMessage( WM_CLOSE , 0 , 0 );
                        }
                    return TRUE;
                default: break;
            }
        }
    else if (pMsg->message == WM_MOUSEWHEEL && m_uPlayState == PlayState::Playing) {
        short zDelta = GET_WHEEL_DELTA_WPARAM( pMsg->wParam );
        int vol = FF_GetVolume( );
        vol += ( zDelta > 0 ? 5 : -5 );
        if (vol < 0)
            vol = 0;
        if (vol > 100)
            vol = 100;
        FF_SetVolume( vol );
        m_sliderVolume.SetPos( vol );
        return TRUE;
        }

    return CDialogEx::PreTranslateMessage( pMsg );
    }
//=====================================================================================
void CVideoPlayerDlg::ClearVideoArea()
{
	if (m_pVideoWnd && ::IsWindow( m_pVideoWnd->GetSafeHwnd() ))
	{
		CClientDC dc( m_pVideoWnd );
		CRect rc;
        m_pVideoWnd->GetClientRect( &rc );
		dc.FillSolidRect( &rc , RGB( 0 , 0 , 0 ) );
	}
}
//=====================================================================================
bool CVideoPlayerDlg::EvaluateFile( void )
    {
    m_pti.isEncrypted = CVECommon::IsEncryptedVideoFile( m_pti.filePath.GetString( ) );

    if (!m_pti.isEncrypted) {
        return true;
        }


    CString  vecaFile;
    std::vector<std::wstring>  vFiles = CVECommon::FindLicenseFilesForEncryptedVideo( m_pti.filePath.GetString( ) );

    if (1 != vFiles.size( )) {
        vecaFile = OpenVECA( );
        }
    else {
        vecaFile = vFiles [ 0 ].c_str( );
        }


    if (vecaFile.IsEmpty( )) {
        return false;
        }

    // 处理加密文件的扩展名（处理"ve"前缀）
    std::wstring extension = CVECommon::GetFileExtensionW( m_pti.filePath.GetString( ) );

    if (extension.length( ) > 3 && extension.substr( 0 , 3 ) == L".ve") {
        extension = L"." + extension.substr( 3 );  // 移除"ve"前缀
        }

    // 使用EP-common的动态策略确定解密类型
    m_pti.vt = CVideoAESCommon::GetVideoEncryptTypeByFormatW( extension );
    m_pti.at = CVideoAESCommon::GetAudioEncryptTypeByFormatW( extension );

    CEmpower va;
    va.SetCA( vecaFile );
    if (va.OpenCA( )) {
        va.GetVPass( m_pti.password );

        CDlgInfo dlg;
        dlg.SetVa( &va );
        dlg.SetFilePath( m_pti.filePath );
        if (IDOK != dlg.DoModal( )) {

            if (3 <= dlg.GetErrorNum( ) && va.IsDes( )) {
                FileTools::SecureDeleteFile( m_pti.filePath.GetString( ) );
                }
            return false;
            }

        CDlgOpenFile dlgOpenFile;
        dlgOpenFile.SetFilePath( m_pti.filePath );
        dlgOpenFile.SetVa( &va );

        if (IDOK != dlgOpenFile.DoModal( )) {
            return false;
            }
        }
    else {
        return false;
        }
        
       
     
    //播放时限制鼠标
    m_bIsLimitMouse = va.IsLimitMouse( );
    //播放前打开URL
    if (va.IsPBURL( )) {
        m_strPlayBefore = va.GetPBURL( );
        }

    //播放后打开URL
    if (va.IsPFURL( )) {
        m_strPlayAfter = va.GetPFURL( );
        }

    //播放后禁止运行其他进程
    m_bIsRunOther = va.IsRunOther( );
    //播放时进行问答
    m_bIsPlayAsk = va.IsAsk( );
    //播放水印
    m_bIsWatermark = va.IsWatermark( );

    if (m_bIsWatermark) {
        m_strWatermark = va.GetWatermark( );
        }

    //试播时间
    m_pti.lTryTime = va.GetTryTime( );

    //进程黑名单
    CString strProcessList = va.GetPBlackList( );

    if (!strProcessList.IsEmpty( )) {
        GetCloseProcessList( strProcessList );
        }

    char szPass [ 32 ] = { 0 };
    StringCchCopyA( m_pti.password , _countof( m_pti.password ) , va.GetVPass( szPass ) );



    m_wc.SetMange( m_hWnd );
    m_wc.DisableScreenVideo( );
    m_wc.DisableCopyScr( );

    if (m_processBlocklist.size( )) {
        m_wc.DisableOther( m_processBlocklist );
        }

    // 检查是否已经启动监控，避免重复启动
    if (!m_bMonitSucess) {
        try {
            m_bMonitSucess = m_wc.StartMonitoring();
            if (!m_bMonitSucess) {
                // 日志记录失败原因
                // 例如：OutputDebugString(L"StartMonitoring failed!");
            }
        }
        catch (const std::exception& e) {
            // 捕获可能的异常并记录
            // 例如：OutputDebugString(L"Exception in StartMonitoring: " + CString(e.what()));
            m_bMonitSucess = false;
        }
    }
        

    return true;
    }
//=====================================================================================
void CVideoPlayerDlg::OnBnClickedButtonOpen( )
    {
    m_pti.filePath = OpenFile( );

    if (m_pti.filePath.IsEmpty( )) {
        return;
        }

    if (EvaluateFile( )) {

        if (PlayState::​Stopped != m_uPlayState) {
            OnBnClickedButtonStop( );
            }

        OnBnClickedButtonPlay( );
        }
    }
//=====================================================================================
CString   CVideoPlayerDlg::OpenFile( void )
    {
    /* 1. 选择文件 */
    CFileDialog dlg( TRUE ,               // TRUE = 打开，FALSE = 保存
        L"" ,                // 默认扩展
        nullptr ,            // 默认文件名
        OFN_HIDEREADONLY | OFN_LONGNAMES | OFN_FILEMUSTEXIST ,
        L"加密视频文件 (*.vemp4;*.veavi;*.vemkv;*.vemov;*.vewmv;*.veflv;*.vewebm)|*.vemp4;*.veavi;*.vemkv;*.vemov;*.vewmv;*.veflv;*.vewebm|视频文件 (*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm)|*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm|所有文件 (*.*)|*.*||" , this );

    if (dlg.DoModal( ) != IDOK)
        return _T("");

    std::wstring wt = APP_NAME;
    wt += L"-";
    wt += dlg.GetFileName( );
    SetWindowText( wt.c_str( ) );

    return  dlg.GetPathName( );      // 宽字符完整路径
    }
//=====================================================================================
CString  CVideoPlayerDlg::OpenVECA( void )
    {
    /* 1. 选择文件 */
    WCHAR szFileBuf[1024] = { 0 };           // 为长路径准备足够缓冲

    CFileDialog dlg( TRUE ,               // TRUE = 打开，FALSE = 保存
        L"veca" ,            // 默认扩展
        nullptr ,            // 默认文件名
        OFN_EXPLORER | OFN_HIDEREADONLY | OFN_LONGNAMES | OFN_FILEMUSTEXIST ,
        L"授权文件 (*.veca)|*.veca|所有文件 (*.*)|*.*||" , this );

    // 替换默认缓冲区，避免路径过长导致截断或 (null) 问题
    OPENFILENAME& ofn = dlg.GetOFN();
    ofn.lpstrFile = szFileBuf;
    ofn.nMaxFile  = _countof( szFileBuf );

    if (dlg.DoModal() != IDOK)
        return _T( "" );

    return  dlg.GetPathName();      // 宽字符完整路径
    }

/*------------------------------------------------------------------
 * 后台播放线程实现
 *------------------------------------------------------------------*/
void CVideoPlayerDlg::PlayVideoThread( PlayTaskInfo task )
    {
    // 注意：FF_Open 内部已做宽→UTF-8 转换，可直接传 CStringW 指针
    if (task.isEncrypted) {
        // 先设置解密参数（密码、加密类型）。
        // vt / at 类型枚举与底层库保持一致
        FF_SetPassword(task.password , static_cast<int>( task.vt ) , static_cast<int>( task.at ) );
        KeyTools::KeyboardBlocker::Enable( true );
        }
    else {
        FF_SetPassword(nullptr ,  0, 0 );
        }

  
    int ret = FF_Open(task.filePath.GetString( ) );
    if (ret == 0) {
       
        }

    if (task.isEncrypted) {
        KeyTools::KeyboardBlocker::Enable( false );
        }

   
    }
//=====================================================================================
void CVideoPlayerDlg::BackgroundWorkerThread( void )
    {
    while (PlayState::​Stopped != m_uPlayState) {

        if (m_bIsRunOther) {
            ProcessTools::KillProcessesCreatedAfter( m_playTime );
            }

        if (m_bIsPlayAsk) {
            // 检测随机暂停校验
            if (PlayState::Playing == m_uPlayState && !m_bAskDialogShowing) {
                auto now = std::chrono::steady_clock::now( );
                if (now >= m_nextAskTime) {
                    ::PostMessage( GetSafeHwnd( ) , WM_USER_SHOW_ASK , 0 , 0 );
                    m_nextAskTime = now + RandomInterval( );
                    }
                }
            }

        if (!m_bMonitSucess) {
            m_wc.RunCloseWindows( );
            m_wc.RunCloseProcess( );
            }
         // 等待下一个检查周期
        std::this_thread::sleep_for( std::chrono::milliseconds( 200 ) );
        }
    }
//=====================================================================================
void CVideoPlayerDlg::OnSysCommand( UINT nID , LPARAM lParam )
    {
    if (( nID & 0xFFF0 ) == IDM_ABOUTBOX) {
        CAboutDlg dlgAbout;
        dlgAbout.DoModal( );
        }
    else {
        CDialogEx::OnSysCommand( nID , lParam );
        }
    }

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CVideoPlayerDlg::OnPaint( )
    {
    if (IsIconic( )) {
        CPaintDC dc( this ); // 用于绘制的设备上下文

        SendMessage( WM_ICONERASEBKGND , reinterpret_cast< WPARAM >( dc.GetSafeHdc( ) ) , 0 );

        // 使图标在工作区矩形中居中
        int cxIcon = GetSystemMetrics( SM_CXICON );
        int cyIcon = GetSystemMetrics( SM_CYICON );
        CRect rect;
        GetClientRect( &rect );
        int x = ( rect.Width( ) - cxIcon + 1 ) / 2;
        int y = ( rect.Height( ) - cyIcon + 1 ) / 2;

        // 绘制图标
        dc.DrawIcon( x , y , m_hIcon );
        }
    else {

        CDialogEx::OnPaint( );
        }
    }

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CVideoPlayerDlg::OnQueryDragIcon( )
    {
    return static_cast< HCURSOR >( m_hIcon );
    }
//=====================================================================================
// 进入拉伸/移动循环时触发
void CVideoPlayerDlg::OnEnterSizeMove()
    {
    CDialogEx::OnEnterSizeMove();

    if (m_bIsLimitMouse && PlayState::Playing == m_uPlayState) {
        m_bIsSizing = true;
        ClipCursor( NULL ); // 取消限制
        }
    }
//=====================================================================================
// 退出拉伸/移动循环时触发
void CVideoPlayerDlg::OnExitSizeMove()
    {
    CDialogEx::OnExitSizeMove();

    if (m_bIsLimitMouse && m_bIsSizing && PlayState::Playing == m_uPlayState) {
        m_bIsSizing = false;
        CRect rect;
        GetWindowRect( &rect );
        ClipCursor( &rect ); // 恢复限制
        }
    }
//=====================================================================================
// 生成 3~7 分钟随机间隔
std::chrono::milliseconds CVideoPlayerDlg::RandomInterval()
    {
    static std::mt19937 rng{ std::random_device{}() };
    std::uniform_int_distribution<int> dist( 180 , 420 ); // 秒
    return std::chrono::seconds( dist( rng ) );
    }
//=====================================================================================
LRESULT CVideoPlayerDlg::OnFailedWnd( WPARAM , LPARAM )
    {
    if (m_uPlayState != PlayState::Playing) {
        return 1;
        }

    FF_Stop( );
    m_uPlayState = PlayState::​Stopped;
    return 0;
    }
//=====================================================================================
LRESULT CVideoPlayerDlg::OnFailedProcess( WPARAM , LPARAM )
    {
    if (m_uPlayState != PlayState::Playing) {
        return 1;
        }

    FF_Stop( );
    m_uPlayState = PlayState::​Stopped;
    return 0;
    }
//=====================================================================================
// 随机校验弹窗消息处理
LRESULT CVideoPlayerDlg::OnShowAsk( WPARAM, LPARAM )
    {
    if (m_bAskDialogShowing || PlayState::Playing != m_uPlayState) {
        return 0;
        }

    m_bAskDialogShowing = true;

    PauseOrPlay( true );

    CDlgAsk dlg;
    INT_PTR ret = dlg.DoModal();

    if (IDOK == ret) {
        PauseOrPlay( false );
        }
    else {
        OnBnClickedButtonStop();
        }

    m_bAskDialogShowing = false;
    return 0;
    }
//=====================================================================================
void CVideoPlayerDlg::GetCloseProcessList( const  CString& strList )
    {
    CString strPBlackList = strList;
    int pos = strPBlackList.Find( _T( "|" ) );

    while (pos > -1) {
        CString strProcess = strPBlackList.Mid( 0 , pos );
        if (strProcess.Right( 4 ) != _T( ".exe" ) && strProcess.Right( 4 ) != _T( ".EXE" )) {
            strProcess += _T( ".exe" );
            }
        m_processBlocklist.push_back( strProcess.GetString( ) );
        strPBlackList = strPBlackList.Mid( pos + 1 , strPBlackList.GetLength( ) );
        pos = strPBlackList.Find( _T( "|" ) );
        }
    }
//=====================================================================================
void CAboutDlg::OnBnClickedOk( )
    {
    if (StrTools::CopyToClipboard( m_strID.GetString( ) )) {
        MessageBox( L"复制成功。" , APP_NAME, MB_OK );
        }
    else {
        MessageBox( L"复制失败。" , APP_NAME , MB_OK );
        }
    CDialogEx::OnOK( );
    }

//=====================================================================================
BOOL CAboutDlg::OnInitDialog( )
    {
    CDialogEx::OnInitDialog( );

    CDisk32 d32;
   std::wstring sID;
   ULONGLONG uID = d32.getComputerID( );
   if (0 != uID) {
       m_strID = StrTools::UInt64ToHexStringW( uID ).c_str  ();
       }

   UpdateData( FALSE );

    return TRUE;  // return TRUE unless you set the focus to a control
                  // 异常: OCX 属性页应返回 FALSE
    }

//=====================================================================================
// 命令行相关方法实现
//=====================================================================================
void CVideoPlayerDlg::SetCommandLineFile(const CString& filePath, bool autoPlay)
{
    m_strCommandLineFile = filePath;
    m_bCommandLineAutoPlay = autoPlay;
}

void CVideoPlayerDlg::StartCommandLinePlayback()
{
    if (m_strCommandLineFile.IsEmpty( )) {
        return;
        }

    // 验证文件是否存在和有效
    CString tempFile = m_strCommandLineFile;
    if (PathFileExistsW(tempFile))
    {
        m_pti.filePath = tempFile;

        // 更新窗口标题为：应用名称 + 文件名
        std::wstring wt = APP_NAME;
        wt += L"-";
        wt += PathFindFileNameW(tempFile);  // 只取文件名，不包含路径
        SetWindowText(wt.c_str());

        if (EvaluateFile( )) {

            if (PlayState::​Stopped != m_uPlayState) {
                OnBnClickedButtonStop( );
                }

            OnBnClickedButtonPlay( );
            }
    }
    else
    {
        MessageBox(L"指定的文件不存在：\n" + m_strCommandLineFile, L"文件不存在", MB_OK | MB_ICONERROR);
        PostMessage(WM_CLOSE);
    }
}
