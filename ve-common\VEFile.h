#pragma once

#include <windows.h>
#include <tchar.h>
#include <atlstr.h>

#define FILE_PANEL      L"File"
#define OUT_FOLDER_PATH L"Out_Folder_Path"
#define OUT_FILE_PATH   L"Out_File_Path"
#define IN_FILE_PATH    L"In_File_Path"
#define VE_PASS         L"VE_PASS"
#define FILE_MD5        L"File_MD5"

#define AUTH_PANEL      L"Auth"
#define AUTH_URL        L"Auth_URL"
#define AUTH_HIDBL      L"Auth_HIDBL"
#define AUTH_NUM        L"Auth_Num"
#define AUTH_HARD       L"Auth_Hard"
#define PLAY_VER        L"Play_Ver"

#define INFO_PANEL        L"Info"
#define INFO_TITLE        L"Info_Title"
#define INFO_DES          L"Info_Des"
#define INFO_BUY_TEXT     L"Info_Buy_Text"
#define INFO_BUY_URL      L"Info_Buy_URL"
 
#define PLAY_PANEL       L"Play"
#define BREAK_NET       L"Break_Net"
#define PLAY_ASK       L"Play_ask"
#define PLAY_VIR       L"Play_Vir"
#define PLAY_MOUSE       L"Play_Mouse"
#define SELF_DEL       L"Self_Del"
#define RUN_OTHER       L"Run_Other"
#define PLAY_WATER       L"Play_Water"
#define PLAY_OP_URL       L"Play_OP_URL"
#define PLAY_OB_URL       L"Play_OB_URL"
#define PLAY_BP       L"Play_BP"
#define WATER       L"Water"
#define OP_URL       L"OP_URL"
#define OB_URL      L"OB_URL"
#define BP           L"BP"

class  CVEFile
	{
	public:
		CVEFile(void);
		CVEFile(const CString & strFile);
		~CVEFile(void);
	public:
		DWORD SetValue(const CString &strPanel, const CString &strKey, const CString &strValue);
		DWORD GetValue(const CString &strPanel, const CString &strKey, CString &strValue);
	private:
	    CString  m_strFilePath;
	};

