#pragma once
class CGPEdit : public CEdit {
public:
    CGPEdit( );
    virtual ~CGPEdit( );
  
protected:
    //{{AFX_MSG(CGPStatic)
    afx_msg void OnNcCalcSize( BOOL bCalcValidRects , NCCALCSIZE_PARAMS FAR* lpncsp );
    afx_msg void OnNcPaint( );
    //afx_msg BOOL OnEraseBkgnd( CDC* pDC );
    afx_msg HBRUSH CtlColor( CDC* pDC , UINT nCtlColor );
    DECLARE_MESSAGE_MAP( )

    virtual void PreSubclassWindow( );
private:
    CRect  m_rectNCTop;
    CRect  m_rectNCBottom;
    CRect  m_rectNCLeft;
    CRect  m_rectNCRight;
    CBrush m_Brush;
    CFont         m_font;
    };

