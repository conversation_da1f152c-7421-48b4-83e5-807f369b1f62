#pragma once
#include <string>
#include <vector>
typedef std::vector< std::wstring> VWString;

class CAppEx
    {
        public:
        CAppEx( );
        ~CAppEx( );

        std::wstring GetAppPath( )
            {
            if (m_wsAppPath.empty( )) {
                GetAppPathAndFileName( );
                }
            return m_wsAppPath;
            }

        std::wstring GetAppFileName( )
            {
            if (m_wsAppFileName.empty( )) {
                GetAppPathAndFileName( );
                }

            return m_wsAppFileName;
            }

        std::wstring GetAppFilePath( )
            {
            if (m_wsAppFilePath.empty( )) {
                GetAppPathAndFileName( );
                }

            return m_wsAppFilePath;
            }

        int GetParameterNum( )
            {
            return m_iArgcs;
            }

        VWString GetParameterList( )
            {
            return m_vArg;
            }

        DWORD GetPID( )
            {
            return m_dwPID;
            }

        std::wstring GetComputerName( );
        std::wstring GetUserDocuments( );

        std::wstring GetPublicDocuments( );

        std::wstring GetUserDataPath( );
        std::wstring GetUserLocalDataPath( );

        std::wstring GetPublicDataPath( );

        std::wstring GetUserTempPath( );
        std::wstring GetSystemPath( );
        std::wstring GetWindowsPath( );

        std::wstring GetUserName( );
        std::wstring GetUserPath( );
        std::wstring GetUserDeskTopPath( );
        std::wstring GetProgramFilePath( );
        BOOL    IsSystemFolder( const  std::wstring& wsPath );

        DWORD IsProcessExist( DWORD processID );
        private:
        std::wstring GetFolderPathEx( const int& nFolder );
        void    GetAppPathAndFileName( void );

        DWORD CheckProcessBySnapshot( DWORD processID );

        DWORD CheckProcessByHandle( DWORD processID );
        private:
        HRESULT m_hr;
        DWORD           m_dwLastError;
        std::wstring         m_wsAppPath;
        std::wstring         m_wsAppFilePath;
        std::wstring         m_wsAppFileName;
        std::wstring         m_wsUserName;
        DWORD           m_dwPID;
        VWString        m_vArg;
        int             m_iArgcs;

        std::wstring         m_wsUserTempPath;
        std::wstring         m_wsUserDocuments;
        std::wstring         m_wsPublicDocuments;
        std::wstring         m_wsUserDataPath;
        std::wstring         m_wsUserLocalDataPath;
        std::wstring         m_wsUserDeskTopPath;
        std::wstring         m_wsUserPath;
        std::wstring         m_wsPublicDataPath;
        std::wstring         m_wsSystemPath;
        std::wstring         m_wsWindowsPath;
        std::wstring         m_wsProgramFilePath;
    };

