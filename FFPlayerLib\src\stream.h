#pragma once
#include "definition.h"

VideoState* stream_open( const char* filename ,
    const AVInputFormat* iformat );

int stream_component_open( VideoState* is , int stream_index );
int stream_has_enough_packets( AVStream* st , int stream_id , PacketQueue* queue );
void stream_component_close( VideoState* is , int stream_index );
void stream_close( VideoState* is );

int create_hwaccel( AVBufferRef** device_ctx );