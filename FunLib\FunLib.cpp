﻿// FunLib.cpp : 定义静态库的函数。
//

#include "pch.h"
#include "funLib.h"

namespace FunLib
    {
    std::wstring FormatLastError( DWORD code )
        {
        HMODULE hModule = nullptr;
        bool needFree = false;
        DWORD flags = FORMAT_MESSAGE_IGNORE_INSERTS | FORMAT_MESSAGE_ALLOCATE_BUFFER;

        auto loadModule = [ & ]( const wchar_t* name ) -> HMODULE
            {
            HMODULE mod = ::GetModuleHandleW( name );
            if (!mod) {
                mod = ::LoadLibraryW( name );
                if (mod) needFree = true;
                }
            return mod;
            };

        if (code >= 12000 && code <= 12175)           // WinINet
            hModule = loadModule( L"wininet.dll" );
        else if (code >= 10000 && code <= 11999)     // Winsock
            hModule = loadModule( L"ws2_32.dll" );
        else if (code >= 2100 && code <= 2999)       // NetAPI errors
            hModule = loadModule( L"netmsg.dll" );

        if (hModule)
            flags |= FORMAT_MESSAGE_FROM_HMODULE;
        else
            flags |= FORMAT_MESSAGE_FROM_SYSTEM;

        wchar_t* buf = nullptr;
        DWORD len = ::FormatMessageW( flags , hModule , code , 0 , ( LPWSTR ) &buf , 0 , nullptr );
        std::wstring msg;
        if (len && buf)
            msg.assign( buf , len );
        else {
            wchar_t tmp [ 64 ];
            swprintf_s( tmp , L"Unknown error 0x%08X" , code );
            msg = tmp;
            }
        if (buf) LocalFree( buf );
        if (needFree && hModule) FreeLibrary( hModule );
        return msg;
        }
    }