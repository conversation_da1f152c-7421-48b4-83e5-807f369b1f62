﻿#include "thread.h"
#include "cmdutils.h"
#include "decoder.h"
#include "audio.h"
#include "fame_queue.h"
#include "ve.h"
#include "ffplay.h"   /* for event dispatch */

static int infinite_buffer = -1;
static int loop = 1;
static const char* wanted_stream_spec [ AVMEDIA_TYPE_NB ] = { 0 };
static int autoexit = 1;
static int64_t duration = AV_NOPTS_VALUE;
static int find_stream_info = 1;

static int subtitle_disable;
static int genpts = 0;
static enum ShowMode show_mode = SHOW_MODE_NONE;

/* seek 完成事件延迟到第一帧渲染后再发送 */
volatile int g_seek_in_progress = 0;
void FF_Internal_SeekDone( void );

/* this thread gets the stream from the disk or the network */
int read_thread( void* arg )
    {
    VideoState* is = arg;
    AVFormatContext* ic = NULL;
    int err , i , ret;
    int st_index [ AVMEDIA_TYPE_NB ];
    AVPacket* pkt = NULL;
    int64_t stream_start_time;
    int pkt_in_play_range = 0;
    const AVDictionaryEntry* t;
    SDL_mutex* wait_mutex = SDL_CreateMutex( );
    int scan_all_pmts_set = 0;
    int64_t pkt_ts;
    int    isEncrypted = IsEncrypted( );

    if (!wait_mutex) {
        av_log( NULL , AV_LOG_FATAL , "SDL_CreateMutex(): %s\n" , SDL_GetError( ) );
        ret = AVERROR( ENOMEM );
        goto fail;
        }

    memset( st_index , -1 , sizeof( st_index ) );
    is->eof = 0;

    pkt = av_packet_alloc( );

    if (!pkt) {
        av_log( NULL , AV_LOG_FATAL , "Could not allocate packet.\n" );
        ret = AVERROR( ENOMEM );
        goto fail;
        }

    ic = avformat_alloc_context( );
    if (!ic) {
        av_log( NULL , AV_LOG_FATAL , "Could not allocate context.\n" );
        ret = AVERROR( ENOMEM );
        goto fail;
        }

    ic->interrupt_callback.callback = decode_interrupt_cb;
    ic->interrupt_callback.opaque = is;

    if (!av_dict_get( format_opts , "scan_all_pmts" , NULL , AV_DICT_MATCH_CASE )) {
        av_dict_set( &format_opts , "scan_all_pmts" , "1" , AV_DICT_DONT_OVERWRITE );
        scan_all_pmts_set = 1;
        }

    err = avformat_open_input( &ic , is->filename , is->iformat , &format_opts );
    if (err < 0) {
        print_error( is->filename , err );
        ret = -1;
        goto fail;
        }

    if (scan_all_pmts_set) {
        av_dict_set( &format_opts , "scan_all_pmts" , NULL , AV_DICT_MATCH_CASE );
        }

    remove_avoptions( &format_opts , codec_opts );

    ret = check_avoptions( format_opts );
    if (ret < 0) {
        goto fail;
        }

    is->ic = ic;

    if (genpts) {
        ic->flags |= AVFMT_FLAG_GENPTS;
        }

    if (find_stream_info) {
        AVDictionary** opts;
        int orig_nb_streams = ic->nb_streams;

        err = setup_find_stream_info_opts( ic , codec_opts , &opts );
        if (err < 0) {
            av_log( NULL , AV_LOG_ERROR ,
                "Error setting up avformat_find_stream_info() options\n" );
            ret = err;
            goto fail;
            }

        err = avformat_find_stream_info( ic , opts );

        for (i = 0; i < orig_nb_streams; i++) {
            av_dict_free( &opts [ i ] );
            }

        av_freep( &opts );

        if (err < 0) {
            av_log( NULL , AV_LOG_WARNING ,
                "%s: could not find codec parameters\n" , is->filename );
            ret = -1;
            goto fail;
            }
        }

    if (ic->pb)
        ic->pb->eof_reached = 0; // FIXME hack, ffplay maybe should not use avio_feof() to test for the end

    if (seek_by_bytes < 0) {
        seek_by_bytes = !( ic->iformat->flags & AVFMT_NO_BYTE_SEEK ) &&
            !!( ic->iformat->flags & AVFMT_TS_DISCONT ) &&
            strcmp( "ogg" , ic->iformat->name );
        }

    is->max_frame_duration = ( ic->iformat->flags & AVFMT_TS_DISCONT ) ? 10.0 : 3600.0;

    if (!window_title && ( t = av_dict_get( ic->metadata , "title" , NULL , 0 ) )) {
        window_title = av_asprintf( "%s - %s" , t->value , input_filename );
        }

    /* if seeking requested, we execute it */
    if (start_time != AV_NOPTS_VALUE) {
        int64_t timestamp;

        timestamp = start_time;
        /* add the stream start time */
        if (ic->start_time != AV_NOPTS_VALUE)
            timestamp += ic->start_time;
        ret = avformat_seek_file( ic , -1 , INT64_MIN , timestamp , INT64_MAX , 0 );
        if (ret < 0) {
            av_log( NULL , AV_LOG_WARNING , "%s: could not seek to position %0.3f\n" ,
                is->filename , ( double ) timestamp / AV_TIME_BASE );
            }
        }

    is->realtime = is_realtime( ic );

    if (show_status) {
        av_dump_format( ic , 0 , is->filename , 0 );
        }

    for (i = 0; i < ic->nb_streams; i++) {

        AVStream* st = ic->streams [ i ];
        enum AVMediaType type = st->codecpar->codec_type;
        st->discard = AVDISCARD_ALL;

        if (type >= 0 && wanted_stream_spec [ type ] && st_index [ type ] == -1)
            if (avformat_match_stream_specifier( ic , st , wanted_stream_spec [ type ] ) > 0)
                st_index [ type ] = i;
        }

    for (i = 0; i < AVMEDIA_TYPE_NB; i++) {
        if (wanted_stream_spec [ i ] && st_index [ i ] == -1) {
            av_log( NULL , AV_LOG_ERROR , "Stream specifier %s does not match any %s stream\n" , wanted_stream_spec [ i ] , av_get_media_type_string( i ) );
            st_index [ i ] = INT_MAX;
            }
        }

    if (!video_disable)
        st_index [ AVMEDIA_TYPE_VIDEO ] =
        av_find_best_stream( ic , AVMEDIA_TYPE_VIDEO ,
            st_index [ AVMEDIA_TYPE_VIDEO ] , -1 , NULL , 0 );
    if (!audio_disable)
        st_index [ AVMEDIA_TYPE_AUDIO ] =
        av_find_best_stream( ic , AVMEDIA_TYPE_AUDIO ,
            st_index [ AVMEDIA_TYPE_AUDIO ] ,
            st_index [ AVMEDIA_TYPE_VIDEO ] ,
            NULL , 0 );
    if (!video_disable && !subtitle_disable)
        st_index [ AVMEDIA_TYPE_SUBTITLE ] =
        av_find_best_stream( ic , AVMEDIA_TYPE_SUBTITLE ,
            st_index [ AVMEDIA_TYPE_SUBTITLE ] ,
            ( st_index [ AVMEDIA_TYPE_AUDIO ] >= 0 ?
                st_index [ AVMEDIA_TYPE_AUDIO ] :
                st_index [ AVMEDIA_TYPE_VIDEO ] ) ,
            NULL , 0 );

    is->show_mode = show_mode;
    if (st_index [ AVMEDIA_TYPE_VIDEO ] >= 0) {
        AVStream* st = ic->streams [ st_index [ AVMEDIA_TYPE_VIDEO ] ];
        AVCodecParameters* codecpar = st->codecpar;
        AVRational sar = av_guess_sample_aspect_ratio( ic , st , NULL );
        if (codecpar->width)
            set_default_window_size( codecpar->width , codecpar->height , sar );
        }

    /* open the streams */
    if (st_index [ AVMEDIA_TYPE_AUDIO ] >= 0) {
        stream_component_open( is , st_index [ AVMEDIA_TYPE_AUDIO ] );
        }

    ret = -1;
    if (st_index [ AVMEDIA_TYPE_VIDEO ] >= 0) {
        ret = stream_component_open( is , st_index [ AVMEDIA_TYPE_VIDEO ] );
        }
    if (is->show_mode == SHOW_MODE_NONE)
        is->show_mode = ret >= 0 ? SHOW_MODE_VIDEO : SHOW_MODE_RDFT;

    if (st_index [ AVMEDIA_TYPE_SUBTITLE ] >= 0) {
        stream_component_open( is , st_index [ AVMEDIA_TYPE_SUBTITLE ] );
        }

    if (is->video_stream < 0 && is->audio_stream < 0) {
        av_log( NULL , AV_LOG_FATAL , "Failed to open file '%s' or configure filtergraph\n" ,
            is->filename );
        ret = -1;
        goto fail;
        }

    if (infinite_buffer < 0 && is->realtime)
        infinite_buffer = 1;

    for (;;) {
        if (is->abort_request) {
            break;
            }

        if (is->paused != is->last_paused) {

            is->last_paused = is->paused;

            if (is->paused) {
                is->read_pause_return = av_read_pause( ic );
                }
            else {
                av_read_play( ic );
                }
            }

#if CONFIG_RTSP_DEMUXER || CONFIG_MMSH_PROTOCOL
        if (is->paused &&
            ( !strcmp( ic->iformat->name , "rtsp" ) ||
                ( ic->pb && !strncmp( input_filename , "mmsh:" , 5 ) ) )) {
            /* wait 10 ms to avoid trying to get another packet */
            /* XXX: horrible */
            SDL_Delay( 10 );
            continue;
            }
#endif
        if (is->seek_req) {
            int64_t seek_target = is->seek_pos;
            int64_t seek_min = is->seek_rel > 0 ? seek_target - is->seek_rel + 2 : INT64_MIN;
            int64_t seek_max = is->seek_rel < 0 ? seek_target - is->seek_rel - 2 : INT64_MAX;
            // FIXME the +-2 is due to rounding being not done in the correct direction in generation
            //      of the seek_pos/seek_rel variables

            ret = avformat_seek_file( is->ic , -1 , seek_min , seek_target , seek_max , is->seek_flags );

            if (ret < 0) {
                av_log( NULL , AV_LOG_ERROR ,
                    "%s: error while seeking\n" , is->ic->url );
                }
            else {
                if (is->audio_stream >= 0) {
                    packet_queue_flush( &is->audioq );
                    }

                if (is->subtitle_stream >= 0) {
                    packet_queue_flush( &is->subtitleq );
                    }

                if (is->video_stream >= 0) {
                    packet_queue_flush( &is->videoq );
                    }

                if (is->seek_flags & AVSEEK_FLAG_BYTE) {
                    set_clock( &is->extclk , NAN , 0 );
                    }
                else {
                    set_clock( &is->extclk , seek_target / ( double ) AV_TIME_BASE , 0 );
                    }
                }

            is->seek_req = 0;
            is->queue_attachments_req = 1;
            is->eof = 0;

            if (is->paused) {
                step_to_next_frame( is );
                }

            /* 标记：等待第一帧渲染再通知 SEEK_DONE */
            g_seek_in_progress = 1;
            }

        if (is->queue_attachments_req) {

            if (is->video_st && is->video_st->disposition & AV_DISPOSITION_ATTACHED_PIC) {

                if (( ret = av_packet_ref( pkt , &is->video_st->attached_pic ) ) < 0) {
                    goto fail;
                    }

                packet_queue_put( &is->videoq , pkt );
                packet_queue_put_nullpacket( &is->videoq , pkt , is->video_stream );
                }
            is->queue_attachments_req = 0;
            }

        /* if the queue are full, no need to read more */
        if (infinite_buffer < 1 &&
            ( is->audioq.size + is->videoq.size + is->subtitleq.size > MAX_QUEUE_SIZE
                || ( stream_has_enough_packets( is->audio_st , is->audio_stream , &is->audioq ) &&
                    stream_has_enough_packets( is->video_st , is->video_stream , &is->videoq ) &&
                    stream_has_enough_packets( is->subtitle_st , is->subtitle_stream , &is->subtitleq ) ) )) {
            /* wait 10 ms */
            SDL_LockMutex( wait_mutex );
            SDL_CondWaitTimeout( is->continue_read_thread , wait_mutex , 10 );
            SDL_UnlockMutex( wait_mutex );
            continue;
            }

        if (!is->paused &&
            (!is->audio_st || (is->auddec.finished == is->audioq.serial && frame_queue_nb_remaining(&is->sampq) == 0)) &&
            (!is->video_st || (is->viddec.finished == is->videoq.serial && frame_queue_nb_remaining(&is->pictq) == 0))) {

            if (loop != 1 && (!loop || --loop)) {
                stream_seek(is, start_time != AV_NOPTS_VALUE ? start_time : 0, 0, 0);
            } else if (autoexit) {
                // 发送播放完成事件，而不是直接退出
                SDL_Event event;
                event.type = FF_QUIT_EVENT; // 自定义事件类型
                event.user.data1 = is;
                SDL_PushEvent(&event);
                ret = AVERROR_EOF;
                // 不直接跳转到 fail，而是等待事件处理
            }
        }

        ret = av_read_frame( ic , pkt );

        if (ret < 0) {
            if (( ret == AVERROR_EOF || avio_feof( ic->pb ) ) && !is->eof) {
                if (is->video_stream >= 0) {
                    packet_queue_put_nullpacket( &is->videoq , pkt , is->video_stream );
                    }

                if (is->audio_stream >= 0) {
                    packet_queue_put_nullpacket( &is->audioq , pkt , is->audio_stream );
                    }

                if (is->subtitle_stream >= 0) {
                    packet_queue_put_nullpacket( &is->subtitleq , pkt , is->subtitle_stream );
                    }

                is->eof = 1;
                }

            if (ic->pb && ic->pb->error) {
                if (autoexit)
                    goto fail;
                else
                    break;
                }

            // 添加检查：如果已到达文件末尾且播放完成，则发送播放完成事件
            if (is->eof && autoexit &&
                (!is->audio_st || (is->auddec.finished == is->audioq.serial && frame_queue_nb_remaining(&is->sampq) == 0)) &&
                (!is->video_st || (is->viddec.finished == is->videoq.serial && frame_queue_nb_remaining(&is->pictq) == 0))) {
                SDL_Event event;
                event.type = FF_QUIT_EVENT; // 自定义事件类型，需要在头文件中定义
                event.user.data1 = is;
                SDL_PushEvent(&event);
                // 不直接跳转到 fail，而是等待事件处理
                break;
            }
            SDL_LockMutex(wait_mutex);
            SDL_CondWaitTimeout(is->continue_read_thread, wait_mutex, 10);
            SDL_UnlockMutex(wait_mutex);
            continue;
            }
        else {
            is->eof = 0;
            }

        /* check if packet is in play range specified by user, then queue, otherwise discard */
        stream_start_time = ic->streams [ pkt->stream_index ]->start_time;
        pkt_ts = pkt->pts == AV_NOPTS_VALUE ? pkt->dts : pkt->pts;
        pkt_in_play_range = duration == AV_NOPTS_VALUE ||
            ( pkt_ts - ( stream_start_time != AV_NOPTS_VALUE ? stream_start_time : 0 ) ) *
            av_q2d( ic->streams [ pkt->stream_index ]->time_base ) -
            ( double ) ( start_time != AV_NOPTS_VALUE ? start_time : 0 ) / 1000000
            <= ( ( double ) duration / 1000000 );

        if (pkt->stream_index == is->audio_stream && pkt_in_play_range) {
            if (isEncrypted) {
                if (!DecryptAudioPacket( pkt )) {
                    av_packet_unref( pkt );
                    }
                }
            packet_queue_put( &is->audioq , pkt );
            }
        else if (pkt->stream_index == is->video_stream && pkt_in_play_range
            && !( is->video_st->disposition & AV_DISPOSITION_ATTACHED_PIC )) {
            if (isEncrypted) {
                if (!DecryptVideoPacket( pkt )) {
                    av_packet_unref( pkt );
                    }
                }
            packet_queue_put( &is->videoq , pkt );
            }
        else if (pkt->stream_index == is->subtitle_stream && pkt_in_play_range) {
            packet_queue_put( &is->subtitleq , pkt );
            }
        else {
            av_packet_unref( pkt );
            }
        }

    ret = 0;
fail:

    if (wait_mutex) {
        SDL_DestroyMutex(wait_mutex);
        wait_mutex = NULL;  
    }

    if (ic && !is->ic)
        avformat_close_input( &ic );

    av_packet_free( &pkt );
    if (ret != 0) {
        SDL_Event event;

        event.type = FF_QUIT_EVENT;
        event.user.data1 = is;
        SDL_PushEvent( &event );
       }

    return 0;
    }


int subtitle_thread( void* arg )
    {
    VideoState* is = arg;
    Frame* sp;
    int got_subtitle;
    double pts;

    for (;;) {
        if (!( sp = frame_queue_peek_writable( &is->subpq ) ))
            return 0;

        if (( got_subtitle = decoder_decode_frame( &is->subdec , NULL , &sp->sub ) ) < 0)
            break;

        pts = 0;

        if (got_subtitle && sp->sub.format == 0) {
            if (sp->sub.pts != AV_NOPTS_VALUE)
                pts = sp->sub.pts / ( double ) AV_TIME_BASE;
            sp->pts = pts;
            sp->serial = is->subdec.pkt_serial;
            sp->width = is->subdec.avctx->width;
            sp->height = is->subdec.avctx->height;
            sp->uploaded = 0;

            /* now we can update the picture count */
            frame_queue_push( &is->subpq );
            }
        else if (got_subtitle) {
            avsubtitle_free( &sp->sub );
            }
        }
    return 0;
    }

int video_thread( void* arg )
    {
    VideoState* is = arg;
    AVFrame* frame = av_frame_alloc( );
    double pts;
    double duration;
    int ret;
    AVRational tb = is->video_st->time_base;
    AVRational frame_rate = av_guess_frame_rate( is->ic , is->video_st , NULL );

    AVFilterGraph* graph = NULL;
    AVFilterContext* filt_out = NULL , * filt_in = NULL;
    int last_w = 0;
    int last_h = 0;
    enum AVPixelFormat last_format = -2;
    int last_serial = -1;
    int last_vfilter_idx = 0;

    if (!frame)
        return AVERROR( ENOMEM );

    for (;;) {
        ret = get_video_frame( is , frame );
        if (ret < 0)
            goto the_end;
        if (!ret)
            continue;

        if (last_w != frame->width
            || last_h != frame->height
            || last_format != frame->format
            || last_serial != is->viddec.pkt_serial
            || last_vfilter_idx != is->vfilter_idx) {
            av_log( NULL , AV_LOG_DEBUG ,
                "Video frame changed from size:%dx%d format:%s serial:%d to size:%dx%d format:%s serial:%d\n" ,
                last_w , last_h ,
                ( const char* ) av_x_if_null( av_get_pix_fmt_name( last_format ) , "none" ) , last_serial ,
                frame->width , frame->height ,
                ( const char* ) av_x_if_null( av_get_pix_fmt_name( frame->format ) , "none" ) , is->viddec.pkt_serial );
            avfilter_graph_free( &graph );
            graph = avfilter_graph_alloc( );
            if (!graph) {
                ret = AVERROR( ENOMEM );
                goto the_end;
                }
            graph->nb_threads = filter_nbthreads;
            if (( ret = configure_video_filters( graph , is , vfilters_list ? vfilters_list [ is->vfilter_idx ] : NULL , frame ) ) < 0) {
                SDL_Event event;
                event.type = FF_QUIT_EVENT;
                event.user.data1 = is;
                SDL_PushEvent( &event );
                goto the_end;
                }
            filt_in = is->in_video_filter;
            filt_out = is->out_video_filter;
            last_w = frame->width;
            last_h = frame->height;
            last_format = frame->format;
            last_serial = is->viddec.pkt_serial;
            last_vfilter_idx = is->vfilter_idx;
            frame_rate = av_buffersink_get_frame_rate( filt_out );
            }

        ret = av_buffersrc_add_frame( filt_in , frame );
        if (ret < 0)
            goto the_end;

        while (ret >= 0) {
            FrameData* fd;

            is->frame_last_returned_time = av_gettime_relative( ) / 1000000.0;

            ret = av_buffersink_get_frame_flags( filt_out , frame , 0 );
            if (ret < 0) {
                if (ret == AVERROR_EOF)
                    is->viddec.finished = is->viddec.pkt_serial;
                ret = 0;
                break;
                }

            fd = frame->opaque_ref ? ( FrameData* ) frame->opaque_ref->data : NULL;

            is->frame_last_filter_delay = av_gettime_relative( ) / 1000000.0 - is->frame_last_returned_time;
            if (fabs( is->frame_last_filter_delay ) > AV_NOSYNC_THRESHOLD / 10.0)
                is->frame_last_filter_delay = 0;
            tb = av_buffersink_get_time_base( filt_out );
            duration = ( frame_rate.num && frame_rate.den ? av_q2d( ( AVRational ) { frame_rate.den , frame_rate.num } ) : 0 );
            pts = ( frame->pts == AV_NOPTS_VALUE ) ? NAN : frame->pts * av_q2d( tb );
            ret = queue_picture( is , frame , pts , duration , fd ? fd->pkt_pos : -1 , is->viddec.pkt_serial );
            av_frame_unref( frame );
            if (is->videoq.serial != is->viddec.pkt_serial)
                break;
            }

        if (ret < 0)
            goto the_end;
        }
the_end:
    avfilter_graph_free( &graph );
    av_frame_free( &frame );
    return 0;
    }

int audio_thread( void* arg )
        {
        VideoState* is = arg;
        AVFrame* frame = av_frame_alloc( );
        Frame* af;
        int last_serial = -1;
        int reconfigure;
        int got_frame = 0;
        AVRational tb;
        int ret = 0;

        if (!frame)
            return AVERROR( ENOMEM );

        do {
            if (( got_frame = decoder_decode_frame( &is->auddec , frame , NULL ) ) < 0)
                goto the_end;

            if (got_frame) {
                tb = ( AVRational ) { 1, frame->sample_rate };

                reconfigure =
                    cmp_audio_fmts( is->audio_filter_src.fmt , is->audio_filter_src.ch_layout.nb_channels ,
                        frame->format , frame->ch_layout.nb_channels ) ||
                    av_channel_layout_compare( &is->audio_filter_src.ch_layout , &frame->ch_layout ) ||
                    is->audio_filter_src.freq != frame->sample_rate ||
                    is->auddec.pkt_serial != last_serial;

                if (reconfigure) {
                    char buf1 [ 1024 ] , buf2 [ 1024 ];
                    av_channel_layout_describe( &is->audio_filter_src.ch_layout , buf1 , sizeof( buf1 ) );
                    av_channel_layout_describe( &frame->ch_layout , buf2 , sizeof( buf2 ) );
                    av_log( NULL , AV_LOG_DEBUG ,
                        "Audio frame changed from rate:%d ch:%d fmt:%s layout:%s serial:%d to rate:%d ch:%d fmt:%s layout:%s serial:%d\n" ,
                        is->audio_filter_src.freq , is->audio_filter_src.ch_layout.nb_channels , av_get_sample_fmt_name( is->audio_filter_src.fmt ) , buf1 , last_serial ,
                        frame->sample_rate , frame->ch_layout.nb_channels , av_get_sample_fmt_name( frame->format ) , buf2 , is->auddec.pkt_serial );

                    is->audio_filter_src.fmt = frame->format;
                    ret = av_channel_layout_copy( &is->audio_filter_src.ch_layout , &frame->ch_layout );
                    if (ret < 0)
                        goto the_end;
                    is->audio_filter_src.freq = frame->sample_rate;
                    last_serial = is->auddec.pkt_serial;

                    if (( ret = configure_audio_filters( is , afilters , 1 ) ) < 0)
                        goto the_end;
                    }

                if (( ret = av_buffersrc_add_frame( is->in_audio_filter , frame ) ) < 0)
                    goto the_end;

                while (( ret = av_buffersink_get_frame_flags( is->out_audio_filter , frame , 0 ) ) >= 0) {
                    FrameData* fd = frame->opaque_ref ? ( FrameData* ) frame->opaque_ref->data : NULL;
                    tb = av_buffersink_get_time_base( is->out_audio_filter );
                    if (!( af = frame_queue_peek_writable( &is->sampq ) ))
                        goto the_end;

                    af->pts = ( frame->pts == AV_NOPTS_VALUE ) ? NAN : frame->pts * av_q2d( tb );
                    af->pos = fd ? fd->pkt_pos : -1;
                    af->serial = is->auddec.pkt_serial;
                    af->duration = av_q2d( ( AVRational ) { frame->nb_samples , frame->sample_rate } );

                    av_frame_move_ref( af->frame , frame );
                    frame_queue_push( &is->sampq );

                    if (is->audioq.serial != is->auddec.pkt_serial)
                        break;
                    }
                if (ret == AVERROR_EOF)
                    is->auddec.finished = is->auddec.pkt_serial;
                }
            }
        while (ret >= 0 || ret == AVERROR( EAGAIN ) || ret == AVERROR_EOF);
        the_end:
            avfilter_graph_free( &is->agraph );
            av_frame_free( &frame );
            return ret;
        }

int queue_picture( VideoState* is , AVFrame* src_frame , double pts , double duration , int64_t pos , int serial )
    {
    Frame* vp;

#if defined(DEBUG_SYNC)
    printf( "frame_type=%c pts=%0.3f\n" ,
        av_get_picture_type_char( src_frame->pict_type ) , pts );
#endif

    if (!( vp = frame_queue_peek_writable( &is->pictq ) ))
        return -1;

    vp->sar = src_frame->sample_aspect_ratio;
    vp->uploaded = 0;

    vp->width = src_frame->width;
    vp->height = src_frame->height;
    vp->format = src_frame->format;

    vp->pts = pts;
    vp->duration = duration;
    vp->pos = pos;
    vp->serial = serial;

    set_default_window_size( vp->width , vp->height , vp->sar );

    av_frame_move_ref( vp->frame , src_frame );
    frame_queue_push( &is->pictq );

    /* Seek 之后的第一帧入列，发送 SEEK_DONE 事件 */
    if (g_seek_in_progress) {
        g_seek_in_progress = 0;
        FF_Internal_SeekDone();
    }

    return 0;
    }


int get_video_frame( VideoState* is , AVFrame* frame )
    {
    int got_picture;

    if (( got_picture = decoder_decode_frame( &is->viddec , frame , NULL ) ) < 0)
        return -1;

    if (got_picture) {
        double dpts = NAN;

        if (frame->pts != AV_NOPTS_VALUE)
            dpts = av_q2d( is->video_st->time_base ) * frame->pts;

        frame->sample_aspect_ratio = av_guess_sample_aspect_ratio( is->ic , is->video_st , frame );

        if (framedrop > 0 || ( framedrop && get_master_sync_type( is ) != AV_SYNC_VIDEO_MASTER )) {
            if (frame->pts != AV_NOPTS_VALUE) {
                double diff = dpts - get_master_clock( is );
                if (!isnan( diff ) && fabs( diff ) < AV_NOSYNC_THRESHOLD &&
                    diff - is->frame_last_filter_delay < 0 &&
                    is->viddec.pkt_serial == is->vidclk.serial &&
                    is->videoq.nb_packets) {
                    is->frame_drops_early++;
                    av_frame_unref( frame );
                    got_picture = 0;
                    }
                }
            }
        }

    return got_picture;
    }

void set_default_window_size( int width , int height , AVRational sar )
    {
    SDL_Rect rect;
    int max_width = screen_width ? screen_width : INT_MAX;
    int max_height = screen_height ? screen_height : INT_MAX;
    if (max_width == INT_MAX && max_height == INT_MAX)
        max_height = height;
    calculate_display_rect( &rect , 0 , 0 , max_width , max_height , width , height , sar );
    default_width = rect.w;
    default_height = rect.h;
    }

int is_realtime( AVFormatContext* s )
    {
    if (!strcmp( s->iformat->name , "rtp" )
        || !strcmp( s->iformat->name , "rtsp" )
        || !strcmp( s->iformat->name , "sdp" )
        )
        return 1;

    if (s->pb && ( !strncmp( s->url , "rtp:" , 4 )
        || !strncmp( s->url , "udp:" , 4 )
        )
        )
        return 1;
    return 0;
    }