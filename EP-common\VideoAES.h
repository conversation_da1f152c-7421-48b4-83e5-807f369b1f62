﻿#pragma once

#include "AESEncryption.h"
#include <memory>
#include <cstdint>  // for uint8_t

// 视频AES加密解密类
class CVideoAES
{
public:
    CVideoAES();
    virtual ~CVideoAES();

    // 初始化加密器
    bool Initialize(const std::string& password);
    
    // 视频数据加密和解密
    bool EncryptPacket(uint8_t* data, size_t size);
    bool DecryptPacket(uint8_t* data, size_t size);
    
    // 部分加密和解密（从指定位置开始）
    bool EncryptPacketPartial(uint8_t* data, size_t size, size_t start_pos);
    bool DecryptPacketPartial(uint8_t* data, size_t size, size_t start_pos);
    
    // 部分加密和解密（从指定位置开始，指定长度）
    bool EncryptPacketPartialWithLength(uint8_t* data, size_t size, size_t start_pos, size_t max_length);
    bool DecryptPacketPartialWithLength(uint8_t* data, size_t size, size_t start_pos, size_t max_length);
    
    // 音频数据加密和解密（简单异或方式）
    void EncryptAudioPacket(uint8_t* data, size_t size);
    void DecryptAudioPacket(uint8_t* data, size_t size);
    
    // 音频数据部分加密和解密
    void EncryptAudioPacketPartial(uint8_t* data, size_t size, size_t start_pos);
    void DecryptAudioPacketPartial(uint8_t* data, size_t size, size_t start_pos);

private:
    std::unique_ptr<CAESEncryption> m_pAESEncryption;
    std::vector<uint8_t> m_tempBuffer;
    bool m_bInitialized;
    
    // 辅助函数
    bool EncryptDataBlock(uint8_t* data, size_t size, size_t start_pos = 0, size_t max_length = 0);
    bool DecryptDataBlock(uint8_t* data, size_t size, size_t start_pos = 0, size_t max_length = 0);
}; 