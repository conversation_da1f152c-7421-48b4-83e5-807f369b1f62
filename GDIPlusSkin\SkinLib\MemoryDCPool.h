#pragma once
#include <queue>
#include <mutex>
#include <memory>

// 内存DC池，避免频繁创建销毁DC
class CMemoryDCPool
{
public:
    struct DCItem {
        CDC dc;
        CBitmap bitmap;
        CSize size;
        DWORD lastUsed;
    };

    static CMemoryDCPool& Instance() {
        static CMemoryDCPool instance;
        return instance;
    }

    // 获取或创建一个指定大小的内存DC
    std::shared_ptr<DCItem> AcquireDC(CDC* pDC, int width, int height) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 清理超时的DC（超过30秒未使用）
        DWORD now = GetTickCount();
        while (!m_pool.empty() && (now - m_pool.front()->lastUsed > 30000)) {
            m_pool.pop();
        }

        // 查找合适大小的DC
        std::shared_ptr<DCItem> item;
        if (!m_pool.empty() && m_pool.front()->size.cx >= width && m_pool.front()->size.cy >= height) {
            item = m_pool.front();
            m_pool.pop();
        } else {
            // 创建新的DC
            item = std::make_shared<DCItem>();
            item->dc.CreateCompatibleDC(pDC);
            item->bitmap.CreateCompatibleBitmap(pDC, width, height);
            item->dc.SelectObject(&item->bitmap);
            item->size = CSize(width, height);
        }
        
        item->lastUsed = now;
        return item;
    }

    // 归还DC到池中
    void ReleaseDC(std::shared_ptr<DCItem> item) {
        if (!item) return;
        
        std::lock_guard<std::mutex> lock(m_mutex);
        item->lastUsed = GetTickCount();
        
        // 池大小限制为10个
        if (m_pool.size() < 10) {
            m_pool.push(item);
        }
    }

private:
    CMemoryDCPool() = default;
    ~CMemoryDCPool() = default;
    
    std::queue<std::shared_ptr<DCItem>> m_pool;
    std::mutex m_mutex;
};
