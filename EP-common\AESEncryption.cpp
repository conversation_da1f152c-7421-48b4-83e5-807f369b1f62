﻿#include "pch.h"
#include "AESEncryption.h"
#include <iostream>
#include <cstring>    // for memcpy
#include <algorithm>  // for std::min
#include <windows.h>  // for OutputDebugStringA

using namespace CryptoPP;

CAESEncryption::CAESEncryption(const std::string& password)
    : IEncryption(password), m_bInitialized(false)
{
    memset(m_key, 0, sizeof(m_key));
    memset(m_iv, 0, sizeof(m_iv));
    memset(m_counter, 0, sizeof(m_counter));
    
    InitializeEncryption( password );
}

CAESEncryption::~CAESEncryption()
{
    // 清理敏感数据
    memset(m_key, 0, sizeof(m_key));
    memset(m_iv, 0, sizeof(m_iv));
    memset(m_counter, 0, sizeof(m_counter));
}

bool CAESEncryption::InitializeEncryption( const std::string& password )
    {
    if (m_bInitialized) {
        return m_bInitialized;
        }

    DeriveKeyFromPassword( password , m_key , m_iv );
    // 初始化计数器为IV
    memcpy( m_counter , m_iv , 16 );
    m_bInitialized = true;

    return m_bInitialized;
    }

void CAESEncryption::DeriveKeyFromPassword(const std::string& password, 
                                         unsigned char* key, unsigned char* iv)
{
    try
    {
        // 使用SHA256从密码派生密钥
        SHA256 hash;
        std::string seed = password + "VE_SALT_2024"; // 添加盐值
        
        // 生成密钥 (32字节)
        hash.Update((const byte*)seed.c_str(), seed.length());
        hash.Final(key);
        
        // 生成IV (16字节) - 使用密码+不同盐值
        hash.Restart();
        seed = password + "VE_IV_SALT_2024";
        hash.Update((const byte*)seed.c_str(), seed.length());
        
        byte temp_hash[32];
        hash.Final(temp_hash);
        memcpy(iv, temp_hash, 16); // 只取前16字节作为IV
    }
    catch (...)
    {
        // 出错时使用简单的密钥派生
        size_t pwd_len = password.length();
        for (int i = 0; i < 32; i++)
        {
            key[i] = (unsigned char)(password[i % pwd_len] ^ (i + 0x5A));
        }
        for (int i = 0; i < 16; i++)
        {
            iv[i] = (unsigned char)(password[i % pwd_len] ^ (i + 0xA5));
        }
    }
}



size_t CAESEncryption::GetOutputSize(size_t input_size)
{
    // CTR模式是流加密，输出大小等于输入大小
    return input_size;
}

bool CAESEncryption::EncryptData(const unsigned char* input, size_t input_len, 
                                unsigned char* output, size_t& output_len)
{
    if (!m_bInitialized || !input || !output)
        return false;

    try
    {
        // 检查输出缓冲区大小
        if (output_len < input_len)
        {
            output_len = input_len;
            return false;
        }

        // 使用基础计数器 - 修复CTR模式兼容性问题
        // CTR模式的安全性主要依赖密钥和IV的随机性，无需额外复杂化
        CTR_Mode<AES>::Encryption encryption;
        encryption.SetKeyWithIV(m_key, 32, m_counter, 16);

        // 执行加密
        ArraySource source(input, input_len, true,
            new StreamTransformationFilter(encryption,
                new ArraySink(output, input_len)
            )
        );

        output_len = input_len;
        return true;
    }
    catch (const CryptoPP::Exception& e)
    {
        // CryptoPP异常，输出详细信息
        OutputDebugStringA("AES加密失败 - CryptoPP异常: ");
        OutputDebugStringA(e.what());
        OutputDebugStringA("\n");
        return false;
    }
    catch (const std::exception& e)
    {
        // 标准异常
        OutputDebugStringA("AES加密失败 - 标准异常: ");
        OutputDebugStringA(e.what());
        OutputDebugStringA("\n");
        return false;
    }
    catch (...)
    {
        // 未知异常
        OutputDebugStringA("AES加密失败 - 未知异常\n");
        return false;
    }
}

bool CAESEncryption::DecryptData(const unsigned char* input, size_t input_len, 
                                unsigned char* output, size_t& output_len)
{
    if (!m_bInitialized || !input || !output)
        return false;

    try
    {
        // 检查输出缓冲区大小
        if (output_len < input_len)
        {
            output_len = input_len;
            return false;
        }

        // 使用基础计数器 - 与加密完全一致
        CTR_Mode<AES>::Decryption decryption;
        decryption.SetKeyWithIV(m_key, 32, m_counter, 16);

        // 执行解密
        ArraySource source(input, input_len, true,
            new StreamTransformationFilter(decryption,
                new ArraySink(output, input_len)
            )
        );

        output_len = input_len;
        return true;
    }
    catch (const CryptoPP::Exception& e)
    {
        // CryptoPP异常，输出详细信息
        OutputDebugStringA("AES解密失败 - CryptoPP异常: ");
        OutputDebugStringA(e.what());
        OutputDebugStringA("\n");
        return false;
    }
    catch (const std::exception& e)
    {
        // 标准异常
        OutputDebugStringA("AES解密失败 - 标准异常: ");
        OutputDebugStringA(e.what());
        OutputDebugStringA("\n");
        return false;
    }
    catch (...)
    {
        // 未知异常
        OutputDebugStringA("AES解密失败 - 未知异常\n");
        return false;
    }
}

