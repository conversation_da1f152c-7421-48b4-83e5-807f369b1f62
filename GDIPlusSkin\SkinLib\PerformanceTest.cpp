#include "pch.h"
#include "PerformanceMonitor.h"
#include "MemoryDCPool.h"
#include "FastImageRenderer.h"

// 性能测试示例

// 测试优化前后的绘制性能
void TestDrawPerformance()
{
    CDC* pDC = GetDC(AfxGetMainWnd());
    CRect rect(0, 0, 800, 600);
    
    // 测试传统方式
    {
        PERF_TIMER("Traditional Draw");
        
        // 传统方式：每次创建新的内存DC
        for (int i = 0; i < 100; i++) {
            CDC memDC;
            memDC.CreateCompatibleDC(pDC);
            CBitmap bitmap;
            bitmap.CreateCompatibleBitmap(pDC, rect.Width(), rect.Height());
            CBitmap* pOldBmp = memDC.SelectObject(&bitmap);
            
            // 绘制操作...
            Gdiplus::Graphics graphics(memDC.GetSafeHdc());
            Gdiplus::SolidBrush brush(Gdiplus::Color(255, 0, 0, 255));
            graphics.FillRectangle(&brush, 0, 0, rect.Width(), rect.Height());
            
            memDC.SelectObject(pOldBmp);
            // DC和Bitmap自动销毁
        }
    }
    
    // 测试优化方式
    {
        PERF_TIMER("Optimized Draw");
        
        // 优化方式：使用DC池
        for (int i = 0; i < 100; i++) {
            auto dcItem = CMemoryDCPool::Instance().AcquireDC(pDC, rect.Width(), rect.Height());
            
            // 绘制操作...
            Gdiplus::Graphics graphics(dcItem->dc.GetSafeHdc());
            Gdiplus::SolidBrush brush(Gdiplus::Color(255, 0, 0, 255));
            graphics.FillRectangle(&brush, 0, 0, rect.Width(), rect.Height());
            
            CMemoryDCPool::Instance().ReleaseDC(dcItem);
        }
    }
    
    ReleaseDC(AfxGetMainWnd(), pDC);
}

// 测试图片渲染性能
void TestImageRenderPerformance()
{
    // 加载测试图片
    Gdiplus::Image* pImage = CGPImageInfo::Instance()->ImageFromFile(L"test.png");
    if (!pImage) return;
    
    CDC* pDC = GetDC(AfxGetMainWnd());
    Gdiplus::Graphics graphics(pDC->GetSafeHdc());
    Gdiplus::RectF destRect(0, 0, 100, 100);
    
    // 测试传统GDI+绘制
    {
        PERF_TIMER("Traditional Image Draw");
        
        for (int i = 0; i < 1000; i++) {
            graphics.DrawImage(pImage, destRect);
        }
    }
    
    // 预处理图片
    CFastImageRenderer::Instance().PreprocessImage(pImage, L"test");
    
    // 测试优化后的绘制
    {
        PERF_TIMER("Optimized Image Draw");
        
        CFastImageRenderer::Instance().BeginBatchDraw(&graphics);
        for (int i = 0; i < 1000; i++) {
            CFastImageRenderer::Instance().FastDrawImage(&graphics, L"test", destRect);
        }
        CFastImageRenderer::Instance().EndBatchDraw(&graphics);
    }
    
    ReleaseDC(AfxGetMainWnd(), pDC);
}

// 在应用程序中使用
void RunPerformanceTests()
{
    TRACE(L"Starting performance tests...\n");
    
    // 重置统计
    CPerformanceMonitor::Instance().Reset();
    
    // 运行测试
    TestDrawPerformance();
    TestImageRenderPerformance();
    
    // 输出报告
    PERF_REPORT();
}

// 在实际控件中使用性能监控
class COptimizedButton : public CButton
{
protected:
    virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) override
    {
        PERF_TIMER("Button Draw");
        
        CDC dc;
        dc.Attach(lpDrawItemStruct->hDC);
        
        // 使用优化的绘制逻辑...
        CRect rect = lpDrawItemStruct->rcItem;
        
        // 只在需要时重绘
        if (m_bNeedRedraw) {
            // 实际绘制代码...
            m_bNeedRedraw = false;
        }
        
        dc.Detach();
    }
    
private:
    bool m_bNeedRedraw = true;
};
