﻿// CDlgSkin.cpp: 实现文件
//

#include "stdafx.h"
#include "CDlgSkin.h"
#include "afxdialogex.h"


// CDlgSkin 对话框

IMPLEMENT_DYNAMIC( CDlgSkin , CGPDlgSkinBase )

CDlgSkin::CDlgSkin( CWnd* pParent /*=NULL*/ )
    : CGPDlgSkinBase( IDD_DIALOG_SKIN , pParent )
    , m_pWnd( NULL )
    {

    }

CDlgSkin::~CDlgSkin( )
    { }

void CDlgSkin::DoDataExchange( CDataExchange* pDX )
    {
    CGPDlgSkinBase::DoDataExchange( pDX );
    }


BEGIN_MESSAGE_MAP( CDlgSkin , CGPDlgSkinBase )
    ON_COMMAND( 1888 , &CDlgSkin::OnBtnSkin1 )
    ON_COMMAND( 1889 , &CDlgSkin::OnBtnSkin2 )
    ON_COMMAND( 1890 , &CDlgSkin::OnBtnSkin3 )
    ON_COMMAND( 1891 , &CDlgSkin::OnBtnSkin4 )
    ON_COMMAND( 1892 , &CDlgSkin::OnBtnSkin5 )
    ON_COMMAND( 1893 , &CDlgSkin::OnBtnSkin6 )
    ON_COMMAND( 1894 , &CDlgSkin::OnBtnSkin7 )
    ON_COMMAND( 1895 , &CDlgSkin::OnBtnSkin8 )
    ON_COMMAND( 1896 , &CDlgSkin::OnBtnSkin9 )
    ON_COMMAND( 1897 , &CDlgSkin::OnBtnSkin10 )
    ON_COMMAND( 1898 , &CDlgSkin::OnBtnSkin11 )
    ON_COMMAND( 1899 , &CDlgSkin::OnBtnSkin12 )
    ON_COMMAND( 1900 , &CDlgSkin::OnBtnSkin13 )
    ON_COMMAND( 1901 , &CDlgSkin::OnBtnSkin14 )
    ON_COMMAND( 1902 , &CDlgSkin::OnBtnSkin15 )
    ON_COMMAND( 1903 , &CDlgSkin::OnBtnSkin16 )
END_MESSAGE_MAP( )


// CDlgSkin 消息处理程序


BOOL CDlgSkin::OnInitDialog( )
    {

    CGPDlgSkinBase::OnInitDialog( );

    SetWindowPos( NULL , 0 , 0 , ( int ) ( 620 * GetPix( ) ) , ( int ) ( 535 * GetPix( ) ) , SWP_NOMOVE );
    m_skin1.CreatePicture( this , CRect( 20 , 55 , 150 , 155 ) , GetSkinPath( 0 ) , TRUE , 1888 );
    m_skin1.SetCursor( IDC_CURSOR1 );
    m_skin1.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );

    if (0 == g_stSoftSetup.iSkin) {
        m_skin1.SetSelect( TRUE );
        }

    AddItem( m_skin1 );

    m_skin2.CreatePicture( this , CRect( 170 , 55 , 300 , 155 ) , GetSkinPath( 1 ) , TRUE , 1889 );
    m_skin2.SetCursor( IDC_CURSOR1 );
    m_skin2.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (1 == g_stSoftSetup.iSkin) {
        m_skin2.SetSelect( TRUE );
        }
    AddItem( m_skin2 );

    m_skin3.CreatePicture( this , CRect( 320 , 55 , 450 , 155 ) , GetSkinPath( 2 ) , TRUE , 1890 );
    m_skin3.SetCursor( IDC_CURSOR1 );
    m_skin3.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (2 == g_stSoftSetup.iSkin) {
        m_skin3.SetSelect( TRUE );
        }
    AddItem( m_skin3 );


    m_skin4.CreatePicture( this , CRect( 470 , 55 , 600 , 155 ) , GetSkinPath( 3 ) , TRUE , 1891 );
    m_skin4.SetCursor( IDC_CURSOR1 );
    m_skin4.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (3 == g_stSoftSetup.iSkin) {
        m_skin4.SetSelect( TRUE );
        }
    AddItem( m_skin4 );

    m_skin5.CreatePicture( this , CRect( 20 , 175 , 150 , 275 ) , GetSkinPath( 4 ) , TRUE , 1892 );
    m_skin5.SetCursor( IDC_CURSOR1 );
    m_skin5.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (4 == g_stSoftSetup.iSkin) {
        m_skin5.SetSelect( TRUE );
        }
    AddItem( m_skin5 );

    m_skin6.CreatePicture( this , CRect( 170 , 175 , 300 , 275 ) , GetSkinPath( 5 ) , TRUE , 1893 );
    m_skin6.SetCursor( IDC_CURSOR1 );
    m_skin6.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (5 == g_stSoftSetup.iSkin) {
        m_skin6.SetSelect( TRUE );
        }
    AddItem( m_skin6 );

    m_skin7.CreatePicture( this , CRect( 320 , 175 , 450 , 275 ) , GetSkinPath( 6 ) , TRUE , 1894 );
    m_skin7.SetCursor( IDC_CURSOR1 );
    m_skin7.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (6 == g_stSoftSetup.iSkin) {
        m_skin7.SetSelect( TRUE );
        }
    AddItem( m_skin7 );

    m_skin8.CreatePicture( this , CRect( 470 , 175 , 600 , 275 ) , GetSkinPath( 7 ) , TRUE , 1895 );
    m_skin8.SetCursor( IDC_CURSOR1 );
    m_skin8.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (7 == g_stSoftSetup.iSkin) {
        m_skin8.SetSelect( TRUE );
        }
    AddItem( m_skin8 );

    m_skin9.CreatePicture( this , CRect( 20 , 295 , 150 , 395 ) , GetSkinPath( 8 ) , TRUE , 1896 );
    m_skin9.SetCursor( IDC_CURSOR1 );
    m_skin9.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (8 == g_stSoftSetup.iSkin) {
        m_skin9.SetSelect( TRUE );
        }
    AddItem( m_skin9 );

    m_skin10.CreatePicture( this , CRect( 170 , 295 , 300 , 395 ) , GetSkinPath( 9 ) , TRUE , 1897 );
    m_skin10.SetCursor( IDC_CURSOR1 );
    m_skin10.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (9 == g_stSoftSetup.iSkin) {
        m_skin10.SetSelect( TRUE );
        }
    AddItem( m_skin10 );

    m_skin11.CreatePicture( this , CRect( 320 , 295 , 450 , 395 ) , GetSkinPath( 10 ) , TRUE , 1898 );
    m_skin11.SetCursor( IDC_CURSOR1 );
    m_skin11.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (10 == g_stSoftSetup.iSkin) {
        m_skin11.SetSelect( TRUE );
        }
    AddItem( m_skin11 );

    m_skin12.CreatePicture( this , CRect( 470 , 295 , 600 , 395 ) , GetSkinPath( 11 ) , TRUE , 1899 );
    m_skin12.SetCursor( IDC_CURSOR1 );
    m_skin12.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (11 == g_stSoftSetup.iSkin) {
        m_skin12.SetSelect( TRUE );
        }
    AddItem( m_skin12 );

    m_skin13.CreatePicture( this , CRect( 20 , 415 , 150 , 515 ) , GetSkinPath( 12 ) , TRUE , 1900 );
    m_skin13.SetCursor( IDC_CURSOR1 );
    m_skin13.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (12 == g_stSoftSetup.iSkin) {
        m_skin13.SetSelect( TRUE );
        }
    AddItem( m_skin13 );

    m_skin14.CreatePicture( this , CRect( 170 , 415 , 300 , 515 ) , GetSkinPath( 13 ) , TRUE , 1901 );
    m_skin14.SetCursor( IDC_CURSOR1 );
    m_skin14.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (13 == g_stSoftSetup.iSkin) {
        m_skin14.SetSelect( TRUE );
        }
    AddItem( m_skin14 );

    m_skin15.CreatePicture( this , CRect( 320 , 415 , 450 , 515 ) , GetSkinPath( 14 ) , TRUE , 1902 );
    m_skin15.SetCursor( IDC_CURSOR1 );
    m_skin15.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (14 == g_stSoftSetup.iSkin) {
        m_skin15.SetSelect( TRUE );
        }
    AddItem( m_skin15 );

    m_skin16.CreatePicture( this , CRect( 470 , 415 , 600 , 515 ) , GetSkinPath( 15 ) , TRUE , 1903 );
    m_skin16.SetCursor( IDC_CURSOR1 );
    m_skin16.SetSelect( _T( "msgicon\\msg_icon_check.png" ) );
    if (15 == g_stSoftSetup.iSkin) {
        m_skin16.SetSelect( TRUE );
        }
    AddItem( m_skin16 );

    RenewBK( );
    SetWindowText( _T( "皮肤中心" ) );
    return TRUE;  // return TRUE unless you set the focus to a control
                  // 异常: OCX 属性页应返回 FALSE
    }

void CDlgSkin::SetSkin( int iSkin )
    {
    if (0 > iSkin || 15 < iSkin) {
        iSkin = 0;
        }

    g_stSoftSetup.iSkin = iSkin;
    GDIPlusSetBK( g_stSoftSetup.iSkin );

    RenewBK( TRUE );
    m_pWnd->SendMessage( UM_WND_SKIN );

    CSoftSetup softSetup;
    softSetup.WriteSetup( g_stSoftSetup );
    }

void CDlgSkin::OnBtnSkin1( )
    {
    SetSkin( 0 );
    }

void CDlgSkin::OnBtnSkin2( )
    {
    SetSkin( 1 );
    }

void CDlgSkin::OnBtnSkin3( )
    {
    SetSkin( 2 );
    }

void CDlgSkin::OnBtnSkin4( )
    {
    SetSkin( 3 );
    }

void CDlgSkin::OnBtnSkin5( )
    {
    SetSkin( 4 );
    }

void CDlgSkin::OnBtnSkin6( )
    {
    SetSkin( 5 );
    }

void CDlgSkin::OnBtnSkin7( )
    {
    SetSkin( 6 );
    }

void CDlgSkin::OnBtnSkin8( )
    {
    SetSkin( 7 );
    }

void CDlgSkin::OnBtnSkin9( )
    {
    SetSkin( 8 );
    }

void CDlgSkin::OnBtnSkin10( )
    {
    SetSkin( 9 );
    }

void CDlgSkin::OnBtnSkin11( )
    {
    SetSkin( 10 );
    }

void CDlgSkin::OnBtnSkin12( )
    {
    SetSkin( 11 );
    }

void CDlgSkin::OnBtnSkin13( )
    {
    SetSkin( 12 );
    }

void CDlgSkin::OnBtnSkin14( )
    {
    SetSkin( 13 );
    }

void CDlgSkin::OnBtnSkin15( )
    {
    SetSkin( 14 );
    }

void CDlgSkin::OnBtnSkin16( )
    {
    SetSkin( 15 );
    }