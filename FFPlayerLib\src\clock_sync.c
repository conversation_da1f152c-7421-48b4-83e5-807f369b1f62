﻿#include "clock_sync.h"
void update_video_pts( VideoState* is , double pts , int serial )
    {
    /* update current video pts */
    set_clock( &is->vidclk , pts , serial );
    sync_clock_to_slave( &is->extclk , &is->vidclk );
    }

double get_clock( Clock* c )
    {
    if (*c->queue_serial != c->serial)
        return NAN;

    if (c->paused) {
        return c->pts;
        }
    else {
        double time = av_gettime_relative( ) / 1000000.0;
        return c->pts_drift + time - ( time - c->last_updated ) * ( 1.0 - c->speed );
        }
    }

void set_clock_at( Clock* c , double pts , int serial , double time )
    {
    c->pts = pts;
    c->last_updated = time;
    c->pts_drift = c->pts - time;
    c->serial = serial;
    }

void set_clock( Clock* c , double pts , int serial )
    {
    double time = av_gettime_relative( ) / 1000000.0;
    set_clock_at( c , pts , serial , time );
    }

void set_clock_speed( Clock* c , double speed )
    {
    set_clock( c , get_clock( c ) , c->serial );
    c->speed = speed;
    }

void init_clock( Clock* c , int* queue_serial )
    {
    c->speed = 1.0;
    c->paused = 0;
    c->queue_serial = queue_serial;
    set_clock( c , NAN , -1 );
    }

void sync_clock_to_slave( Clock* c , Clock* slave )
    {
    double clock = get_clock( c );
    double slave_clock = get_clock( slave );
    if (!isnan( slave_clock ) && ( isnan( clock ) || fabs( clock - slave_clock ) > AV_NOSYNC_THRESHOLD ))
        set_clock( c , slave_clock , slave->serial );
    }

int get_master_sync_type( VideoState* is )
    {
    if (is->av_sync_type == AV_SYNC_VIDEO_MASTER) {
        if (is->video_st)
            return AV_SYNC_VIDEO_MASTER;
        else
            return AV_SYNC_AUDIO_MASTER;
        }
    else if (is->av_sync_type == AV_SYNC_AUDIO_MASTER) {
        if (is->audio_st)
            return AV_SYNC_AUDIO_MASTER;
        else
            return AV_SYNC_EXTERNAL_CLOCK;
        }
    else {
        return AV_SYNC_EXTERNAL_CLOCK;
        }
    }

/* get the current master clock value */
double get_master_clock( VideoState* is )
    {
    double val = 0;

   
    switch (get_master_sync_type( is )) {
            case AV_SYNC_VIDEO_MASTER:
                val = get_clock( &is->vidclk );
                break;
            case AV_SYNC_AUDIO_MASTER:
                val = get_clock( &is->audclk );
                break;
            default:
                val = get_clock( &is->extclk );
                break;
        }
    
    return val;
    }

void check_external_clock_speed( VideoState* is )
    {
    if (is->video_stream >= 0 && is->videoq.nb_packets <= EXTERNAL_CLOCK_MIN_FRAMES ||
        is->audio_stream >= 0 && is->audioq.nb_packets <= EXTERNAL_CLOCK_MIN_FRAMES) {
        set_clock_speed( &is->extclk , FFMAX( EXTERNAL_CLOCK_SPEED_MIN , is->extclk.speed - EXTERNAL_CLOCK_SPEED_STEP ) );
        }
    else if (( is->video_stream < 0 || is->videoq.nb_packets > EXTERNAL_CLOCK_MAX_FRAMES ) &&
        ( is->audio_stream < 0 || is->audioq.nb_packets > EXTERNAL_CLOCK_MAX_FRAMES )) {
        set_clock_speed( &is->extclk , FFMIN( EXTERNAL_CLOCK_SPEED_MAX , is->extclk.speed + EXTERNAL_CLOCK_SPEED_STEP ) );
        }
    else {
        double speed = is->extclk.speed;
        if (speed != 1.0)
            set_clock_speed( &is->extclk , speed + EXTERNAL_CLOCK_SPEED_STEP * ( 1.0 - speed ) / fabs( 1.0 - speed ) );
        }
    }

double compute_target_delay( double delay , VideoState* is )
    {
    double sync_threshold , diff = 0;

    /* update delay to follow master synchronisation source */
    if (get_master_sync_type( is ) != AV_SYNC_VIDEO_MASTER) {
        /* if video is slave, we try to correct big delays by
           duplicating or deleting a frame */
        diff = get_clock( &is->vidclk ) - get_master_clock( is );

        /* skip or repeat frame. We take into account the
           delay to compute the threshold. I still don't know
           if it is the best guess */
        sync_threshold = FFMAX( AV_SYNC_THRESHOLD_MIN , FFMIN( AV_SYNC_THRESHOLD_MAX , delay ) );
        if (!isnan( diff ) && fabs( diff ) < is->max_frame_duration) {
            if (diff <= -sync_threshold)
                delay = FFMAX( 0 , delay + diff );
            else if (diff >= sync_threshold && delay > AV_SYNC_FRAMEDUP_THRESHOLD)
                delay = delay + diff;
            else if (diff >= sync_threshold)
                delay = 2 * delay;
            }
        }

    av_log( NULL , AV_LOG_TRACE , "video: delay=%0.3f A-V=%f\n" ,
        delay , -diff );

    return delay;
    }

double vp_duration( VideoState* is , Frame* vp , Frame* nextvp )
    {
    if (vp->serial == nextvp->serial) {
        double duration = nextvp->pts - vp->pts;
        if (isnan( duration ) || duration <= 0 || duration > is->max_frame_duration)
            return vp->duration;
        else
            return duration;
        }
    else {
        return 0.0;
        }
    }