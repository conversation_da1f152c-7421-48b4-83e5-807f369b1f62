﻿#pragma once

#include "VideoAES.h"

// FFmpeg前置声明
struct AVPacket;

// 视频类型枚举
enum VideoEncryptType
{
    VIDEO_ENCRYPT_FULL = 0,     // 完全加密
    VIDEO_ENCRYPT_PARTIAL = 1,  // 部分加密
    VIDEO_ENCRYPT_KEYFRAME = 2  // 仅关键帧加密
};

// 音频类型枚举
enum AudioEncryptType
{
    AUDIO_ENCRYPT_NONE = 0,     // 不加密
    AUDIO_ENCRYPT_SIMPLE = 1,   // 简单加密
    AUDIO_ENCRYPT_PARTIAL = 2   // 部分加密
};

// 关键帧检测结果枚举
enum KeyFrameDetectResult
{
    KEYFRAME_YES = 1,       // 确认是关键帧
    KEYFRAME_NO = 0,        // 确认不是关键帧
    KEYFRAME_UNKNOWN = -1   // 无法确定
};

// 通用视频AES加密解密类
class CVideoAESCommon : public CVideoAES
{
public:
    CVideoAESCommon();
    virtual ~CVideoAESCommon();

    // 根据视频类型进行加密
    bool EncryptVideoData(uint8_t* data, size_t size, VideoEncryptType video_type);
    bool DecryptVideoData(uint8_t* data, size_t size, VideoEncryptType video_type);

    // 根据音频类型进行加密
    void EncryptAudioData(uint8_t* data, size_t size, AudioEncryptType audio_type);
    void DecryptAudioData(uint8_t* data, size_t size, AudioEncryptType audio_type);

    // 新增：基于FFmpeg AVPacket的加密解密（推荐方式）
    bool EncryptVideoPacket(AVPacket* packet, VideoEncryptType video_type);
    bool DecryptVideoPacket(AVPacket* packet, VideoEncryptType video_type);

    // 设置加密参数
    void SetVideoEncryptOffset(size_t offset) { m_videoEncryptOffset = offset; }
    void SetAudioEncryptOffset(size_t offset) { m_audioEncryptOffset = offset; }
    void SetEncryptRatio(float ratio) { m_encryptRatio = ratio; }

    // 根据文件格式确定加密类型
    static VideoEncryptType GetVideoEncryptTypeByFormat(const std::string& format);
    static AudioEncryptType GetAudioEncryptTypeByFormat(const std::string& format);

    static VideoEncryptType GetVideoEncryptTypeByFormatW( const std::wstring& format );
    static AudioEncryptType GetAudioEncryptTypeByFormatW( const std::wstring& format );
    
    // 新增：格式支持检测
    static bool IsSupportedFormat(const std::string& extension);

    // 新增：关键帧检测方法（公开接口）
    static bool IsKeyFrameFFmpeg(const AVPacket* packet);
    static KeyFrameDetectResult DetectKeyFrame(const uint8_t* data, size_t size);

private:
    size_t m_videoEncryptOffset;  // 视频加密起始偏移
    size_t m_audioEncryptOffset;  // 音频加密起始偏移
    float m_encryptRatio;         // 加密比例 (0.0-1.0)

    // 辅助函数
    bool IsKeyFrame(const uint8_t* data, size_t size);
    size_t CalculateEncryptSize(size_t total_size);
    
    // 新增：增强的关键帧检测方法
    static bool DetectH264KeyFrame(const uint8_t* data, size_t size);
    static bool DetectH265KeyFrame(const uint8_t* data, size_t size);
    static bool DetectMPEG2KeyFrame(const uint8_t* data, size_t size);
    static bool DetectH263KeyFrame(const uint8_t* data, size_t size);
    static bool DetectVC1KeyFrame(const uint8_t* data, size_t size);
    static bool DetectContainerFormat(const uint8_t* data, size_t size, size_t& payload_start);
}; 