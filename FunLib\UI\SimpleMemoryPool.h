#pragma once
#include <memory>
#include <queue>
#include <mutex>

// 简单的内存DC池，用于优化StaticEx等控件的绘制性能
class CSimpleMemoryPool
{
public:
    struct DCItem {
        CDC dc;
        CBitmap bitmap;
        CSize size;
        DWORD lastUsed;
        
        DCItem() : size(0, 0), lastUsed(0) {}
    };
    
    // 获取单例
    static CSimpleMemoryPool& Instance() {
        static CSimpleMemoryPool instance;
        return instance;
    }
    
    // 获取一个DC
    std::shared_ptr<DCItem> AcquireDC(CDC* pDC, int width, int height) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 查找合适的缓存DC
        for (auto it = m_availableItems.begin(); it != m_availableItems.end(); ++it) {
            auto& item = *it;
            if (item->size.cx >= width && item->size.cy >= height &&
                item->size.cx <= width * 1.5 && item->size.cy <= height * 1.5) {
                // 找到合适的DC
                item->lastUsed = ::GetTickCount();
                auto result = item;
                m_availableItems.erase(it);
                return result;
            }
        }
        
        // 创建新的DC
        auto item = std::make_shared<DCItem>();
        if (item->dc.CreateCompatibleDC(pDC) &&
            item->bitmap.CreateCompatibleBitmap(pDC, width, height)) {
            item->dc.SelectObject(&item->bitmap);
            item->size = CSize(width, height);
            item->lastUsed = ::GetTickCount();
            return item;
        }
        
        return nullptr;
    }
    
    // 释放DC
    void ReleaseDC(std::shared_ptr<DCItem> item) {
        if (!item) return;
        
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 清理超过30秒未使用的DC
        DWORD now = ::GetTickCount();
        m_availableItems.erase(
            std::remove_if(m_availableItems.begin(), m_availableItems.end(),
                [now](const std::shared_ptr<DCItem>& dc) {
                    return (now - dc->lastUsed) > 30000;
                }),
            m_availableItems.end()
        );
        
        // 将DC放回池中
        if (m_availableItems.size() < 10) { // 最多缓存10个
            m_availableItems.push_back(item);
        }
    }
    
    // 清理所有缓存
    void Clear() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_availableItems.clear();
    }
    
private:
    CSimpleMemoryPool() = default;
    ~CSimpleMemoryPool() { Clear(); }
    
    std::vector<std::shared_ptr<DCItem>> m_availableItems;
    std::mutex m_mutex;
};
