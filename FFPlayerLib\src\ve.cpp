﻿#include "ve.h"
#include "..\..\ep-common\VideoAESCommon.h"
#include <windows.h>

std::unique_ptr<CVideoAESCommon> _videoAES = std::make_unique<CVideoAESCommon>( );

static char _psssword [ 256 ] = { 0 };
static int _isEncrypted = 0;
static VideoEncryptType _videoEncryptType;  // 动态视频解密类型
static AudioEncryptType _audioEncryptType;  // 动态音频解密类型
#define safe_strlen(str) ((((char*)(str))==NULL)?0:strlen(str))

int FF_SetPassword( const char* Password , int vt , int at )
    {

    if (0 != safe_strlen( Password )) {
        strcpy_s( _psssword , Password );
        // 初始化解密器
        if (!_videoAES->Initialize( _psssword )) {
            return -2;
            }

        _isEncrypted = 1;
        _videoEncryptType = ( VideoEncryptType ) vt;
        _audioEncryptType = ( AudioEncryptType ) at;
        }
    else {
        _isEncrypted = 0;
        }

    return 0;
    }

int DecryptVideoPacket( AVPacket* packet )
    {
    return (int)_videoAES->DecryptVideoData( packet->data , packet->size , _videoEncryptType );
    }

int DecryptAudioPacket( AVPacket* packet )
    {
    _videoAES->DecryptAudioData( packet->data , packet->size , _audioEncryptType );
    return 1;
    }

int IsEncrypted( void )
    {
    return _isEncrypted;
    }