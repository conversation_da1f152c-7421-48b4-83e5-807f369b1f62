﻿#pragma once
#ifndef PCH_H
#define PCH_H

// 添加要在此处预编译的标头
#include "framework.h"
// 系统头文件
#include <windows.h>
#include <tchar.h>

// 标准C++库
#include <string>
#include <vector>
#include <memory>
#include <algorithm>
#include <cstdint>    // for standard uint8_t

// Crypto++库
#define CRYPTOPP_ENABLE_NAMESPACE_WEAK 1
#include <aes.h>
#include <modes.h>
#include <filters.h>
#include <hex.h>
#include <sha.h>
#include <md5.h>
#include <pwdbased.h>
#include <osrng.h>
#include <io.h>
#include <fcntl.h>

// 使用标准库的uint8_t，避免重复定义 
#ifdef _DEBUG
#define _CRTDBG_MAP_ALLOC
#include <crtdbg.h>
#ifndef DEBUG_NEW
#define DEBUG_NEW new(_NORMAL_BLOCK, __FILE__, __LINE__)
#endif
#ifndef new
#define new DEBUG_NEW
#endif
#endif
#endif //PCH_H 