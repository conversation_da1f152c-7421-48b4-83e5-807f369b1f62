#pragma once
#include "definition.h"

int audio_open( void* opaque , AVChannelLayout* wanted_channel_layout , 
    int wanted_sample_rate , struct AudioParams* audio_hw_params );

void sdl_audio_callback( void* opaque , Uint8* stream , int len );
int audio_decode_frame( VideoState* is );

int configure_audio_filters( VideoState* is , const char* afilters , int force_output_format );

int synchronize_audio( VideoState* is , int nb_samples );

void update_sample_display( VideoState* is , short* samples , int samples_size );

int cmp_audio_fmts( enum AVSampleFormat fmt1 , int64_t channel_count1 , 
    enum AVSampleFormat fmt2 , int64_t channel_count2 );