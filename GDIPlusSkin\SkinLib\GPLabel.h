#if !defined(AFX_A16Label_H__A4EABEC5_2E8C_11D1_B79F_00805F9ECE10__INCLUDED_)
#define AFX_A16Label_H__A4EABEC5_2E8C_11D1_B79F_00805F9ECE10__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// A16Label.h : header file
//

#define	NM_LINKCLICK	WM_APP + 0x200

/////////////////////////////////////////////////////////////////////////////
// CGPLabel window

class CGPLabel : public CStatic
{
// Construction
public:
	enum FlashType {None, Text, Background };
	enum Type3D { Raised, Sunken};

	CGPLabel();
	virtual CGPLabel& SetBkColor(COLORREF crBkgnd);
	virtual CGPLabel& SetTextColor(COLORREF crText);
	virtual CGPLabel& SetText(const CString& strText);
	virtual CGPLabel& SetFontBold(BOOL bBold);
	virtual CGPLabel& SetFontName(const CString& strFont);
	virtual CGPLabel& SetFontUnderline(BOOL bSet);
	virtual CGPLabel& SetFontItalic(BOOL bSet);
	virtual CGPLabel& SetFontSize(int nSize);
	virtual CGPLabel& SetSunken(BOOL bSet);
	virtual CGPLabel& SetBorder(BOOL bSet);
	virtual CGPLabel& SetTransparent(BOOL bSet);
	virtual CGPLabel& FlashText(BOOL bActivate);
	virtual CGPLabel& FlashBackground(BOOL bActivate);
	virtual CGPLabel& SetLink(BOOL bLink,BOOL bNotifyParent);
	virtual CGPLabel& SetLinkCursor(HCURSOR hCursor);
	virtual CGPLabel& SetFont3D(BOOL bSet,Type3D type=Raised);
	virtual CGPLabel& SetRotationAngle(UINT nAngle,BOOL bRotation);


// Attributes
public:
protected:
	void ReconstructFont();
	COLORREF	m_crText;
	HBRUSH		m_hwndBrush;
	HBRUSH		m_hBackBrush;
	LOGFONT		m_lf;
	CFont		m_font;
	CString		m_strText;
	BOOL		m_bState;
	BOOL		m_bTimer;
	BOOL		m_bLink;
	BOOL		m_bTransparent;
	FlashType	m_Type;
	HCURSOR		m_hCursor;
	Type3D		m_3dType;
	BOOL		m_bFont3d;
	BOOL		m_bToolTips;
	BOOL		m_bNotifyParent;
	BOOL		m_bRotation;

	// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CGPLabel)
	public:
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CGPLabel();

	// Generated message map functions
protected:
	//{{AFX_MSG(CGPLabel)
	afx_msg void OnTimer(UINT nIDEvent);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg BOOL OnSetCursor(CWnd* pWnd, UINT nHitTest, UINT message);
	afx_msg void OnPaint();
	afx_msg void OnSysColorChange();
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_A16Label_H__A4EABEC5_2E8C_11D1_B79F_00805F9ECE10__INCLUDED_)
