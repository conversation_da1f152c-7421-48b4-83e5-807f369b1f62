#include "audio.h"
#include "fame_queue.h"

int cmp_audio_fmts( enum AVSampleFormat fmt1 , int64_t channel_count1 ,
    enum AVSampleFormat fmt2 , int64_t channel_count2 )
    {
    /* If channel count == 1, planar and non-planar formats are the same */
    if (channel_count1 == 1 && channel_count2 == 1)
        return av_get_packed_sample_fmt( fmt1 ) != av_get_packed_sample_fmt( fmt2 );
    else
        return channel_count1 != channel_count2 || fmt1 != fmt2;
    }


int audio_open( void* opaque , AVChannelLayout* wanted_channel_layout , int wanted_sample_rate , struct AudioParams* audio_hw_params )
    {
    SDL_AudioSpec wanted_spec , spec;
    const char* env;
    static const int next_nb_channels [ ] = { 0, 0, 1, 6, 2, 6, 4, 6 };
    static const int next_sample_rates [ ] = { 0, 44100, 48000, 96000, 192000 };
    int next_sample_rate_idx = FF_ARRAY_ELEMS( next_sample_rates ) - 1;
    int wanted_nb_channels = wanted_channel_layout->nb_channels;

    env = SDL_getenv( "SDL_AUDIO_CHANNELS" );
    if (env) {
        wanted_nb_channels = atoi( env );
        av_channel_layout_uninit( wanted_channel_layout );
        av_channel_layout_default( wanted_channel_layout , wanted_nb_channels );
        }
    if (wanted_channel_layout->order != AV_CHANNEL_ORDER_NATIVE) {
        av_channel_layout_uninit( wanted_channel_layout );
        av_channel_layout_default( wanted_channel_layout , wanted_nb_channels );
        }
    wanted_nb_channels = wanted_channel_layout->nb_channels;
    wanted_spec.channels = wanted_nb_channels;
    wanted_spec.freq = wanted_sample_rate;
    if (wanted_spec.freq <= 0 || wanted_spec.channels <= 0) {
        av_log( NULL , AV_LOG_ERROR , "Invalid sample rate or channel count!\n" );
        return -1;
        }
    while (next_sample_rate_idx && next_sample_rates [ next_sample_rate_idx ] >= wanted_spec.freq)
        next_sample_rate_idx--;
    wanted_spec.format = AUDIO_S16SYS;
    wanted_spec.silence = 0;
    wanted_spec.samples = FFMAX( SDL_AUDIO_MIN_BUFFER_SIZE , 2 << av_log2( wanted_spec.freq / SDL_AUDIO_MAX_CALLBACKS_PER_SEC ) );
    wanted_spec.callback = sdl_audio_callback;
    wanted_spec.userdata = opaque;
    while (!( audio_dev = SDL_OpenAudioDevice( NULL , 0 , &wanted_spec , &spec , SDL_AUDIO_ALLOW_FREQUENCY_CHANGE | SDL_AUDIO_ALLOW_CHANNELS_CHANGE ) )) {
        av_log( NULL , AV_LOG_WARNING , "SDL_OpenAudio (%d channels, %d Hz): %s\n" ,
            wanted_spec.channels , wanted_spec.freq , SDL_GetError( ) );
        wanted_spec.channels = next_nb_channels [ FFMIN( 7 , wanted_spec.channels ) ];
        if (!wanted_spec.channels) {
            wanted_spec.freq = next_sample_rates [ next_sample_rate_idx-- ];
            wanted_spec.channels = wanted_nb_channels;
            if (!wanted_spec.freq) {
                av_log( NULL , AV_LOG_ERROR ,
                    "No more combinations to try, audio open failed\n" );
                return -1;
                }
            }
        av_channel_layout_default( wanted_channel_layout , wanted_spec.channels );
        }
    if (spec.format != AUDIO_S16SYS) {
        av_log( NULL , AV_LOG_ERROR ,
            "SDL advised audio format %d is not supported!\n" , spec.format );
        return -1;
        }
    if (spec.channels != wanted_spec.channels) {
        av_channel_layout_uninit( wanted_channel_layout );
        av_channel_layout_default( wanted_channel_layout , spec.channels );
        if (wanted_channel_layout->order != AV_CHANNEL_ORDER_NATIVE) {
            av_log( NULL , AV_LOG_ERROR ,
                "SDL advised channel count %d is not supported!\n" , spec.channels );
            return -1;
            }
        }

    audio_hw_params->fmt = AV_SAMPLE_FMT_S16;
    audio_hw_params->freq = spec.freq;
    if (av_channel_layout_copy( &audio_hw_params->ch_layout , wanted_channel_layout ) < 0)
        return -1;
    audio_hw_params->frame_size = av_samples_get_buffer_size( NULL , audio_hw_params->ch_layout.nb_channels , 1 , audio_hw_params->fmt , 1 );
    audio_hw_params->bytes_per_sec = av_samples_get_buffer_size( NULL , audio_hw_params->ch_layout.nb_channels , audio_hw_params->freq , audio_hw_params->fmt , 1 );
    if (audio_hw_params->bytes_per_sec <= 0 || audio_hw_params->frame_size <= 0) {
        av_log( NULL , AV_LOG_ERROR , "av_samples_get_buffer_size failed\n" );
        return -1;
        }
    return spec.size;
    }

/**
 * Decode one audio frame and return its uncompressed size.
 *
 * The processed audio frame is decoded, converted if required, and
 * stored in is->audio_buf, with size in bytes given by the return
 * value.
 */
int audio_decode_frame( VideoState* is )
    {
    int data_size , resampled_data_size;
    av_unused double audio_clock0;
    int wanted_nb_samples;
    Frame* af;

    if (is->paused)
        return -1;

    do {
#if defined(_WIN32)
        while (frame_queue_nb_remaining( &is->sampq ) == 0) {
            if (( av_gettime_relative( ) - audio_callback_time ) > 1000000LL * is->audio_hw_buf_size / is->audio_tgt.bytes_per_sec / 2)
                return -1;
            av_usleep( 1000 );
            }
#endif
        if (!( af = frame_queue_peek_readable( &is->sampq ) ))
            return -1;
        frame_queue_next( &is->sampq );
        }
    while (af->serial != is->audioq.serial);

        data_size = av_samples_get_buffer_size( NULL , af->frame->ch_layout.nb_channels ,
            af->frame->nb_samples ,
            af->frame->format , 1 );

        wanted_nb_samples = synchronize_audio( is , af->frame->nb_samples );

        if (af->frame->format != is->audio_src.fmt ||
            av_channel_layout_compare( &af->frame->ch_layout , &is->audio_src.ch_layout ) ||
            af->frame->sample_rate != is->audio_src.freq ||
            ( wanted_nb_samples != af->frame->nb_samples && !is->swr_ctx )) {
            int ret;
            swr_free( &is->swr_ctx );
            ret = swr_alloc_set_opts2( &is->swr_ctx ,
                &is->audio_tgt.ch_layout , is->audio_tgt.fmt , is->audio_tgt.freq ,
                &af->frame->ch_layout , af->frame->format , af->frame->sample_rate ,
                0 , NULL );
            if (ret < 0 || swr_init( is->swr_ctx ) < 0) {
                av_log( NULL , AV_LOG_ERROR ,
                    "Cannot create sample rate converter for conversion of %d Hz %s %d channels to %d Hz %s %d channels!\n" ,
                    af->frame->sample_rate , av_get_sample_fmt_name( af->frame->format ) , af->frame->ch_layout.nb_channels ,
                    is->audio_tgt.freq , av_get_sample_fmt_name( is->audio_tgt.fmt ) , is->audio_tgt.ch_layout.nb_channels );
                swr_free( &is->swr_ctx );
                return -1;
                }
            if (av_channel_layout_copy( &is->audio_src.ch_layout , &af->frame->ch_layout ) < 0)
                return -1;
            is->audio_src.freq = af->frame->sample_rate;
            is->audio_src.fmt = af->frame->format;
            }

        if (is->swr_ctx) {
            const uint8_t** in = ( const uint8_t** ) af->frame->extended_data;
            uint8_t** out = &is->audio_buf1;
            int out_count = ( int64_t ) wanted_nb_samples * is->audio_tgt.freq / af->frame->sample_rate + 256;
            int out_size = av_samples_get_buffer_size( NULL , is->audio_tgt.ch_layout.nb_channels , out_count , is->audio_tgt.fmt , 0 );
            int len2;
            if (out_size < 0) {
                av_log( NULL , AV_LOG_ERROR , "av_samples_get_buffer_size() failed\n" );
                return -1;
                }
            if (wanted_nb_samples != af->frame->nb_samples) {
                if (swr_set_compensation( is->swr_ctx , ( wanted_nb_samples - af->frame->nb_samples ) * is->audio_tgt.freq / af->frame->sample_rate ,
                    wanted_nb_samples * is->audio_tgt.freq / af->frame->sample_rate ) < 0) {
                    av_log( NULL , AV_LOG_ERROR , "swr_set_compensation() failed\n" );
                    return -1;
                    }
                }
            av_fast_malloc( &is->audio_buf1 , &is->audio_buf1_size , out_size );
            if (!is->audio_buf1)
                return AVERROR( ENOMEM );
            len2 = swr_convert( is->swr_ctx , out , out_count , in , af->frame->nb_samples );
            if (len2 < 0) {
                av_log( NULL , AV_LOG_ERROR , "swr_convert() failed\n" );
                return -1;
                }
            if (len2 == out_count) {
                av_log( NULL , AV_LOG_WARNING , "audio buffer is probably too small\n" );
                if (swr_init( is->swr_ctx ) < 0)
                    swr_free( &is->swr_ctx );
                }
            is->audio_buf = is->audio_buf1;
            resampled_data_size = len2 * is->audio_tgt.ch_layout.nb_channels * av_get_bytes_per_sample( is->audio_tgt.fmt );
            }
        else {
            is->audio_buf = af->frame->data [ 0 ];
            resampled_data_size = data_size;
            }

        audio_clock0 = is->audio_clock;
        /* update the audio clock with the pts */
        if (!isnan( af->pts ))
            is->audio_clock = af->pts + ( double ) af->frame->nb_samples / af->frame->sample_rate;
        else
            is->audio_clock = NAN;
        is->audio_clock_serial = af->serial;
#ifdef DEBUG
        {
        static double last_clock;
        printf( "audio: delay=%0.3f clock=%0.3f clock0=%0.3f\n" ,
            is->audio_clock - last_clock ,
            is->audio_clock , audio_clock0 );
        last_clock = is->audio_clock;
        }
#endif
        return resampled_data_size;
    }

/* prepare a new audio buffer */
void sdl_audio_callback( void* opaque , Uint8* stream , int len )
    {
    VideoState* is = opaque;
    int audio_size , len1;

    audio_callback_time = av_gettime_relative( );

    while (len > 0) {
        if (is->audio_buf_index >= is->audio_buf_size) {
            audio_size = audio_decode_frame( is );
            if (audio_size < 0) {
                /* if error, just output silence */
                is->audio_buf = NULL;
                is->audio_buf_size = SDL_AUDIO_MIN_BUFFER_SIZE / is->audio_tgt.frame_size * is->audio_tgt.frame_size;
                }
            else {
                if (is->show_mode != SHOW_MODE_VIDEO)
                    update_sample_display( is , ( int16_t* ) is->audio_buf , audio_size );
                is->audio_buf_size = audio_size;
                }
            is->audio_buf_index = 0;
            }
        len1 = is->audio_buf_size - is->audio_buf_index;
        if (len1 > len)
            len1 = len;
        if (!is->muted && is->audio_buf && is->audio_volume == SDL_MIX_MAXVOLUME)
            memcpy( stream , ( uint8_t* ) is->audio_buf + is->audio_buf_index , len1 );
        else {
            memset( stream , 0 , len1 );
            if (!is->muted && is->audio_buf)
                SDL_MixAudioFormat( stream , ( uint8_t* ) is->audio_buf + is->audio_buf_index , AUDIO_S16SYS , len1 , is->audio_volume );
            }
        len -= len1;
        stream += len1;
        is->audio_buf_index += len1;
        }
    is->audio_write_buf_size = is->audio_buf_size - is->audio_buf_index;
    /* Let's assume the audio driver that is used by SDL has two periods. */
    if (!isnan( is->audio_clock )) {
        set_clock_at( &is->audclk , is->audio_clock - ( double ) ( 2 * is->audio_hw_buf_size + is->audio_write_buf_size ) / is->audio_tgt.bytes_per_sec , is->audio_clock_serial , audio_callback_time / 1000000.0 );
        sync_clock_to_slave( &is->extclk , &is->audclk );
        }
    }

int configure_audio_filters( VideoState* is , const char* afilters , int force_output_format )
    {
    static const enum AVSampleFormat sample_fmts [ ] = { AV_SAMPLE_FMT_S16, AV_SAMPLE_FMT_NONE };
    int sample_rates [ 2 ] = { 0, -1 };
    AVFilterContext* filt_asrc = NULL , * filt_asink = NULL;
    char aresample_swr_opts [ 512 ] = "";
    const AVDictionaryEntry* e = NULL;
    AVBPrint bp;
    char asrc_args [ 256 ];
    int ret;

    avfilter_graph_free( &is->agraph );
    if (!( is->agraph = avfilter_graph_alloc( ) ))
        return AVERROR( ENOMEM );
    is->agraph->nb_threads = filter_nbthreads;

    av_bprint_init( &bp , 0 , AV_BPRINT_SIZE_AUTOMATIC );

    while (( e = av_dict_iterate( swr_opts , e ) ))
        av_strlcatf( aresample_swr_opts , sizeof( aresample_swr_opts ) , "%s=%s:" , e->key , e->value );
    if (strlen( aresample_swr_opts ))
        aresample_swr_opts [ strlen( aresample_swr_opts ) - 1 ] = '\0';
    av_opt_set( is->agraph , "aresample_swr_opts" , aresample_swr_opts , 0 );

    av_channel_layout_describe_bprint( &is->audio_filter_src.ch_layout , &bp );

    ret = snprintf( asrc_args , sizeof( asrc_args ) ,
        "sample_rate=%d:sample_fmt=%s:time_base=%d/%d:channel_layout=%s" ,
        is->audio_filter_src.freq , av_get_sample_fmt_name( is->audio_filter_src.fmt ) ,
        1 , is->audio_filter_src.freq , bp.str );

    ret = avfilter_graph_create_filter( &filt_asrc ,
        avfilter_get_by_name( "abuffer" ) , "ffplay_abuffer" ,
        asrc_args , NULL , is->agraph );
    if (ret < 0)
        goto end;


    ret = avfilter_graph_create_filter( &filt_asink ,
        avfilter_get_by_name( "abuffersink" ) , "ffplay_abuffersink" ,
        NULL , NULL , is->agraph );
    if (ret < 0)
        goto end;

    if (( ret = av_opt_set_int_list( filt_asink , "sample_fmts" , sample_fmts , AV_SAMPLE_FMT_NONE , AV_OPT_SEARCH_CHILDREN ) ) < 0)
        goto end;
    if (( ret = av_opt_set_int( filt_asink , "all_channel_counts" , 1 , AV_OPT_SEARCH_CHILDREN ) ) < 0)
        goto end;

    if (force_output_format) {
        av_bprint_clear( &bp );
        av_channel_layout_describe_bprint( &is->audio_tgt.ch_layout , &bp );
        sample_rates [ 0 ] = is->audio_tgt.freq;
        if (( ret = av_opt_set_int( filt_asink , "all_channel_counts" , 0 , AV_OPT_SEARCH_CHILDREN ) ) < 0)
            goto end;
        if (( ret = av_opt_set( filt_asink , "ch_layouts" , bp.str , AV_OPT_SEARCH_CHILDREN ) ) < 0)
            goto end;
        if (( ret = av_opt_set_int_list( filt_asink , "sample_rates" , sample_rates , -1 , AV_OPT_SEARCH_CHILDREN ) ) < 0)
            goto end;
        }


    if (( ret = configure_filtergraph( is->agraph , afilters , filt_asrc , filt_asink ) ) < 0)
        goto end;

    is->in_audio_filter = filt_asrc;
    is->out_audio_filter = filt_asink;

end:
    if (ret < 0)
        avfilter_graph_free( &is->agraph );
    av_bprint_finalize( &bp , NULL );

    return ret;
    }



/* copy samples for viewing in editor window */
void update_sample_display( VideoState* is , short* samples , int samples_size )
    {
    int size , len;

    size = samples_size / sizeof( short );
    while (size > 0) {
        len = SAMPLE_ARRAY_SIZE - is->sample_array_index;
        if (len > size)
            len = size;
        memcpy( is->sample_array + is->sample_array_index , samples , len * sizeof( short ) );
        samples += len;
        is->sample_array_index += len;
        if (is->sample_array_index >= SAMPLE_ARRAY_SIZE)
            is->sample_array_index = 0;
        size -= len;
        }
    }

/* return the wanted number of samples to get better sync if sync_type is video
 * or external master clock */
int synchronize_audio( VideoState* is , int nb_samples )
    {
    int wanted_nb_samples = nb_samples;

    /* if not master, then we try to remove or add samples to correct the clock */
    if (get_master_sync_type( is ) != AV_SYNC_AUDIO_MASTER) {
        double diff , avg_diff;
        int min_nb_samples , max_nb_samples;

        diff = get_clock( &is->audclk ) - get_master_clock( is );

        if (!isnan( diff ) && fabs( diff ) < AV_NOSYNC_THRESHOLD) {
            is->audio_diff_cum = diff + is->audio_diff_avg_coef * is->audio_diff_cum;
            if (is->audio_diff_avg_count < AUDIO_DIFF_AVG_NB) {
                /* not enough measures to have a correct estimate */
                is->audio_diff_avg_count++;
                }
            else {
                /* estimate the A-V difference */
                avg_diff = is->audio_diff_cum * ( 1.0 - is->audio_diff_avg_coef );

                if (fabs( avg_diff ) >= is->audio_diff_threshold) {
                    wanted_nb_samples = nb_samples + ( int ) ( diff * is->audio_src.freq );
                    min_nb_samples = ( ( nb_samples * ( 100 - SAMPLE_CORRECTION_PERCENT_MAX ) / 100 ) );
                    max_nb_samples = ( ( nb_samples * ( 100 + SAMPLE_CORRECTION_PERCENT_MAX ) / 100 ) );
                    wanted_nb_samples = av_clip( wanted_nb_samples , min_nb_samples , max_nb_samples );
                    }
                av_log( NULL , AV_LOG_TRACE , "diff=%f adiff=%f sample_diff=%d apts=%0.3f %f\n" ,
                    diff , avg_diff , wanted_nb_samples - nb_samples ,
                    is->audio_clock , is->audio_diff_threshold );
                }
            }
        else {
            /* too big difference : may be initial PTS errors, so
               reset A-V filter */
            is->audio_diff_avg_count = 0;
            is->audio_diff_cum = 0;
            }
        }

    return wanted_nb_samples;
    }
