<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B1234567-89AB-4CDE-AB00-000000000000}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>FFPlayerLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <EnableASAN>false</EnableASAN>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)..\lib\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)..\lib\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>A:\ffmpeg\msvc\include;A:\crypto++\8.90\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>FFPLAYERLIB_EXPORTS;FFPLAYERLIB_STATIC;_CRT_SECURE_NO_WARNINGS;SDL_MAIN_HANDLED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <FloatingPointModel>Precise</FloatingPointModel>
    </ClCompile>
    <Lib>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <Lib>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalIncludeDirectories>A:\ffmpeg\msvc\include;A:\crypto++\8.90\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>FFPLAYERLIB_EXPORTS;FFPLAYERLIB_STATIC;_CRT_SECURE_NO_WARNINGS;SDL_MAIN_HANDLED;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <DebugInformationFormat>None</DebugInformationFormat>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FloatingPointModel>Precise</FloatingPointModel>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <ControlFlowGuard>Guard</ControlFlowGuard>
      <OpenMPSupport>false</OpenMPSupport>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="src\audio.h" />
    <ClInclude Include="src\clock_sync.h" />
    <ClInclude Include="src\cmdutils.h" />
    <ClInclude Include="src\config.h" />
    <ClInclude Include="src\control​.h" />
    <ClInclude Include="src\decoder.h" />
    <ClInclude Include="src\definition.h" />
    <ClInclude Include="src\fame_queue.h" />
    <ClInclude Include="src\ffplay.h" />
    <ClInclude Include="src\ffplay_renderer.h" />
    <ClInclude Include="src\filter.h" />
    <ClInclude Include="src\fopen_utf8.h" />
    <ClInclude Include="src\pkt_queue.h" />
    <ClInclude Include="src\render.h" />
    <ClInclude Include="src\stream.h" />
    <ClInclude Include="src\subtitle.h" />
    <ClInclude Include="src\thread.h" />
    <ClInclude Include="src\ve.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\audio.c" />
    <ClCompile Include="src\clock_sync.c" />
    <ClCompile Include="src\cmdutils.c" />
    <ClCompile Include="src\control.c" />
    <ClCompile Include="src\decoder.c" />
    <ClCompile Include="src\fame_queue.c" />
    <ClCompile Include="src\ffplay.c" />
    <ClCompile Include="src\ffplay_renderer.c" />
    <ClCompile Include="src\filter.c" />
    <ClCompile Include="src\pkt_queue.c" />
    <ClCompile Include="src\render.c" />
    <ClCompile Include="src\stream.c" />
    <ClCompile Include="src\subtitle.c" />
    <ClCompile Include="src\thread.c" />
    <ClCompile Include="src\ve.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>