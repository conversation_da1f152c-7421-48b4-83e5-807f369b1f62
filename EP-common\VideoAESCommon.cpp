﻿#include "pch.h"
#include "VideoAESCommon.h"
#include <algorithm>
#include <cctype>
#include <cwctype>

// 添加FFmpeg头文件
extern "C" {
#include "libavformat/avformat.h"
#include "libavcodec/avcodec.h"
}

CVideoAESCommon::CVideoAESCommon()
    : m_videoEncryptOffset(20)
    , m_audioEncryptOffset(10)
    , m_encryptRatio(0.3f)  // 默认加密30%的数据
{
}

CVideoAESCommon::~CVideoAESCommon()
{
}

bool CVideoAESCommon::EncryptVideoData(uint8_t* data, size_t size, VideoEncryptType video_type)
{
    if (!data || size == 0)
        return false;

    switch (video_type)
    {
    case VIDEO_ENCRYPT_FULL:
        return EncryptPacket(data, size);

    case VIDEO_ENCRYPT_PARTIAL:
        return EncryptPacketPartial(data, size, m_videoEncryptOffset);

    case VIDEO_ENCRYPT_KEYFRAME:
        // 检查是否为关键帧
        if (IsKeyFrame(data, size))
        {
            return EncryptPacketPartial(data, size, m_videoEncryptOffset);
        }
        return true; // 非关键帧不加密，返回成功

    default:
        return false;
    }
}

bool CVideoAESCommon::DecryptVideoData(uint8_t* data, size_t size, VideoEncryptType video_type)
{
    if (!data || size == 0)
        return false;

    switch (video_type)
    {
    case VIDEO_ENCRYPT_FULL:
        return DecryptPacket(data, size);

    case VIDEO_ENCRYPT_PARTIAL:
        return DecryptPacketPartial(data, size, m_videoEncryptOffset);

    case VIDEO_ENCRYPT_KEYFRAME:
        // 检查是否为关键帧
        if (IsKeyFrame(data, size))
        {
            return DecryptPacketPartial(data, size, m_videoEncryptOffset);
        }
        return true; // 非关键帧不解密，返回成功

    default:
        return false;
    }
}

void CVideoAESCommon::EncryptAudioData(uint8_t* data, size_t size, AudioEncryptType audio_type)
{
    if (!data || size == 0)
        return;

    switch (audio_type)
    {
    case AUDIO_ENCRYPT_NONE:
        // 不进行加密
        break;

    case AUDIO_ENCRYPT_SIMPLE:
        EncryptAudioPacket(data, size);
        break;

    case AUDIO_ENCRYPT_PARTIAL:
        EncryptAudioPacketPartial(data, size, m_audioEncryptOffset);
        break;
    }
}

void CVideoAESCommon::DecryptAudioData(uint8_t* data, size_t size, AudioEncryptType audio_type)
{
    if (!data || size == 0)
        return;

    switch (audio_type)
    {
    case AUDIO_ENCRYPT_NONE:
        // 不进行解密
        break;

    case AUDIO_ENCRYPT_SIMPLE:
        DecryptAudioPacket(data, size);
        break;

    case AUDIO_ENCRYPT_PARTIAL:
        DecryptAudioPacketPartial(data, size, m_audioEncryptOffset);
        break;
    }
}

VideoEncryptType CVideoAESCommon::GetVideoEncryptTypeByFormat(const std::string& format)
{
    std::string lowerFormat = format;
    std::transform(lowerFormat.begin(), lowerFormat.end(), lowerFormat.begin(),
        [](unsigned char c) { return static_cast<char>(std::tolower(c)); });

    // 更新为动态加密策略 - 与VE库保持一致
    if (lowerFormat == ".mpeg" || lowerFormat == ".mpg") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
    }
    else if (lowerFormat == ".flv") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
    }
    else if (lowerFormat == ".avi") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
    }
    else if (lowerFormat == ".mp4") {
        return VIDEO_ENCRYPT_KEYFRAME; // nVideoEncry = 1 (关键帧加密更适合MP4)
    }
    else if (lowerFormat == ".mov") {
        return VIDEO_ENCRYPT_KEYFRAME; // MOV格式使用关键帧加密
    }
    else if (lowerFormat == ".mkv") {
        return VIDEO_ENCRYPT_KEYFRAME; // MKV格式使用关键帧加密
    }
    else if (lowerFormat == ".wmv") {
        return VIDEO_ENCRYPT_PARTIAL;  // WMV使用部分加密
    }
    else if (lowerFormat == ".rmvb") {
        return VIDEO_ENCRYPT_PARTIAL;  // RMVB使用部分加密
    }
    else if (lowerFormat == ".dat") {
        return VIDEO_ENCRYPT_PARTIAL;  // DAT使用部分加密
    }
    else if (lowerFormat == ".3gp" || lowerFormat == ".3gpp") {
        return VIDEO_ENCRYPT_FULL;     // 3GP使用完整加密
    }
    else {
        // 默认使用部分加密
        return VIDEO_ENCRYPT_PARTIAL;
    }
}

VideoEncryptType CVideoAESCommon::GetVideoEncryptTypeByFormatW( const std::wstring& format )
    {
    std::wstring lowerFormat = format;
    std::transform( lowerFormat.begin( ) , lowerFormat.end( ) , lowerFormat.begin( ) ,
        [ ]( wchar_t c ) { return std::towlower( c ); } );

    // 更新为动态加密策略 - 与VE库保持一致
    if (lowerFormat == L".mpeg" || lowerFormat == L".mpg") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
        }
    else if (lowerFormat == L".flv") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
        }
    else if (lowerFormat == L".avi") {
        return VIDEO_ENCRYPT_PARTIAL;  // nVideoEncry = 1
        }
    else if (lowerFormat == L".mp4") {
        return VIDEO_ENCRYPT_KEYFRAME; // nVideoEncry = 1 (关键帧加密更适合MP4)
        }
    else if (lowerFormat == L".mov") {
        return VIDEO_ENCRYPT_KEYFRAME; // MOV格式使用关键帧加密
        }
    else if (lowerFormat == L".mkv") {
        return VIDEO_ENCRYPT_KEYFRAME; // MKV格式使用关键帧加密
        }
    else if (lowerFormat == L".wmv") {
        return VIDEO_ENCRYPT_PARTIAL;  // WMV使用部分加密
        }
    else if (lowerFormat == L".rmvb") {
        return VIDEO_ENCRYPT_PARTIAL;  // RMVB使用部分加密
        }
    else if (lowerFormat == L".dat") {
        return VIDEO_ENCRYPT_PARTIAL;  // DAT使用部分加密
        }
    else if (lowerFormat == L".3gp" || lowerFormat == L".3gpp") {
        return VIDEO_ENCRYPT_FULL;     // 3GP使用完整加密
        }
    else {
        // 默认使用部分加密
        return VIDEO_ENCRYPT_PARTIAL;
        }
    }

AudioEncryptType CVideoAESCommon::GetAudioEncryptTypeByFormat(const std::string& format)
{
    std::string lowerFormat = format;
    std::transform(lowerFormat.begin(), lowerFormat.end(), lowerFormat.begin(),
        [](unsigned char c) { return static_cast<char>(std::tolower(c)); });

    if (lowerFormat == ".mpeg" || lowerFormat == ".mpg")
    {
        return AUDIO_ENCRYPT_SIMPLE;
    }
    else if (lowerFormat == ".flv")
    {
        return AUDIO_ENCRYPT_SIMPLE;
    }
    else if (lowerFormat == ".avi")
    {
        return AUDIO_ENCRYPT_SIMPLE;
    }
    else if (lowerFormat == ".mp4")
    {
        return AUDIO_ENCRYPT_PARTIAL;
    }
    else if (lowerFormat == ".dat")
    {
        return AUDIO_ENCRYPT_PARTIAL;
    }
    else if (lowerFormat == ".mov")
    {
        return AUDIO_ENCRYPT_PARTIAL; // MOV格式类似MP4，使用部分加密
    }
    else if (lowerFormat == ".mkv")
    {
        return AUDIO_ENCRYPT_PARTIAL; // MKV格式支持多种音频编码，使用部分加密
    }
    else if (lowerFormat == ".wmv")
    {
        return AUDIO_ENCRYPT_SIMPLE; // WMV格式使用简单加密
    }
    else if (lowerFormat == ".rmvb")
    {
        return AUDIO_ENCRYPT_SIMPLE; // RMVB格式使用简单加密
    }
    else if (lowerFormat == ".3gp" || lowerFormat == ".3gpp")
    {
        return AUDIO_ENCRYPT_SIMPLE; // 3GP格式音频使用简单加密
    }
    else
    {
        // 默认使用简单加密
        return AUDIO_ENCRYPT_SIMPLE;
    }
}


AudioEncryptType CVideoAESCommon::GetAudioEncryptTypeByFormatW( const std::wstring& format )
    {
    std::wstring lowerFormat = format;
    std::transform( lowerFormat.begin( ) , lowerFormat.end( ) , lowerFormat.begin( ) ,
        [ ]( wchar_t c ) { return std::towlower( c ); } );

    if (lowerFormat == L".mpeg" || lowerFormat == L".mpg") {
        return AUDIO_ENCRYPT_SIMPLE;
        }
    else if (lowerFormat == L".flv") {
        return AUDIO_ENCRYPT_SIMPLE;
        }
    else if (lowerFormat == L".avi") {
        return AUDIO_ENCRYPT_SIMPLE;
        }
    else if (lowerFormat == L".mp4") {
        return AUDIO_ENCRYPT_PARTIAL;
        }
    else if (lowerFormat == L".dat") {
        return AUDIO_ENCRYPT_PARTIAL;
        }
    else if (lowerFormat == L".mov") {
        return AUDIO_ENCRYPT_PARTIAL; // MOV格式类似MP4，使用部分加密
        }
    else if (lowerFormat == L".mkv") {
        return AUDIO_ENCRYPT_PARTIAL; // MKV格式支持多种音频编码，使用部分加密
        }
    else if (lowerFormat == L".wmv") {
        return AUDIO_ENCRYPT_SIMPLE; // WMV格式使用简单加密
        }
    else if (lowerFormat == L".rmvb") {
        return AUDIO_ENCRYPT_SIMPLE; // RMVB格式使用简单加密
        }
    else if (lowerFormat == L".3gp" || lowerFormat == L".3gpp") {
        return AUDIO_ENCRYPT_SIMPLE; // 3GP格式音频使用简单加密
        }
    else {
        // 默认使用简单加密
        return AUDIO_ENCRYPT_SIMPLE;
        }
    }

bool CVideoAESCommon::IsKeyFrame(const uint8_t* data, size_t size)
{
    KeyFrameDetectResult result = DetectKeyFrame(data, size);
    
    // 保守策略：无法确定时默认为关键帧，确保不遗漏加密
    return (result == KEYFRAME_YES || result == KEYFRAME_UNKNOWN);
}

size_t CVideoAESCommon::CalculateEncryptSize(size_t total_size)
{
    size_t encrypt_size = static_cast<size_t>(total_size * m_encryptRatio);
    
    // 确保最小加密尺寸（CTR模式支持任意长度）
    if (encrypt_size < 1)
        encrypt_size = (std::min)(total_size, static_cast<size_t>(1));
    
    // 确保最大加密尺寸
    if (encrypt_size > 1024)
        encrypt_size = 1024;
    
    // CTR模式无需16字节对齐，直接返回计算的大小
    return encrypt_size;
}

// 新增：文件格式支持检测
bool CVideoAESCommon::IsSupportedFormat(const std::string& extension)
{
    std::string ext_lower = extension;
    std::transform(ext_lower.begin(), ext_lower.end(), ext_lower.begin(),
        [](unsigned char c) { return static_cast<char>(std::tolower(c)); });
    
    // 支持的格式列表 - 与VE库保持一致
    const std::string supported_formats[] = {
        ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".mpeg", ".mpg", 
        ".rmvb", ".dat", ".3gp", ".3gpp", ".m4v", ".asf", ".vob"
    };
    
    for (const auto& format : supported_formats) {
        if (ext_lower == format) {
            return true;
        }
    }
    
    return false;
}

// ======================== 新增：改进的关键帧检测实现 ========================

// 基于FFmpeg AVPacket的关键帧检测（最准确的方法）
bool CVideoAESCommon::IsKeyFrameFFmpeg(const AVPacket* packet)
{
    if (!packet)
        return false;
    
    return (packet->flags & AV_PKT_FLAG_KEY) != 0;
}

// 基于FFmpeg AVPacket的视频加密
bool CVideoAESCommon::EncryptVideoPacket(AVPacket* packet, VideoEncryptType video_type)
{
    if (!packet || !packet->data || packet->size <= 0)
        return false;

    switch (video_type)
    {
    case VIDEO_ENCRYPT_FULL:
        return EncryptPacket(packet->data, packet->size);

    case VIDEO_ENCRYPT_PARTIAL:
        return EncryptPacketPartial(packet->data, packet->size, m_videoEncryptOffset);

    case VIDEO_ENCRYPT_KEYFRAME:
        // 使用FFmpeg的准确关键帧检测
        if (IsKeyFrameFFmpeg(packet))
        {
            return EncryptPacketPartial(packet->data, packet->size, m_videoEncryptOffset);
        }
        return true; // 非关键帧不加密，返回成功

    default:
        return false;
    }
}

// 基于FFmpeg AVPacket的视频解密
bool CVideoAESCommon::DecryptVideoPacket(AVPacket* packet, VideoEncryptType video_type)
{
    if (!packet || !packet->data || packet->size <= 0)
        return false;

    switch (video_type)
    {
    case VIDEO_ENCRYPT_FULL:
        return DecryptPacket(packet->data, packet->size);

    case VIDEO_ENCRYPT_PARTIAL:
        return DecryptPacketPartial(packet->data, packet->size, m_videoEncryptOffset);

    case VIDEO_ENCRYPT_KEYFRAME:
        // 使用FFmpeg的准确关键帧检测
        if (IsKeyFrameFFmpeg(packet))
        {
            return DecryptPacketPartial(packet->data, packet->size, m_videoEncryptOffset);
        }
        return true; // 非关键帧不解密，返回成功

    default:
        return false;
    }
}

// 统一的关键帧检测入口
KeyFrameDetectResult CVideoAESCommon::DetectKeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 4)
        return KEYFRAME_UNKNOWN;

    // 检测容器格式并获取实际负载起始位置
    size_t payload_start = 0;
    if (DetectContainerFormat(data, size, payload_start))
    {
        // 调整数据指针和大小
        if (payload_start < size)
        {
            data += payload_start;
            size -= payload_start;
        }
    }

    // 按优先级尝试不同的编码格式检测
    if (DetectH264KeyFrame(data, size))
        return KEYFRAME_YES;
    
    if (DetectH265KeyFrame(data, size))
        return KEYFRAME_YES;
    
    if (DetectMPEG2KeyFrame(data, size))
        return KEYFRAME_YES;
    
    if (DetectH263KeyFrame(data, size))
        return KEYFRAME_YES;
    
    if (DetectVC1KeyFrame(data, size))
        return KEYFRAME_YES;

    // 所有检测都失败，返回未知
    return KEYFRAME_UNKNOWN;
}

// H.264关键帧检测（改进版）
bool CVideoAESCommon::DetectH264KeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 5)
        return false;

    // 查找NAL单元起始码 0x00000001 或 0x000001
    for (size_t i = 0; i < size - 4; i++)
    {
        bool found_start_code = false;
        size_t nal_start = 0;
        
        // 检查4字节起始码
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x00 && data[i+3] == 0x01)
        {
            found_start_code = true;
            nal_start = i + 4;
        }
        // 检查3字节起始码
        else if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x01)
        {
            found_start_code = true;
            nal_start = i + 3;
        }

        if (found_start_code && nal_start < size)
        {
            uint8_t nal_header = data[nal_start];
            uint8_t nal_type = nal_header & 0x1F;
            
            // NAL类型5：IDR帧（即时解码刷新帧）
            if (nal_type == 5)
                return true;
            
            // NAL类型1：非IDR的切片，需要进一步检查slice_type
            if (nal_type == 1 && nal_start + 1 < size)
            {
                // 简化的slice_type检测
                // 实际应该解析CAVLC/CABAC，这里使用简化方法
                uint8_t slice_info = data[nal_start + 1];
                
                // 如果第二个字节的前3位表示I slice (slice_type = 2, 7)
                uint8_t slice_type = (slice_info >> 5) & 0x07;
                if (slice_type == 2 || slice_type == 7)
                    return true;
            }
        }
    }

    return false;
}

// H.265关键帧检测（改进版）
bool CVideoAESCommon::DetectH265KeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 5)
        return false;

    // 查找NAL单元起始码
    for (size_t i = 0; i < size - 4; i++)
    {
        bool found_start_code = false;
        size_t nal_start = 0;
        
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x00 && data[i+3] == 0x01)
        {
            found_start_code = true;
            nal_start = i + 4;
        }
        else if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x01)
        {
            found_start_code = true;
            nal_start = i + 3;
        }

        if (found_start_code && nal_start + 1 < size)
        {
            uint16_t nal_header = (data[nal_start] << 8) | data[nal_start + 1];
            uint8_t nal_unit_type = (nal_header >> 9) & 0x3F;
            
            // H.265关键帧NAL类型：
            // 16-18: CRA (Clean Random Access)
            // 19-20: IDR (Instantaneous Decoding Refresh)  
            // 21: RSV_IRAP (Reserved IRAP)
            if ((nal_unit_type >= 16 && nal_unit_type <= 21))
                return true;
        }
    }

    return false;
}

// MPEG-2关键帧检测（改进版）
bool CVideoAESCommon::DetectMPEG2KeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 6)
        return false;

    // 查找图像起始码 0x00 0x00 0x01 0x00
    for (size_t i = 0; i < size - 5; i++)
    {
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x01 && data[i+3] == 0x00)
        {
            // 检查图像类型（在第6个字节的高3位）
            if (i + 5 < size)
            {
                uint8_t picture_coding_type = (data[i+5] >> 3) & 0x07;
                // picture_coding_type = 1 表示I帧
                if (picture_coding_type == 1)
                    return true;
            }
        }
    }

    return false;
}

// H.263关键帧检测（修复版）
bool CVideoAESCommon::DetectH263KeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 6)
        return false;

    // 查找Picture Start Code (PSC): 0x000080 到 0x000083
    for (size_t i = 0; i < size - 5; i++)
    {
        if (data[i] == 0x00 && data[i+1] == 0x00 && (data[i+2] & 0xFC) == 0x80)
        {
            // 解析PTYPE字段来确定图像类型
            if (i + 4 < size)
            {
                // H.263的PTYPE在第3、4字节
                uint16_t ptype = (data[i+2] << 8) | data[i+3];
                
                // 提取PTYPE的bit 6 (Picture Type bit)
                // bit 6 = 0 表示INTRA (I) 帧
                // bit 6 = 1 表示INTER (P) 帧
                if ((ptype & 0x0200) == 0) // bit 6 = 0
                    return true;
            }
        }
    }

    return false;
}

// VC-1关键帧检测（改进版）
bool CVideoAESCommon::DetectVC1KeyFrame(const uint8_t* data, size_t size)
{
    if (!data || size < 8)
        return false;

    // 查找VC-1帧起始码
    for (size_t i = 0; i < size - 7; i++)
    {
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x01)
        {
            // VC-1的不同起始码
            uint8_t start_code = data[i+3];
            
            // 0x0D: Frame start code
            // 0x0E: Field start code  
            // 0x0F: Slice start code
            if (start_code == 0x0D || start_code == 0x0E || start_code == 0x0F)
            {
                if (i + 4 < size)
                {
                    uint8_t frame_type = data[i+4];
                    
                    // VC-1帧类型：
                    // 00: I帧 (Intra)
                    // 01: P帧 (Predictive)  
                    // 10: B帧 (Bi-directional)
                    // 11: BI帧 (Bi-directional Intra)
                    uint8_t ptype = (frame_type >> 6) & 0x03;
                    if (ptype == 0x00 || ptype == 0x03) // I帧或BI帧
                        return true;
                }
            }
        }
    }

    return false;
}

// 容器格式检测（新增）
bool CVideoAESCommon::DetectContainerFormat(const uint8_t* data, size_t size, size_t& payload_start)
{
    if (!data || size < 8)
    {
        payload_start = 0;
        return false;
    }

    payload_start = 0;

    // MP4/MOV AVCC格式检测
    // AVCC格式使用长度前缀而不是起始码
    if (size >= 4)
    {
        // 检查是否为AVCC格式的长度前缀
        uint32_t length = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
        
        // 如果长度合理且后面跟着NAL数据
        if (length > 0 && length < size - 4 && length < 1024*1024) // 最大1MB的合理性检查
        {
            payload_start = 4;
            return true;
        }
    }

    // AVI格式检测
    if (size >= 8)
    {
        // 查找 "00dc" 或 "00db" chunk标识
        if ((data[0] == '0' && data[1] == '0' && data[2] == 'd' && data[3] == 'c') ||
            (data[0] == '0' && data[1] == '0' && data[2] == 'd' && data[3] == 'b'))
        {
            // AVI chunk头部是8字节
            payload_start = 8;
            return true;
        }
    }

    // 默认情况，认为是原始码流
    return false;
}