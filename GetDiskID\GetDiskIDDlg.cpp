﻿
// GetDiskIDDlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "GetDiskID.h"
#include "GetDiskIDDlg.h"
#include "afxdialogex.h"
#include <disk/disk32.h>
#include <string/strTools.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CGetDiskIDDlg 对话框



CGetDiskIDDlg::CGetDiskIDDlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_GETDISKID_DIALOG, pParent)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CGetDiskIDDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange( pDX );
	DDX_Control( pDX , IDC_VALUE_SERIAL , m_serial );
	}

BEGIN_MESSAGE_MAP(CGetDiskIDDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_BN_CLICKED(IDC_BTN_COPY, &CGetDiskIDDlg::OnBnClickedCopy)
END_MESSAGE_MAP()


// CGetDiskIDDlg 消息处理程序

BOOL CGetDiskIDDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码

	// 1. 获取当前可执行文件所在盘符
	wchar_t modulePath[MAX_PATH]{};
	GetModuleFileNameW(nullptr, modulePath, _countof(modulePath));
	wchar_t driveLetter = towupper(modulePath[0]);

	CString driveStr;
	driveStr.Format(L"%c:", driveLetter);
	SetDlgItemTextW(IDC_VALUE_DRIVE, driveStr);

	// 2. 序列号 / 型号
	CDisk32 disk;

	// 尝试通过 USB 接口读取
	disk.GetUsbSerial( driveLetter );

	std::wstring serial = disk.GetSerialNumber();
	std::wstring model  = disk.GetModelNumber();

	ULONGLONG uID( 0 );
    if (serial.empty( )) {
        // 若失败则退回到普通卷序列号方式
        uID = disk.getDiskID( driveLetter );
        }
	else {
		uID = disk.SerialNumberToID( );
		}

    std::wstring sid;


	if (uID) {
		sid = StrTools::UInt64ToHexStringW( uID );
		}
	else {
		serial = L"序列号为空";
		}
       

	SetDlgItemTextW(IDC_VALUE_SERIAL, sid.c_str());
	SetDlgItemTextW(IDC_VALUE_MODEL,  model.c_str());

	// 3. 文件系统 / 容量
	wchar_t rootPath[4] = { driveLetter, L':', L'\\', L'\0' };
	wchar_t fsName[MAX_PATH]{};
	DWORD volSN = 0, maxComp = 0, fsFlags = 0;
	GetVolumeInformationW(rootPath, nullptr, 0, &volSN, &maxComp, &fsFlags, fsName, _countof(fsName));

	ULARGE_INTEGER total{};
	GetDiskFreeSpaceExW(rootPath, nullptr, &total, nullptr);
	double sizeGB = total.QuadPart / (1024.0 * 1024 * 1024);
	CString fsInfo;
	fsInfo.Format(L"%s / %.2f GB", fsName, sizeGB);
	SetDlgItemTextW(IDC_VALUE_FS, fsInfo);

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CGetDiskIDDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CGetDiskIDDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CGetDiskIDDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

// 复制序列号按钮
void CGetDiskIDDlg::OnBnClickedCopy()
{
	CString serial;
	GetDlgItemText(IDC_VALUE_SERIAL, serial);
	if (serial.IsEmpty( ) || serial.CompareNoCase( L"序列号为空" ) == 0) {
		SetDlgItemTextW( IDC_VALUE_MSG , L"序列号为空。" );
		return;
		}

	if (OpenClipboard())
	{
		EmptyClipboard();
		size_t size = (serial.GetLength() + 1) * sizeof(wchar_t);
		HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, size);
		if (hMem)
		{
			memcpy(GlobalLock(hMem), (LPCTSTR)serial, size);
			GlobalUnlock(hMem);
			if (NULL != SetClipboardData( CF_UNICODETEXT , hMem )) {
				SetDlgItemTextW( IDC_VALUE_MSG , L"序列号复制成功。" );
				}
			else {
				SetDlgItemTextW( IDC_VALUE_MSG , L"序列号复制失败。" );
				}
		}
		CloseClipboard();
	}
}

