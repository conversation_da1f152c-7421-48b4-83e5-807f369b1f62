﻿#pragma once
#include "gpbutton.h"
#include <memory>
#include "MemoryDCPool.h"

enum class MsgBoxType
	{
		MBT_INFOMATION,
		MBT_ERROR,
		MBT_WARNING,
		MBT_SUCCESS,
		MBT_CONFRIM
	};
// CGPMsgBox

class CGPMsgBox : public CWnd
{
	DECLARE_DYNAMIC(CGPMsgBox)

public:
	CGPMsgBox();
	virtual ~CGPMsgBox();

protected:
	DECLARE_MESSAGE_MAP()
public:
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
    afx_msg BOOL OnEraseBkgnd( CDC* pDC );
    afx_msg void OnDestroy( );
    afx_msg void OnBtnClose( );
    afx_msg void OnBtnYes( );
    afx_msg void OnBtnNo( );
    afx_msg void OnLButtonDown( UINT nFlags , CPoint point );
    afx_msg void OnKeyDown( UINT nChar , UINT nRepCnt , UINT nFlags );
	int Run(HWND hWndParent);
	void SetDT(MsgBoxType dt)
		{
        m_dtMsgBoxType = dt;
		}

	void SetCaption(const CString &sc)
		{
		m_sWindowCaption = sc;
		}

	void setMsgText(const CString &st)
		{
		m_sExplanation = st;
		}

    void setTitleText( const CString &st )
        {
        m_sTitle = st;
        }

	void SetButType(UINT uType)
		{
		m_uButType = uType;
		}
protected:
	void		AutoResizeControls();
	void		MakeBkgndDC();
    virtual void PostNcDestroy( );
private:
	UINT        m_uButType;
	
	// 优化：使用内存DC池
	std::shared_ptr<CMemoryDCPool::DCItem> m_pDCItem;
	
	CDC			m_dcBack;    // 保留用于兼容性，实际使用m_pDCItem->dc
	int         m_iTopHeight;
	int         m_iBottomHeight;
	MsgBoxType  m_dtMsgBoxType;
	CString		m_sYes;
	CString		m_sNo;
	CString		m_sWindowCaption;
	CString		m_sExplanation;
    CString     m_sTitle;
	CGPButton	m_ctBtnClose;
	CGPButton	m_ctBtnOK;
	CGPButton	m_ctBtnCancel;
	BOOL        m_bExit;
	int         m_iRes;
	CWnd*       m_pParent;
    Image*      m_pBackground;
    Image*      m_pFramemod;
    CBitmap*    m_pBitmap;    // 保留用于兼容性，实际使用m_pDCItem->bitmap
	};


