#pragma once
#include <afxwin.h>

// 安全的消息框辅助类，解决 Release 版本崩溃问题
class CMsgBoxHelper
{
public:
    // 显示普通消息框
    static void ShowMessage(UINT nID, HWND hWnd, LPCTSTR lpCaption)
    {
        CString strText;
        CString strCaption;
        
        // 安全加载字符串资源
        if (!strText.LoadString(nID))
        {
            strText.Format(_T("无法加载字符串资源 ID:%d"), nID);
        }
        
        // 安全处理标题
        if (lpCaption != nullptr)
        {
            // 创建副本避免指针问题
            strCaption = CString(lpCaption);
        }
        else
        {
            strCaption = _T("消息");
        }
        
        // 直接使用 Windows API 消息框
        ::MessageBox(hWnd, strText.GetString(), strCaption.GetString(), MB_OK | MB_ICONINFORMATION);
    }
    
    // 显示错误消息框
    static void ShowError(UINT nID, DWORD dwError, HWND hWnd, LPCTSTR lpCaption)
    {
        CString strTemplate;
        CString strCaption;
        CString strError;
        
        // 安全加载字符串资源
        if (!strTemplate.LoadString(nID))
        {
            strTemplate.Format(_T("错误 (资源ID:%d, 错误码:%%d)"), nID);
        }
        
        // 安全处理标题
        if (lpCaption != nullptr)
        {
            strCaption = CString(lpCaption);
        }
        else
        {
            strCaption = _T("错误");
        }
        
        // 格式化错误信息
        try
        {
            strError.Format(strTemplate, dwError);
        }
        catch(...)
        {
            strError.Format(_T("错误代码: %d"), dwError);
        }
        
        // 直接使用 Windows API 消息框
        ::MessageBox(hWnd, strError.GetString(), strCaption.GetString(), MB_OK | MB_ICONERROR);
    }
};
