<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{a0eacfe2-872e-435c-86ea-1dd10bec2187}</ProjectGuid>
    <RootNamespace>GDIPlusSkin</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <EnableASAN>false</EnableASAN>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\lib\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\lib\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <DebugInformationFormat>None</DebugInformationFormat>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FloatingPointModel>Precise</FloatingPointModel>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="framework.h" />
    <ClInclude Include="GDIPlusSkinH.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="SkinLib\command\Functions.h" />
    <ClInclude Include="SkinLib\command\MemoryDC.h" />
    <ClInclude Include="SkinLib\command\OSVersion.h" />
    <ClInclude Include="SkinLib\GPAdvToolList.h" />
    <ClInclude Include="SkinLib\GPAdvToolsTab.h" />
    <ClInclude Include="SkinLib\GPButton.h" />
    <ClInclude Include="SkinLib\GPCheckBox.h" />
    <ClInclude Include="SkinLib\GPChileDlgSkinBase.h" />
    <ClInclude Include="SkinLib\GPDlgSkinBase.h" />
    <ClInclude Include="SkinLib\GPDrivePickerListCtrl.h" />
    <ClInclude Include="SkinLib\GPEdit.h" />
    <ClInclude Include="SkinLib\GPHeader.h" />
    <ClInclude Include="SkinLib\GPHooks.h" />
    <ClInclude Include="SkinLib\GPIconButton.h" />
    <ClInclude Include="SkinLib\GPImageInfo.h" />
    <ClInclude Include="SkinLib\MemoryDCPool.h" />
    <ClInclude Include="SkinLib\DirtyRegionManager.h" />
    <ClInclude Include="SkinLib\FastImageRenderer.h" />
    <ClInclude Include="SkinLib\LazyImageLoader.h" />
    <ClInclude Include="SkinLib\PreloadStrategy.h" />
    <ClInclude Include="SkinLib\GPListBox.h" />
    <ClInclude Include="SkinLib\GPListCtrl.h" />
    <ClInclude Include="SkinLib\GPListHeader.h" />
    <ClInclude Include="SkinLib\GPMsgBox.h" />
    <ClInclude Include="SkinLib\GPMsgBoxEx.h" />
    <ClInclude Include="SkinLib\GPProgress.h" />
    <ClInclude Include="SkinLib\GPQScrollBar.h" />
    <ClInclude Include="SkinLib\GPQScrollBarApi.h" />
    <ClInclude Include="SkinLib\GPQScrollBarDraw.h" />
    <ClInclude Include="SkinLib\GPScrollBar.h" />
    <ClInclude Include="SkinLib\GPSkinInfo.h" />
    <ClInclude Include="SkinLib\GPStatic.h" />
    <ClInclude Include="SkinLib\GPTaskList.h" />
    <ClInclude Include="SkinLib\GPXControl.h" />
    <ClInclude Include="SkinLib\language.h" />
    <ClInclude Include="SkinLib\XZipUnZip\XUnzip.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="GDIPlusSkin.cpp" />
    <ClCompile Include="GDIPlusSkinH.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="SkinLib\command\Functions.cpp" />
    <ClCompile Include="SkinLib\command\OSVersion.cpp" />
    <ClCompile Include="SkinLib\GPAdvToolList.cpp" />
    <ClCompile Include="SkinLib\GPAdvToolsTab.cpp" />
    <ClCompile Include="SkinLib\GPButton.cpp" />
    <ClCompile Include="SkinLib\GPCheckBox.cpp" />
    <ClCompile Include="SkinLib\GPChileDlgSkinBase.cpp" />
    <ClCompile Include="SkinLib\GPDlgSkinBase.cpp" />
    <ClCompile Include="SkinLib\GPDrivePickerListCtrl.cpp" />
    <ClCompile Include="SkinLib\GPEdit.cpp" />
    <ClCompile Include="SkinLib\GPHeader.cpp" />
    <ClCompile Include="SkinLib\GPHooks.cpp" />
    <ClCompile Include="SkinLib\GPIconButton.cpp" />
    <ClCompile Include="SkinLib\GPImageInfo.cpp" />
    <ClCompile Include="SkinLib\GPkinInfo.cpp" />
    <ClCompile Include="SkinLib\LazyImageLoader.cpp" />
    <ClCompile Include="SkinLib\GPListBox.cpp" />
    <ClCompile Include="SkinLib\GPListCtrl.cpp" />
    <ClCompile Include="SkinLib\GPListHeader.cpp" />
    <ClCompile Include="SkinLib\GPMsgBox.cpp" />
    <ClCompile Include="SkinLib\GPMsgBoxEx.cpp" />
    <ClCompile Include="SkinLib\GPProgress.cpp" />
    <ClCompile Include="SkinLib\GPQScrollBar.cpp" />
    <ClCompile Include="SkinLib\GPQScrollBarApi.cpp" />
    <ClCompile Include="SkinLib\GPQScrollBarDraw.cpp" />
    <ClCompile Include="SkinLib\GPScrollBar.cpp" />
    <ClCompile Include="SkinLib\GPStatic.cpp" />
    <ClCompile Include="SkinLib\GPTaskList.cpp" />
    <ClCompile Include="SkinLib\GPXControl.cpp" />
    <ClCompile Include="SkinLib\language.cpp" />
    <ClCompile Include="SkinLib\XZipUnZip\XUnzip.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>