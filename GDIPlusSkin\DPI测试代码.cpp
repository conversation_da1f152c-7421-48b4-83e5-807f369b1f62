// 在您的对话框类的头文件(.h)中添加：
protected:
    afx_msg LRESULT OnDpiChanged(WPARAM wParam, LPARAM lParam);

// 在消息映射中添加：
BEGIN_MESSAGE_MAP(CYourDialog, CGPDlgSkinBase)
    ON_MESSAGE(WM_DPICHANGED, OnDpiChanged)
END_MESSAGE_MAP()

// 在实现文件(.cpp)中添加测试函数：
LRESULT CYourDialog::OnDpiChanged(WPARAM wParam, LPARAM lParam)
{
    // 添加调试输出
    UINT newDpiX = LOWORD(wParam);
    UINT newDpiY = HIWORD(wParam);
    float newDPI = newDpiX / 96.0f;
    
    // 在调试窗口中显示DPI变化
    CString msg;
    msg.Format(_T("DPI Changed: %d -> %.2f%%"), newDpiX, newDPI * 100);
    OutputDebugString(msg);
    
    // 也可以用消息框测试（仅用于调试）
    // MessageBox(msg, _T("DPI Change Debug"));
    
    // 调用基类处理
    return CGPDlgSkinBase::OnDpiChanged(wParam, lParam);
}

// 在OnInitDialog中添加调试信息：
BOOL CYourDialog::OnInitDialog()
{
    CGPDlgSkinBase::OnInitDialog();
    
    // 显示初始DPI值
    float currentDPI = GetCurrentDPI(GetSafeHwnd());
    CString msg;
    msg.Format(_T("Initial DPI: %.2f%% (%.2f)"), currentDPI * 100, currentDPI);
    OutputDebugString(msg);
    
    return TRUE;
}
