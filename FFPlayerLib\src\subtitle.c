﻿#include "subtitle.h"
#include <libavformat/avformat.h>
#include <libavutil/time.h>
#include <libavutil/mem.h>
#include <libavutil/avstring.h>
#include <windows.h>

/* 全局 libass 实例（单实例播放器假设） */
ASS_Library*   g_assLib    = NULL;
ASS_Renderer*  g_assRender = NULL;
ASS_Track*     g_assTrack  = NULL;

static SDL_Texture* g_subTex = NULL;
static int g_subW = 0, g_subH = 0;

static void ensure_ass_renderer( VideoState* is )
{
    if (!g_assLib)
        g_assLib = ass_library_init();
    if (!g_assRender && g_assLib) {
        g_assRender = ass_renderer_init( g_assLib );
        if (g_assRender) {
            ass_set_frame_size( g_assRender , is->width ? is->width : default_width ,
                                 is->height ? is->height : default_height );
        }
    }
}

int subtitle_open( const wchar_t* path )
{
    subtitle_close();
    if (!path || !*path) return -1;
    /* wchar ? UTF-8 */
    int u8len = WideCharToMultiByte( CP_UTF8 , 0 , path , -1 , NULL , 0 , NULL , NULL );
    char* u8 = av_malloc( u8len );
    WideCharToMultiByte( CP_UTF8 , 0 , path , -1 , u8 , u8len , NULL , NULL );

    ensure_ass_renderer( g_vs );
    if (!g_assRender) { av_free(u8); return -2; }

    g_assTrack = ass_read_file( g_assLib , u8 , NULL );
    av_free(u8);
    if (!g_assTrack) return -3;
    return 0;
}

void subtitle_close( void )
{
    if (g_assTrack) { ass_free_track( g_assTrack ); g_assTrack = NULL; }
    if (g_subTex) { SDL_DestroyTexture( g_subTex ); g_subTex = NULL; }
}

SDL_Texture* subtitle_render( VideoState* is , double vid_pts , SDL_Rect dst )
{
    if (!g_assTrack || !g_assRender) return NULL;
    ass_set_frame_size( g_assRender , dst.w , dst.h );
    int detect_change = 0;
    ASS_Image* img = ass_render_frame( g_assRender , g_assTrack , (long long)( vid_pts * 1000.0 ) , &detect_change );
    if (!img) return NULL;
    /* 将 ASS_Image 链表混合到 RGBA */
    int w = dst.w, h = dst.h;
    if (w<=0||h<=0) return NULL;
    if (detect_change || !g_subTex || w!=g_subW || h!=g_subH) {
        if (g_subTex) SDL_DestroyTexture( g_subTex );
        g_subTex = SDL_CreateTexture( renderer , SDL_PIXELFORMAT_ARGB8888 , SDL_TEXTUREACCESS_STREAMING , w , h );
        SDL_SetTextureBlendMode( g_subTex , SDL_BLENDMODE_BLEND );
        g_subW=w; g_subH=h;
    }
    void* pixels; int pitch;
    if (SDL_LockTexture( g_subTex , NULL , &pixels , &pitch )<0) return g_subTex;
    memset( pixels , 0 , pitch * h );
    for (ASS_Image* cur = img; cur; cur = cur->next) {
        uint8_t a = cur->color>>24 ^ 0xFF;
        uint8_t r = cur->color>>16 & 0xFF;
        uint8_t g = cur->color>>8 & 0xFF;
        uint8_t b = cur->color & 0xFF;
        for (int y=0; y<cur->h; ++y) {
            uint8_t* dstLine = (uint8_t*)pixels + (cur->dst_y + y)*pitch + (cur->dst_x)*4;
            uint8_t* srcLine = cur->bitmap + y*cur->stride;
            for (int x=0; x<cur->w; ++x) {
                uint8_t alpha = srcLine[x];
                uint32_t* p = (uint32_t*)dstLine + x;
                uint8_t srcA = alpha * a / 255;
                if (srcA==0) continue;
                uint8_t srcR = r; uint8_t srcG = g; uint8_t srcB = b;
                uint8_t dstA = (*p)>>24; uint8_t dstR=(*p)>>16 &0xFF, dstG=(*p)>>8 &0xFF, dstB=*p &0xFF;
                uint8_t outA = srcA + dstA*(255-srcA)/255;
                uint8_t outR = (srcR*srcA + dstR*dstA*(255-srcA)/255)/outA;
                uint8_t outG = (srcG*srcA + dstG*dstA*(255-srcA)/255)/outA;
                uint8_t outB = (srcB*srcA + dstB*dstA*(255-srcA)/255)/outA;
                *p = (outA<<24)|(outR<<16)|(outG<<8)|outB;
            }
        }
    }
    SDL_UnlockTexture( g_subTex );
    return g_subTex;
} 