﻿#pragma once


// CDlgSkin 对话框

class CDlgSkin : public CGPDlgSkinBase
    {
    DECLARE_DYNAMIC( CDlgSkin )

        public:
        CDlgSkin( CWnd* pParent = NULL );   // 标准构造函数
        virtual ~CDlgSkin( );

        // 对话框数据
#ifdef AFX_DESIGN_TIME
        enum { IDD = IDD_DIALOG_SKIN };
#endif

        protected:
        virtual void DoDataExchange( CDataExchange* pDX );    // DDX/DDV 支持

        DECLARE_MESSAGE_MAP( )
        public:
        virtual BOOL OnInitDialog( );
        afx_msg void OnBtnSkin1( );
        afx_msg void OnBtnSkin2( );
        afx_msg void OnBtnSkin3( );
        afx_msg void OnBtnSkin4( );
        afx_msg void OnBtnSkin5( );
        afx_msg void OnBtnSkin6( );
        afx_msg void OnBtnSkin7( );
        afx_msg void OnBtnSkin8( );
        afx_msg void OnBtnSkin9( );
        afx_msg void OnBtnSkin10( );
        afx_msg void OnBtnSkin11( );
        afx_msg void OnBtnSkin12( );
        afx_msg void OnBtnSkin13( );
        afx_msg void OnBtnSkin14( );
        afx_msg void OnBtnSkin15( );
        afx_msg void OnBtnSkin16( );
        void SetWnd( CWnd* pWnd )
            {
            m_pWnd = pWnd;
            }

        void SetSkin( int iSkin );
        private:
        CGPXControl m_skin1;
        CGPXControl m_skin2;
        CGPXControl m_skin3;
        CGPXControl m_skin4;
        CGPXControl m_skin5;
        CGPXControl m_skin6;
        CGPXControl m_skin7;
        CGPXControl m_skin8;
        CGPXControl m_skin9;
        CGPXControl m_skin10;
        CGPXControl m_skin11;
        CGPXControl m_skin12;
        CGPXControl m_skin13;
        CGPXControl m_skin14;
        CGPXControl m_skin15;
        CGPXControl m_skin16;
        CWnd* m_pWnd;
    };