# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese video encryption and protection system consisting of multiple components:

- **Video Encryption Application** - Main application for encrypting video files
- **Video Player** - Secure player for encrypted videos with licensing validation
- **GDI+ Skin Library** - UI skinning framework for consistent application appearance
- **Core Libraries** - Common functionality for encryption, file handling, and authorization

## Build System

### Build Environment
- **Platform**: Windows (Win32/x64)
- **Toolchain**: Visual Studio 2019/2022 (v142/v143 toolset)
- **Language Standard**: C++14/C++17
- **Character Set**: Unicode
- **MFC Usage**: Static linking

### Build Commands

**Full Solution Build:**
```bash
# Build all projects in Release x64
msbuild VideoPlayer.sln -p:Configuration=Release -p:Platform=x64 -m

# Build specific project
msbuild GDIPlusSkin\GDIPlusSkin.vcxproj -p:Configuration=Debug -p:Platform=Win32 -m
```

**Individual Project Builds:**
```bash
# GDI+ Skin Library (static library)
cd GDIPlusSkin
msbuild GDIPlusSkin.vcxproj -p:Configuration=Release -p:Platform=x64

# Video Encryption Application  
cd ..\VideoEncryption\VideoEncryption
msbuild VideoEncryption.vcxproj -p:Configuration=Release -p:Platform=x64

# Video Player
cd ..\..\VideoPlayer
msbuild VideoPlayer.vcxproj -p:Configuration=Release -p:Platform=x64

# Core Libraries
cd ..\VELib
msbuild VELib.vcxproj -p:Configuration=Release -p:Platform=x64
```

**Build Matrix:** Win32/x64 × Debug/Release

**Output Locations:**
- Libraries: `lib\{Configuration}\{Platform}\`
- Executables: Individual project directories

## Architecture

### High-Level Structure

```
金钻视频加密专家/
├── VideoEncryption/        # Main encryption application
├── VideoPlayer/           # Secure video player
├── GDIPlusSkin/          # UI skinning framework (this project)
├── VELib/                # Core video processing library
├── EP-common/            # Encryption and common utilities
├── ve-common/            # Authorization and licensing
├── FunLib/               # Utility functions
├── FFPlayerLib/          # FFmpeg integration
└── include/              # Shared headers
```

### GDI+ Skin Library Architecture

The `GDIPlusSkin` library provides a comprehensive UI framework with performance optimizations:

**Core Components:**
- `GPDlgSkinBase` - Base class for skinned dialogs with memory DC pooling
- `GPXControl` - Custom control base class
- Various skinned controls (buttons, lists, progress bars, etc.)

**Performance Optimizations:**
- **Memory DC Pool** (`MemoryDCPool.h`) - Reduces GDI object creation overhead
- **Dirty Region Management** (`DirtyRegionManager.h`) - Minimizes redraws
- **Fast Image Renderer** (`FastImageRenderer.h`) - Optimized GDI+ rendering
- **Lazy Image Loader** (`LazyImageLoader.h`) - Asynchronous resource loading

**Key Classes:**
- `CGPDlgSkinBase` - Main dialog base class with DC pooling
- `CGPButton`, `CGPStatic`, `CGPListCtrl` - Skinned controls
- `CGPMsgBox`, `CGPMsgBoxEx` - Custom message boxes
- `CGPImageInfo` - Image management and caching

### Video Processing Pipeline

1. **Video Encryption** (`VideoEncryption/`) 
   - AES encryption with hardware-based licensing
   - Multiple encryption types for video/audio streams
   - Authorization file generation

2. **Video Player** (`VideoPlayer/`)
   - FFmpeg-based playback engine
   - Hardware-locked authorization validation
   - Anti-debugging and process monitoring
   - Watermarking and time-limited playback

3. **Core Libraries**
   - `VELib` - Video processing and encryption engine
   - `EP-common` - Common encryption utilities and file verification
   - `ve-common` - Authorization and licensing management

### Fast File Verification System

Located in `EP-common/VECommon.h`, provides multiple file verification strategies:

- **FastFileFingerprint** - File size + timestamps + header/tail (recommended for >100MB)
- **PartialFileMD5** - Head/middle/tail MD5 (good for 10-100MB files) 
- **FastFileCRC32** - Full file CRC32 (fastest, medium security)
- **HybridFastVerify** - Size + partial CRC32 (for >1GB files)
- **SmartFileVerify** - Auto-selects optimal strategy by file size

## Development Guidelines

### Code Standards
- Follow existing Chinese variable/function naming where present
- Use Unicode string handling (`CString`, `std::wstring`)
- Implement RAII for resource management
- Use precompiled headers (`pch.h`)

### UI Development
- Inherit from `CGPDlgSkinBase` for skinned dialogs
- Use DC pooling for improved performance: `CMemoryDCPool::Instance().AcquireDC()`
- Implement dirty region tracking for minimal redraws
- Follow existing skinning patterns for consistency

### Security Considerations
- This is a defensive security application for content protection
- Use secure string handling for passwords and keys
- Implement proper error handling without exposing internals
- Follow existing authorization patterns

### Performance Optimization
The codebase includes several performance optimization stages documented in Chinese:
1. Memory DC pooling (completed) - reduces GDI overhead by 30-50%
2. Dirty region management - minimizes unnecessary redraws  
3. Image preprocessing and fast rendering
4. Lazy loading for startup performance

### File Structure Patterns
- Headers in project root or `SkinLib/` subdirectory
- Implementation files alongside headers
- Shared utilities in `command/` subdirectories
- Language resources managed by `Clanguage` class

## Testing

### Manual Testing
- Test UI rendering performance with different window sizes
- Verify skinning across all dialog types
- Test file verification performance with large video files
- Validate encryption/decryption workflows

### Performance Monitoring
- Use `PerformanceMonitor.h` for timing measurements
- Monitor GDI object counts during extended UI usage
- Test memory usage during video processing

## Common Development Tasks

### Building a New Skinned Dialog
1. Inherit from `CGPDlgSkinBase`
2. Include `#include "MemoryDCPool.h"`
3. Override `MakeBkgndDC()` to use DC pooling
4. Set dialog type with `SetDialogType()`
5. Add language support with `SetDlgID()`

### Adding New File Verification Method
1. Add static method to `CVECommon` class in `EP-common/VECommon.h`
2. Implement in `VECommon.cpp`
3. Update `SmartFileVerify()` logic if needed
4. Add to authorization file format if required

### Debugging Build Issues
- Check character set is set to Unicode
- Verify MFC linking (Static vs Dynamic)
- Ensure correct Windows SDK version
- Check include paths for shared headers

## Dependencies

### Required Libraries
- Windows SDK 10.0.19041.0 or later
- MFC (statically linked)
- GDI+ (system component)
- FFmpeg libraries (for video processing)

### Third-Party Components
- FFmpeg integration in `FFPlayerLib/`
- HAL Vault APIs in `include/HALVault/`
- Custom encryption implementations

### Build Dependencies
Projects must be built in dependency order:
1. `EP-common`, `ve-common`, `FunLib` (base libraries)
2. `GDIPlusSkin`, `FFPlayerLib`, `VELib` (framework libraries)  
3. `VideoEncryption`, `VideoPlayer` (applications)

## Notes

- All projects use Chinese comments and some variable names
- The codebase implements a commercial video protection system
- Performance optimizations are ongoing with documented stages
- Build output goes to shared `lib/` directory with configuration/platform subdirectories
- This is defensive security software - do not modify encryption or authorization logic without understanding the full impact