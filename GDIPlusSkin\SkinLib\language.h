#pragma once

#define  LANGUAGE_DIR     L"language"
#define  LANGUAGE_RE_TYPE L"LANGUAGE"

class Clanguage
    {
        public:
        Clanguage ( );
        ~Clanguage ( );
        CString GetValue (const CString &section , const CString &key );
        CString GetString (const CString &key  );
        CString GetSoftInfo ( const CString &key );
        CString GetMenuText ( const CString &key );
        CString GetPopupMenuText ( const CString &key );
        CString GetTrayMenuText ( const CString &key );
        CString GetLanguagePath ( void )
            {
            return m_strCurrentLangPath;
            }

        DWORD GetLastError ( void )
            {
            return m_dwLastError;
            }

        void SetFileName( const CString& strFileName )
            {
            m_fileName = strFileName;
            }
        private:
        CString m_strDefaultLangPath;
        CString m_strCurrentLangPath;
        CString m_fileName;
        DWORD   m_dwLastError;
    };

