﻿#include "filter.h"
#include "cmdutils.h"
#include <string.h>

// 引入 ffplay.c 中的全局水印描述
extern char g_watermark_desc[512];

const  TextureFormatEntry   sdl_texture_format_map [ ] = {
    { AV_PIX_FMT_RGB8,           SDL_PIXELFORMAT_RGB332 },
    { AV_PIX_FMT_RGB444,         SDL_PIXELFORMAT_RGB444 },
    { AV_PIX_FMT_RGB555,         SDL_PIXELFORMAT_RGB555 },
    { AV_PIX_FMT_BGR555,         SDL_PIXELFORMAT_BGR555 },
    { AV_PIX_FMT_RGB565,         SDL_PIXELFORMAT_RGB565 },
    { AV_PIX_FMT_BGR565,         SDL_PIXELFORMAT_BGR565 },
    { AV_PIX_FMT_RGB24,          SDL_PIXELFORMAT_RGB24 },
    { AV_PIX_FMT_BGR24,          SDL_PIXELFORMAT_BGR24 },
    { AV_PIX_FMT_0RGB32,         SDL_PIXELFORMAT_RGB888 },
    { AV_PIX_FMT_0BGR32,         SDL_PIXELFORMAT_BGR888 },
    { AV_PIX_FMT_NE( RGB0, 0BGR ), SDL_PIXELFORMAT_RGBX8888 },
    { AV_PIX_FMT_NE( BGR0, 0RGB ), SDL_PIXELFORMAT_BGRX8888 },
    { AV_PIX_FMT_RGB32,          SDL_PIXELFORMAT_ARGB8888 },
    { AV_PIX_FMT_RGB32_1,        SDL_PIXELFORMAT_RGBA8888 },
    { AV_PIX_FMT_BGR32,          SDL_PIXELFORMAT_ABGR8888 },
    { AV_PIX_FMT_BGR32_1,        SDL_PIXELFORMAT_BGRA8888 },
    { AV_PIX_FMT_YUV420P,        SDL_PIXELFORMAT_IYUV },
    { AV_PIX_FMT_YUYV422,        SDL_PIXELFORMAT_YUY2 },
    { AV_PIX_FMT_UYVY422,        SDL_PIXELFORMAT_UYVY },
    { AV_PIX_FMT_NONE,           SDL_PIXELFORMAT_UNKNOWN },
    };


static enum AVColorSpace sdl_supported_color_spaces [ ] = {
    AVCOL_SPC_BT709,
    AVCOL_SPC_BT470BG,
    AVCOL_SPC_SMPTE170M,
    AVCOL_SPC_UNSPECIFIED,
    };

static int autorotate = 1;

AVDictionary* sws_dict = NULL;  // 替代 cmdutils.c 中的全局变量，保持空字典亦可编译

int configure_filtergraph( AVFilterGraph* graph , const char* filtergraph ,
    AVFilterContext* source_ctx , AVFilterContext* sink_ctx )
    {
    int ret , i;
    int nb_filters = graph->nb_filters;
    AVFilterInOut* outputs = NULL , * inputs = NULL;

    if (filtergraph) {
        outputs = avfilter_inout_alloc( );
        inputs = avfilter_inout_alloc( );
        if (!outputs || !inputs) {
            ret = AVERROR( ENOMEM );
            goto fail;
            }

        outputs->name = av_strdup( "in" );
        outputs->filter_ctx = source_ctx;
        outputs->pad_idx = 0;
        outputs->next = NULL;

        inputs->name = av_strdup( "out" );
        inputs->filter_ctx = sink_ctx;
        inputs->pad_idx = 0;
        inputs->next = NULL;

        if (( ret = avfilter_graph_parse_ptr( graph , filtergraph , &inputs , &outputs , NULL ) ) < 0)
            goto fail;
        }
    else {
        if (( ret = avfilter_link( source_ctx , 0 , sink_ctx , 0 ) ) < 0)
            goto fail;
        }

    /* Reorder the filters to ensure that inputs of the custom filters are merged first */
    for (i = 0; i < graph->nb_filters - nb_filters; i++)
        FFSWAP( AVFilterContext* , graph->filters [ i ] , graph->filters [ i + nb_filters ] );

    ret = avfilter_graph_config( graph , NULL );
fail:
    avfilter_inout_free( &outputs );
    avfilter_inout_free( &inputs );
    return ret;
    }

int configure_video_filters( AVFilterGraph* graph , VideoState* is , const char* vfilters , AVFrame* frame )
    {
    enum AVPixelFormat pix_fmts [ FF_ARRAY_ELEMS( sdl_texture_format_map ) ];
    char sws_flags_str [ 512 ] = "";
    char buffersrc_args [ 256 ];
    int ret;
    AVFilterContext* filt_src = NULL , * filt_out = NULL , * last_filter = NULL;
    AVCodecParameters* codecpar = is->video_st->codecpar;
    AVRational fr = av_guess_frame_rate( is->ic , is->video_st , NULL );
    const AVDictionaryEntry* e = NULL;
    int nb_pix_fmts = 0;
    int i , j;
    AVBufferSrcParameters* par = av_buffersrc_parameters_alloc( );

    if (!par)
        return AVERROR( ENOMEM );

    for (i = 0; i < renderer_info.num_texture_formats; i++) {
        for (j = 0; j < FF_ARRAY_ELEMS( sdl_texture_format_map ) - 1; j++) {
            if (renderer_info.texture_formats [ i ] == sdl_texture_format_map [ j ].texture_fmt) {
                pix_fmts [ nb_pix_fmts++ ] = sdl_texture_format_map [ j ].format;
                break;
                }
            }
        }
    pix_fmts [ nb_pix_fmts ] = AV_PIX_FMT_NONE;

    while (( e = av_dict_iterate( sws_dict , e ) )) {
        if (!strcmp( e->key , "sws_flags" )) {
            av_strlcatf( sws_flags_str , sizeof( sws_flags_str ) , "%s=%s:" , "flags" , e->value );
            }
        else
            av_strlcatf( sws_flags_str , sizeof( sws_flags_str ) , "%s=%s:" , e->key , e->value );
        }
    if (strlen( sws_flags_str ))
        sws_flags_str [ strlen( sws_flags_str ) - 1 ] = '\0';

    graph->scale_sws_opts = av_strdup( sws_flags_str );

    snprintf( buffersrc_args , sizeof( buffersrc_args ) ,
        "video_size=%dx%d:pix_fmt=%d:time_base=%d/%d:pixel_aspect=%d/%d:"
        "colorspace=%d:range=%d" ,
        frame->width , frame->height , frame->format ,
        is->video_st->time_base.num , is->video_st->time_base.den ,
        codecpar->sample_aspect_ratio.num , FFMAX( codecpar->sample_aspect_ratio.den , 1 ) ,
        frame->colorspace , frame->color_range );
    if (fr.num && fr.den)
        av_strlcatf( buffersrc_args , sizeof( buffersrc_args ) , ":frame_rate=%d/%d" , fr.num , fr.den );

    if (( ret = avfilter_graph_create_filter( &filt_src ,
        avfilter_get_by_name( "buffer" ) ,
        "ffplay_buffer" , buffersrc_args , NULL ,
        graph ) ) < 0)
        goto fail;
    par->hw_frames_ctx = frame->hw_frames_ctx;
    ret = av_buffersrc_parameters_set( filt_src , par );
    if (ret < 0)
        goto fail;

    ret = avfilter_graph_create_filter( &filt_out ,
        avfilter_get_by_name( "buffersink" ) ,
        "ffplay_buffersink" , NULL , NULL , graph );
    if (ret < 0)
        goto fail;

    if (( ret = av_opt_set_int_list( filt_out , "pix_fmts" , pix_fmts , AV_PIX_FMT_NONE , AV_OPT_SEARCH_CHILDREN ) ) < 0)
        goto fail;
    if (!vk_renderer &&
        ( ret = av_opt_set_int_list( filt_out , "color_spaces" , sdl_supported_color_spaces , AVCOL_SPC_UNSPECIFIED , AV_OPT_SEARCH_CHILDREN ) ) < 0)
        goto fail;

    last_filter = filt_out;

    /* Note: this macro adds a filter before the lastly added filter, so the
     * processing order of the filters is in reverse */
#define INSERT_FILT(name, arg) do {                                          \
    AVFilterContext *filt_ctx;                                               \
                                                                             \
    ret = avfilter_graph_create_filter(&filt_ctx,                            \
                                       avfilter_get_by_name(name),           \
                                       "ffplay_" name, arg, NULL, graph);    \
    if (ret < 0)                                                             \
        goto fail;                                                           \
                                                                             \
    ret = avfilter_link(filt_ctx, 0, last_filter, 0);                        \
    if (ret < 0)                                                             \
        goto fail;                                                           \
                                                                             \
    last_filter = filt_ctx;                                                  \
} while (0)

    if (autorotate) {
        double theta = 0.0;
        int32_t* displaymatrix = NULL;
        AVFrameSideData* sd = av_frame_get_side_data( frame , AV_FRAME_DATA_DISPLAYMATRIX );
        if (sd)
            displaymatrix = ( int32_t* ) sd->data;
        if (!displaymatrix) {
            const AVPacketSideData* psd = av_packet_side_data_get( is->video_st->codecpar->coded_side_data ,
                is->video_st->codecpar->nb_coded_side_data ,
                AV_PKT_DATA_DISPLAYMATRIX );
            if (psd)
                displaymatrix = ( int32_t* ) psd->data;
            }
        theta = get_rotation( displaymatrix );

        if (fabs( theta - 90 ) < 1.0) {
            INSERT_FILT( "transpose" , displaymatrix [ 3 ] > 0 ? "cclock_flip" : "clock" );
            }
        else if (fabs( theta - 180 ) < 1.0) {
            if (displaymatrix [ 0 ] < 0)
                INSERT_FILT( "hflip" , NULL );
            if (displaymatrix [ 4 ] < 0)
                INSERT_FILT( "vflip" , NULL );
            }
        else if (fabs( theta - 270 ) < 1.0) {
            INSERT_FILT( "transpose" , displaymatrix [ 3 ] < 0 ? "clock_flip" : "cclock" );
            }
        else if (fabs( theta ) > 1.0) {
            char rotate_buf [ 64 ];
            snprintf( rotate_buf , sizeof( rotate_buf ) , "%f*PI/180" , theta );
            INSERT_FILT( "rotate" , rotate_buf );
            }
        else {
            if (displaymatrix && displaymatrix [ 4 ] < 0)
                INSERT_FILT( "vflip" , NULL );
            }
        }

    /* 若已设置文字水印，则在靠近输出端插入 drawtext 滤镜 */
    if (g_watermark_desc[0]) {
        INSERT_FILT( "drawtext" , g_watermark_desc );
    }

    if (( ret = configure_filtergraph( graph , vfilters , filt_src , last_filter ) ) < 0)
        goto fail;

    is->in_video_filter = filt_src;
    is->out_video_filter = filt_out;

fail:
    av_freep( &par );
    return ret;
    }


int opt_add_vfilter( void* optctx , const char* opt , const char* arg )
    {
    int ret = GROW_ARRAY( vfilters_list , nb_vfilters );
    if (ret < 0)
        return ret;

    vfilters_list [ nb_vfilters - 1 ] = av_strdup( arg );
    if (!vfilters_list [ nb_vfilters - 1 ])
        return AVERROR( ENOMEM );

    return 0;
    }