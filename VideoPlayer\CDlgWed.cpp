﻿// CDlgWed.cpp: 实现文件
//

#include "pch.h"
#include "VideoPlayer.h"
#include "CDlgWed.h"
#include "afxdialogex.h"


// CDlgWed 对话框

IMPLEMENT_DYNAMIC(CDlgWed, CDialogEx)

CDlgWed::CDlgWed(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_WED, pParent)
	, m_strURL(_T("" ) )
{

}

CDlgWed::~CDlgWed()
{
}

void CDlgWed::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}


BEGIN_MESSAGE_MAP(CDlgWed, CDialogEx)
	ON_WM_SIZE( )
END_MESSAGE_MAP()


// CDlgWed 消息处理程序


BOOL CDlgWed::OnInitDialog( )
	{
	CDialogEx::OnInitDialog( );

	if (!m_vw.Create(m_strURL.GetString  () , this )) {
		AfxMessageBox( m_vw.GetLastError( ).c_str( ) );
		}

	SetWindowText(APP_NAME);
	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
	}


void CDlgWed::OnSize( UINT nType , int cx , int cy )
	{
	CDialogEx::OnSize( nType , cx , cy );

    if (nType != SIZE_MINIMIZED && m_vw.GetSafeHwnd( )) {
        CRect rcClient;
        GetClientRect( &rcClient );
        m_vw.MoveWindow( &rcClient );  // 调整大小跟随父窗口
        }
	}
