﻿#pragma once

#include <ui\StaticEx.h>
#include <ui\EditEx.h>
#include "Empower.h"
// CDlgInfo 对话框

class CDlgInfo : public CDialogEx
{
	DECLARE_DYNAMIC(CDlgInfo)

public:
	CDlgInfo(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~CDlgInfo();
    virtual BOOL OnInitDialog( );
    afx_msg void OnBnClickedButtonInfoPlay( );
	afx_msg void OnBnClickedButtonInfoBuy( );

	UINT GetErrorNum( void )
		{
		return m_ErrorNum;
		}
// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_INFO };
#endif
	void SetVa( CEmpower* pva )
		{
		m_pva = pva;
		}

	void SetFilePath( const CString&  strFilePath )
		{
		m_strFilePath = strFilePath;
		}
protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
    virtual BOOL PreTranslateMessage( MSG* pMsg );
   
    virtual BOOL OnCommand( WPARAM wParam , LPARAM lParam ) override;
	afx_msg LRESULT OnProgress( WPARAM wParam , LPARAM lParam );
	afx_msg LRESULT OnOpComplete( WPARAM wParam , LPARAM lParam );
	DECLARE_MESSAGE_MAP()
    static UINT __cdecl WorkThraed( LPVOID lParam );
	bool Md5Mat( void );
	void CheckMd5Finish( );
	private:
	CString  m_strFilePath;
	CEmpower* m_pva;
	UINT      m_ErrorNum;
	CProgressCtrl m_progressCtrl;
    StaticEx m_gsID;
    StaticEx m_gsPass;
	StaticEx m_gsInfo;
    EditEx m_geID;
    EditEx m_gePass;
	};
