﻿// CDlgOpenFile.cpp: 实现文件
//

#include "pch.h"
#include "VideoPlayer.h"
#include "CDlgOpenFile.h"
#include "afxdialogex.h"
#include "funLib.h"
#include "ScopeGuard.h"
#include "progressHand.h"
#include "..\EP-common\VECommon.h"
#include "..\\EP-common\\VeErrorCodes.h"
#include <closewindows/WindowCloser.h>
#include <time\timeTools.h>
#include <disk\disk32.h>
#include <string\strTools.h>
#include <net\netTools.h>
#include <algorithm>
#include <sys/stat.h>  // 添加_stat64支持

// CDlgOpenFile 对话框

IMPLEMENT_DYNAMIC(CDlgOpenFile, CDialogEx)

CDlgOpenFile::CDlgOpenFile(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_PROGRESS, pParent)
	, m_pva( nullptr )
{

}

CDlgOpenFile::~CDlgOpenFile( )
    { }

void CDlgOpenFile::DoDataExchange( CDataExchange* pDX )
    {
    CDialogEx::DoDataExchange( pDX );
    DDX_Control( pDX , IDC_PROGRESS_POS , m_progressCtrl );
    }


BEGIN_MESSAGE_MAP( CDlgOpenFile , CDialogEx )
    ON_MESSAGE( UWM_OP_COMPLETE ,          &CDlgOpenFile::OnOpComplete )
END_MESSAGE_MAP( )


// CDlgOpenFile 消息处理程序
BOOL CDlgOpenFile::OnInitDialog( )
    {
    CDialogEx::OnInitDialog( );

    m_progressCtrl.SetRange( 0 , 100 );
    m_progressCtrl.SetStep( 1 );
    m_progressCtrl.SetPos( 0 );

    AfxBeginThread( WorkThread , this );
    return TRUE;
    }
//=================================================================
static CString GetErrorMessage( VE::VeOpenFileError code )
    {
    switch (code)
        {
        case VE::VeOpenFileError::SUCCESS:                   return _T("操作成功");
        case VE::VeOpenFileError::ERR_VM_ENVIRONMENT:        return _T("检测到虚拟机环境，无法播放！");
        case VE::VeOpenFileError::ERR_DANGEROUS_PROCESS:     return _T("检测到危险进程，已阻止播放！");
        case VE::VeOpenFileError::ERR_AUTH_EXPIRED:          return _T("授权已过期或系统时间异常！");
        case VE::VeOpenFileError::ERR_DEVICE_NOT_AUTHORIZED: return _T("当前设备未获得授权！");
        case VE::VeOpenFileError::ERR_AUTH_REVOKED:          return _T("该授权已被收回，无法播放！");
        case VE::VeOpenFileError::ERR_DEVICE_BLACK_LISTED:   return _T("当前设备已被拉黑，无法播放！");
        case VE::VeOpenFileError::ERR_PLAYER_VERSION_LOW:    return _T("播放器版本过低，请升级！");
        case VE::VeOpenFileError::ERR_NETWORK_REQUIRED:      return _T("播放需要联网，请检查网络连接！");
        case VE::VeOpenFileError::ERR_FILE_VERIFY_FAILED:    return _T("视频文件校验失败，可能被篡改！");
        default:                                            return _T("未知错误！");
        }
    }
//=================================================================
LRESULT CDlgOpenFile::OnOpComplete( WPARAM wParam , LPARAM lParam )
    {
    VE::VeOpenFileError result = static_cast< VE::VeOpenFileError >( wParam );

    if (result == VE::VeOpenFileError::SUCCESS) {
        OnOK( );
        }
    else {
        MessageBox( GetErrorMessage( result ) , _T("播放失败") , MB_ICONERROR | MB_OK );
        OnCancel( );
        }

    return 0;
    }
//=================================================================
UINT CDlgOpenFile::WorkThread( LPVOID lParam )
    {
    CDlgOpenFile* pThis = reinterpret_cast< CDlgOpenFile* >( lParam );
    ASSERT( pThis != NULL );
    VE::VeOpenFileError err = VE::VeOpenFileError::SUCCESS;

    err = pThis->EmpowerCheck();
    if (err != VE::VeOpenFileError::SUCCESS) goto _exit;

    pThis->CheckEmpFinish();

    err = pThis->Init();
    if (err != VE::VeOpenFileError::SUCCESS) goto _exit;

    pThis->CheckInitFinish();

    err = pThis->PlayVerCheck();
    if (err != VE::VeOpenFileError::SUCCESS) goto _exit;

    pThis->CheckPlayFinish();

_exit:
    pThis->PostMessage( UWM_OP_COMPLETE, static_cast<WPARAM>(err), 0);
    return 0;
    }
//=================================================================
//环境检测
VE::VeOpenFileError CDlgOpenFile::EmpowerCheck( void )
    {
    VMP_PROTECTED_BLOCK( "VM_Check" )
        {
        //虚拟机检测
        if (m_pva->IsVirPlay( ) && IsRunningInVM( )) {
            return VE::VeOpenFileError::ERR_VM_ENVIRONMENT;
            }
        }

    m_progressCtrl.SetPos( 5 );

    //授权过期时间检测
     // 时间验证
    VMP_PROTECTED_BLOCK( "Time_Validation" )
        {
        std::time_t fileTime;

        bool bfile = TimeTools::GetTimeFromEventLog( fileTime );

        std::time_t NTPTime;
        bool bNTP = TimeTools::GetTimeFromNTP( NTPTime );

        if (!bfile && !bNTP) {
            return VE::VeOpenFileError::ERR_AUTH_EXPIRED;
            }


        m_progressCtrl.SetPos( 10 );

        // 选择最新的参考时间
        std::time_t refTime = 0;
        if (bfile && bNTP) {
            refTime = ( std::max ) ( fileTime , NTPTime );
            }
        else if (bfile) {
            refTime = fileTime;
            }
        else if (bNTP) {
            refTime = NTPTime;
            }

        if (refTime && TimeTools::IsSystemTimeTampered( refTime )) {
            // 系统时间异常，返回失败

            return VE::VeOpenFileError::ERR_AUTH_EXPIRED;
            }
        }
    m_progressCtrl.SetPos( 15 );

    //授权过期时间
    VMP_PROTECTED_BLOCK( "Expire_Check" )
        {
        CString strExpireTime = m_pva->GetExpireTime( );
        std::time_t et;
        if (TimeTools::StringToTime( strExpireTime.GetString( ) , et )) {
            if (et < std::time( nullptr )) {
                return VE::VeOpenFileError::ERR_AUTH_EXPIRED;
                }
            }
        }
   
    m_progressCtrl.SetPos( 20 );
    //硬件绑定检测
    int iHardType = m_pva->GetHardType( );
   

    if (0 != iHardType) {
        VMP_PROTECTED_BLOCK( "Hardware_Check" )
            {
            CString strHard = m_pva->GetHardID( );
            CDisk32 d32;
            std::wstring id;
            if (1 == iHardType) {
                ULONGLONG uID = d32.getComputerID( );
                id = StrTools::UInt64ToHexStringW( uID );
                }
            else if (2 == iHardType) {
                ULONGLONG uID( 0 );
                if (d32.GetUsbSerial( m_strFilePath.GetAt( 0 ) )) {
                    uID = d32.SerialNumberToID( );
                    }
                else {
                    uID = d32.getDiskID( m_strFilePath.GetAt( 0 ) );
                    }
                id = StrTools::UInt64ToHexStringW( uID );
                }

            if (strHard.Compare(id.c_str()) != 0) {
                return VE::VeOpenFileError::ERR_DEVICE_NOT_AUTHORIZED;
                }
            }
        }
     return VE::VeOpenFileError::SUCCESS;
    }
//=================================================================
void CDlgOpenFile::CheckEmpFinish( )
    {
    GetDlgItem( IDC_STATIC_1 )->ShowWindow( SW_SHOW );
    m_progressCtrl.SetPos( 30 );
    Sleep( 100 );
    }
//=================================================================
VE::VeOpenFileError CDlgOpenFile::Init( void )
    {
    WindowCloser wc;
    wc.InitCopyScr( );

    std::vector<std::wstring> vp = wc.ScanAndTerminateCopyScrProcesses( );

    if (vp.size( )) {
        return VE::VeOpenFileError::ERR_DANGEROUS_PROCESS;
        }

    m_progressCtrl.SetPos( 40 );

    wc.DisableScreenVideo( );

    if (NULL != wc.RunCloseWindows( )) {
        return VE::VeOpenFileError::ERR_DANGEROUS_PROCESS;
        }

    m_progressCtrl.SetPos( 50 );
    return VE::VeOpenFileError::SUCCESS;
    }
//=================================================================
void CDlgOpenFile::CheckInitFinish( )
    {
    GetDlgItem( IDC_STATIC_2 )->ShowWindow( SW_SHOW );
    m_progressCtrl.SetPos( 60 );
    Sleep( 100 );
    }
//=================================================================
VE::VeOpenFileError CDlgOpenFile::PlayVerCheck( void )
    {
    //播放器版本检测
    int iVer = m_pva->GetEPlayVer( );

    if (iVer < PLAYER_VER) {
        return VE::VeOpenFileError::ERR_PLAYER_VERSION_LOW;
        }

    m_progressCtrl.SetPos( 65 );

    //检查授权是否被收回
    CString strURL = m_pva->GetDeleteEmpURL( );

    if (!strURL.IsEmpty( )) {
        std::wstring err;
        std::wstring text = NetTools::GetUrlTextContent( strURL.GetString( ) , err );

        if (!text.empty( )) {
            CString aid = m_pva->GetAuthID( );
            if (-1 != text.find( aid.GetString( ) )) {
                return VE::VeOpenFileError::ERR_AUTH_REVOKED;
                }
            }
        }
    else {
        strURL = L"www.baidu.com";
        }

    m_progressCtrl.SetPos( 75 );
    //如果播放必须联网
    if (m_pva->IsBreakNet( )) {
        NetTools::UrlReachState urs;
        std::wstring err;
        if (!NetTools::CheckUrlState( strURL.GetString( ) , urs , err )) {
            if (urs == NetTools::UrlReachState::NetworkOffline || urs == NetTools::UrlReachState::DnsFail || urs == NetTools::UrlReachState::ConnectFail) {
                return VE::VeOpenFileError::ERR_NETWORK_REQUIRED;
                }
            }
        }

    //检查硬件ID

    CString strHList = m_pva->GetHBlackList( );

    m_progressCtrl.SetPos( 85 );

    if (!strHList.IsEmpty( )) {
        CDisk32 d32;
        

        ULONGLONG uID = d32.getComputerID( );

        if (0 != uID) {
            std::wstring sid = StrTools::UInt64ToHexStringW( uID );

            if (-1 != strHList.Find( sid.c_str( ) )) {
                return VE::VeOpenFileError::ERR_DEVICE_BLACK_LISTED;
                }
            }

        if (d32.GetUsbSerial( m_strFilePath.GetAt( 0 ) )) {
            uID = d32.SerialNumberToID( );
            }

        if (0 != uID) {
            std::wstring sid = StrTools::UInt64ToHexStringW( uID );

            if (-1 != strHList.Find( sid.c_str( ) )) {
                return VE::VeOpenFileError::ERR_DEVICE_BLACK_LISTED;
                }
            }

        }
    m_progressCtrl.SetPos( 95 );
    return VE::VeOpenFileError::SUCCESS;
    }
//=================================================================
void CDlgOpenFile::CheckPlayFinish( )
    {
    GetDlgItem( IDC_STATIC_3 )->ShowWindow( SW_SHOW );
    m_progressCtrl.SetPos( 100 );
    Sleep( 100 );
    }
//=============================================================
BOOL CDlgOpenFile::PreTranslateMessage( MSG* pMsg )
    {
    if (pMsg->message == WM_KEYDOWN || pMsg->message == WM_SYSKEYDOWN) {
        if (pMsg->wParam == VK_RETURN || pMsg->wParam == VK_ESCAPE ||
            ( pMsg->wParam == VK_F4 && ( pMsg->lParam & ( 1 << 29 ) ) )) // Alt+F4
            {
            return TRUE; // 吞掉
            }
        }
    return CDialogEx::PreTranslateMessage( pMsg );
    }
//=============================================================
// 屏蔽系统菜单关闭（Alt+F4 等）
BOOL CDlgOpenFile::OnCommand( WPARAM wParam , LPARAM lParam )
    {
    if (wParam == SC_CLOSE) {
        return TRUE; // 屏蔽
        }
    return CDialogEx::OnCommand( wParam , lParam );
    }