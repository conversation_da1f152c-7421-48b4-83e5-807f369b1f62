﻿#pragma once

#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容
#define _WIN32_WINNT 0x0601   // 目标最低版本 Windows 7
#include <afxwin.h>
#include <afxext.h>
#include <afxcmn.h>
#include <Unknwn.h>
#include <UIAutomation.h>
#define  COMPANY L"jiamisoft"

#ifdef _M_X64
#define safe_closehandle(h) do {if ((h != INVALID_HANDLE_VALUE) && (h != NULL) && 0xdddddddd != (unsigned long long)h) {CloseHandle(h); h = INVALID_HANDLE_VALUE;}} while(0)
#else
#define safe_closehandle(h) do {if ((h != INVALID_HANDLE_VALUE) && (h != NULL) && 0xdddddddd != (unsigned int)h) {CloseHandle(h); h = INVALID_HANDLE_VALUE;}} while(0)
#endif

#define safe_wcslen(str) ((((wchar_t*)(str))==NULL)?0:wcslen(str))