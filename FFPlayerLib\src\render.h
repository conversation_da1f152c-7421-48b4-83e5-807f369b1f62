#pragma once
#include "definition.h"

int video_open( VideoState* is );
void video_display( VideoState* is );
void video_image_display( VideoState* is );
void video_audio_display( VideoState* s );
void video_refresh( void* opaque , double* remaining_time );
int realloc_texture( SDL_Texture** texture , Uint32 new_format , int new_width , int new_height , SDL_BlendMode blendmode , int init_texture );
void calculate_display_rect( SDL_Rect* rect ,
    int scr_xleft , int scr_ytop , int scr_width , int scr_height ,
    int pic_width , int pic_height , AVRational pic_sar );
void set_sdl_yuv_conversion_mode( AVFrame* frame );
int upload_texture( SDL_Texture** tex , AVFrame* frame );
inline void fill_rectangle( int x , int y , int w , int h );
void get_sdl_pix_fmt_and_blendmode( int format , Uint32* sdl_pix_fmt , SDL_BlendMode* sdl_blendmode );