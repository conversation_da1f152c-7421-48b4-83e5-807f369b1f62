#include "ProcessMonitor.h"
#include <TlHelp32.h>
#include <Psapi.h>
#include <algorithm>
#include <sstream>
#include <locale>
#include <codecvt>
#include <filesystem>
#include <ShlObj.h>
#include <Shlwapi.h>

#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "Shlwapi.lib")

// 静态成员初始化
std::unique_ptr<ProcessMonitor> ProcessMonitor::s_instance = nullptr;
std::once_flag ProcessMonitor::s_onceFlag;

ProcessMonitor& ProcessMonitor::GetInstance() {
    std::call_once(s_onceFlag, []() {
        s_instance.reset(new ProcessMonitor());
    });
    return *s_instance;
}

ProcessMonitor::ProcessMonitor()
    : m_isRunning(false)
    , m_monitorIntervalMs(1000) // 默认每秒监控一次
    , m_logger(DebugLogger::GetInstance()) {
}

ProcessMonitor::~ProcessMonitor() {
    Shutdown();
}

bool ProcessMonitor::Initialize(const std::wstring& appName) {
    // 获取AppData\Roaming路径
    wchar_t appDataPath[MAX_PATH] = {0};
    if (FAILED(SHGetFolderPathW(NULL, CSIDL_APPDATA, NULL, 0, appDataPath))) {
        return false;
    }

    // 创建应用程序文件夹路径
    std::wstring appFolderPath = std::wstring(appDataPath) + L"\\" + appName;
    
    // 创建日志子文件夹
    std::wstring logFolderPath = appFolderPath + L"\\Logs";
    
    try {
        // 确保目录存在
        std::filesystem::create_directories(logFolderPath);
    }
    catch (const std::exception&) {
        return false;
    }

    // 设置进程监控日志文件路径
    std::wstring logFilePath = logFolderPath + L"\\" + appName + L"_Process.log";
    
    // 初始化日志系统
    if (!m_logger.Initialize(appName)) {
        return false;
    }

    m_logger.Info(L"进程监控系统初始化");
    m_logger.Info(L"应用名称: " + appName);
    m_logger.Info(L"日志路径: " + logFilePath);
    
    return true;
}

void ProcessMonitor::Shutdown() {
    StopMonitoring();
    m_logger.Info(L"进程监控系统关闭");
}

bool ProcessMonitor::StartMonitoring() {
    if (m_isRunning) {
        return true; // 已经在运行
    }

    m_isRunning = true;
    m_monitorThread = std::thread(&ProcessMonitor::MonitorThreadFunc, this);
    
    m_logger.Info(L"进程监控已启动");
    return true;
}

void ProcessMonitor::StopMonitoring() {
    if (!m_isRunning) {
        return; // 已经停止
    }

    m_isRunning = false;
    
    if (m_monitorThread.joinable()) {
        m_monitorThread.join();
    }
    
    m_logger.Info(L"进程监控已停止");
}

void ProcessMonitor::SetProcessStartCallback(ProcessCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_startCallback = callback;
}

void ProcessMonitor::SetProcessTerminateCallback(ProcessCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_terminateCallback = callback;
}

void ProcessMonitor::SetProcessCrashCallback(ProcessCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_crashCallback = callback;
}

void ProcessMonitor::AddMonitoredProcess(const std::wstring& processName) {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    // 检查是否已存在
    if (std::find(m_monitoredProcesses.begin(), m_monitoredProcesses.end(), processName) 
        == m_monitoredProcesses.end()) {
        m_monitoredProcesses.push_back(processName);
        m_logger.Info(L"添加监控进程", processName);
    }
}

void ProcessMonitor::RemoveMonitoredProcess(const std::wstring& processName) {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    auto it = std::find(m_monitoredProcesses.begin(), m_monitoredProcesses.end(), processName);
    if (it != m_monitoredProcesses.end()) {
        m_monitoredProcesses.erase(it);
        m_logger.Info(L"移除监控进程", processName);
    }
}

void ProcessMonitor::ClearMonitoredProcesses() {
    std::lock_guard<std::mutex> lock(m_processMutex);
    m_monitoredProcesses.clear();
    m_logger.Info(L"清空监控进程列表");
}

std::vector<ProcessInfo> ProcessMonitor::GetRunningProcesses() const {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    std::vector<ProcessInfo> result;
    for (const auto& pair : m_processes) {
        if (pair.second.state == ProcessState::Running) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

ProcessInfo ProcessMonitor::GetProcessInfo(DWORD processId) const {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    auto it = m_processes.find(processId);
    if (it != m_processes.end()) {
        return it->second;
    }
    
    // 返回空信息
    ProcessInfo emptyInfo = {};
    emptyInfo.processId = 0;
    emptyInfo.state = ProcessState::Terminated;
    
    return emptyInfo;
}

bool ProcessMonitor::IsProcessRunning(DWORD processId) const {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    auto it = m_processes.find(processId);
    return (it != m_processes.end() && it->second.state == ProcessState::Running);
}

bool ProcessMonitor::IsProcessRunning(const std::wstring& processName) const {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    for (const auto& pair : m_processes) {
        if (pair.second.state == ProcessState::Running &&
            _wcsicmp(pair.second.processName.c_str(), processName.c_str()) == 0) {
            return true;
        }
    }
    
    return false;
}

bool ProcessMonitor::TerminateProcess(DWORD processId, DWORD exitCode) {
    HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, processId);
    if (hProcess == NULL) {
        m_logger.Error(L"无法打开进程进行终止", L"进程ID: " + std::to_wstring(processId));
        return false;
    }
    
    bool result = ::TerminateProcess(hProcess, exitCode) != 0;
    CloseHandle(hProcess);
    
    if (result) {
        m_logger.Info(L"成功终止进程", L"进程ID: " + std::to_wstring(processId));
    } else {
        m_logger.Error(L"终止进程失败", L"进程ID: " + std::to_wstring(processId) + L", 错误代码: " + std::to_wstring(GetLastError()));
    }
    
    return result;
}

bool ProcessMonitor::TerminateProcess(const std::wstring& processName, DWORD exitCode) {
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    bool anyTerminated = false;
    
    for (const auto& pair : m_processes) {
        if (pair.second.state == ProcessState::Running &&
            _wcsicmp(pair.second.processName.c_str(), processName.c_str()) == 0) {
            
            if (TerminateProcess(pair.second.processId, exitCode)) {
                anyTerminated = true;
            }
        }
    }
    
    return anyTerminated;
}

size_t ProcessMonitor::GetProcessMemoryUsage(DWORD processId) const {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return 0;
    }
    
    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(hProcess, (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        CloseHandle(hProcess);
        return static_cast<size_t>(pmc.WorkingSetSize);
    }
    
    CloseHandle(hProcess);
    return 0;
}

float ProcessMonitor::GetProcessCpuUsage(DWORD processId) const {
    std::lock_guard<std::mutex> lock(m_cpuUsageMutex);
    
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return 0.0f;
    }
    
    // 系统时间
    FILETIME sysIdle, sysKernel, sysUser;
    if (!GetSystemTimes(&sysIdle, &sysKernel, &sysUser)) {
        CloseHandle(hProcess);
        return 0.0f;
    }
    
    // 进程时间
    FILETIME procCreation, procExit, procKernel, procUser;
    if (!GetProcessTimes(hProcess, &procCreation, &procExit, &procKernel, &procUser)) {
        CloseHandle(hProcess);
        return 0.0f;
    }
    
    // 获取当前条目或创建新条目
    auto it = m_cpuUsageData.find(processId);
    if (it == m_cpuUsageData.end()) {
        CPUUsageData data = {};
        data.lastSysKernel = sysKernel;
        data.lastSysUser = sysUser;
        data.lastProcKernel = procKernel;
        data.lastProcUser = procUser;
        data.lastTime = GetTickCount();
        data.usage = 0.0f;
        
        m_cpuUsageData[processId] = data;
        CloseHandle(hProcess);
        return 0.0f;
    }
    
    CPUUsageData& data = it->second;
    
    // 计算系统时间差
    ULARGE_INTEGER sysKernelDiff, sysUserDiff;
    sysKernelDiff.LowPart = sysKernel.dwLowDateTime;
    sysKernelDiff.HighPart = sysKernel.dwHighDateTime;
    sysUserDiff.LowPart = sysUser.dwLowDateTime;
    sysUserDiff.HighPart = sysUser.dwHighDateTime;
    
    ULARGE_INTEGER lastSysKernel, lastSysUser;
    lastSysKernel.LowPart = data.lastSysKernel.dwLowDateTime;
    lastSysKernel.HighPart = data.lastSysKernel.dwHighDateTime;
    lastSysUser.LowPart = data.lastSysUser.dwLowDateTime;
    lastSysUser.HighPart = data.lastSysUser.dwHighDateTime;
    
    ULONGLONG sysTimeDiff = (sysKernelDiff.QuadPart - lastSysKernel.QuadPart) +
                           (sysUserDiff.QuadPart - lastSysUser.QuadPart);
    
    // 计算进程时间差
    ULARGE_INTEGER procKernelDiff, procUserDiff;
    procKernelDiff.LowPart = procKernel.dwLowDateTime;
    procKernelDiff.HighPart = procKernel.dwHighDateTime;
    procUserDiff.LowPart = procUser.dwLowDateTime;
    procUserDiff.HighPart = procUser.dwHighDateTime;
    
    ULARGE_INTEGER lastProcKernel, lastProcUser;
    lastProcKernel.LowPart = data.lastProcKernel.dwLowDateTime;
    lastProcKernel.HighPart = data.lastProcKernel.dwHighDateTime;
    lastProcUser.LowPart = data.lastProcUser.dwLowDateTime;
    lastProcUser.HighPart = data.lastProcUser.dwHighDateTime;
    
    ULONGLONG procTimeDiff = (procKernelDiff.QuadPart - lastProcKernel.QuadPart) +
                            (procUserDiff.QuadPart - lastProcUser.QuadPart);
    
    // 更新最后的时间
    data.lastSysKernel = sysKernel;
    data.lastSysUser = sysUser;
    data.lastProcKernel = procKernel;
    data.lastProcUser = procUser;
    data.lastTime = GetTickCount();
    
    if (sysTimeDiff > 0) {
        static DWORD cpuCount = [](){ SYSTEM_INFO si{}; GetSystemInfo(&si); return si.dwNumberOfProcessors == 0 ? 1 : si.dwNumberOfProcessors; }();
        data.usage = static_cast<float>(procTimeDiff * 100.0 / sysTimeDiff);
        data.usage /= cpuCount; // 多核校正
        if (data.usage > 100.0f) data.usage = 100.0f;
    }
    
    CloseHandle(hProcess);
    return data.usage;
}

void ProcessMonitor::MonitorThreadFunc() {
    while (m_isRunning) {
        UpdateProcessList();
        std::this_thread::sleep_for(std::chrono::milliseconds(m_monitorIntervalMs));
    }
}

void ProcessMonitor::HandleProcessStart(const ProcessInfo& processInfo) {
    m_logger.Info(L"进程启动", L"进程ID: " + std::to_wstring(processInfo.processId) + 
                             L", 进程名: " + processInfo.processName);
    
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    if (m_startCallback) {
        m_startCallback(processInfo);
    }
}

void ProcessMonitor::HandleProcessTerminate(const ProcessInfo& processInfo) {
    std::wstringstream ss;
    ss << L"进程ID: " << processInfo.processId
       << L", 进程名: " << processInfo.processName
       << L", 退出代码: 0x" << std::hex << processInfo.exitCode;
    
    m_logger.Info(L"进程终止", ss.str());
    
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    if (m_terminateCallback) {
        m_terminateCallback(processInfo);
    }
}

void ProcessMonitor::HandleProcessCrash(const ProcessInfo& processInfo) {
    std::wstringstream ss;
    ss << L"进程ID: " << processInfo.processId
       << L", 进程名: " << processInfo.processName
       << L", 异常代码: 0x" << std::hex << processInfo.exitCode;
    
    m_logger.Error(L"进程崩溃", ss.str());
    
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    if (m_crashCallback) {
        m_crashCallback(processInfo);
    }
}

bool ProcessMonitor::IsAbnormalTermination(DWORD exitCode) const {
    // 常见的异常退出代码
    static const DWORD abnormalExitCodes[] = {
        STATUS_ACCESS_VIOLATION,           // 0xC0000005
        STATUS_ARRAY_BOUNDS_EXCEEDED,      // 0xC000008C
        STATUS_BREAKPOINT,                 // 0x80000003
        STATUS_DATATYPE_MISALIGNMENT,      // 0x80000002
        STATUS_FLOAT_DENORMAL_OPERAND,     // 0xC000008D
        STATUS_FLOAT_DIVIDE_BY_ZERO,       // 0xC000008E
        STATUS_FLOAT_INEXACT_RESULT,       // 0xC000008F
        STATUS_FLOAT_INVALID_OPERATION,    // 0xC0000090
        STATUS_FLOAT_OVERFLOW,             // 0xC0000091
        STATUS_FLOAT_STACK_CHECK,          // 0xC0000092
        STATUS_FLOAT_UNDERFLOW,            // 0xC0000093
        STATUS_ILLEGAL_INSTRUCTION,        // 0xC000001D
        STATUS_PRIVILEGED_INSTRUCTION,     // 0xC0000096
        STATUS_IN_PAGE_ERROR,              // 0xC0000006
        STATUS_INTEGER_DIVIDE_BY_ZERO,     // 0xC0000094
        STATUS_INTEGER_OVERFLOW,           // 0xC0000095
        STATUS_STACK_OVERFLOW,             // 0xC00000FD
        0xC0000409,                        // STATUS_STACK_BUFFER_OVERRUN
        0xC0000374                         // STATUS_HEAP_CORRUPTION
    };
    
    for (DWORD code : abnormalExitCodes) {
        if (exitCode == code) {
            return true;
        }
    }
    
    return false;
}

std::wstring ProcessMonitor::GetProcessCommandLine(HANDLE processHandle) const {
    // 获取进程命令行需要特殊处理，这里简化实现
    return L"";
}

void ProcessMonitor::UpdateProcessList() {
    // 创建进程快照
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        m_logger.Error(L"创建进程快照失败", L"错误代码: " + std::to_wstring(GetLastError()));
        return;
    }
    
    // 遍历进程
    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    // 获取第一个进程
    if (!Process32FirstW(hSnapshot, &pe32)) {
        CloseHandle(hSnapshot);
        m_logger.Error(L"获取第一个进程失败", L"错误代码: " + std::to_wstring(GetLastError()));
        return;
    }
    
    // 当前活动进程ID集合
    std::set<DWORD> activeProcessIds;
    
    // 处理所有进程
    do {
        DWORD processId = pe32.th32ProcessID;
        activeProcessIds.insert(processId);
        
        // 跳过系统进程
        if (processId == 0 || processId == 4) {
            continue;
        }
        
        std::lock_guard<std::mutex> lock(m_processMutex);
        
        // 检查是否需要监控该进程
        bool shouldMonitor = m_monitoredProcesses.empty(); // 如果列表为空，监控所有进程
        
        if (!shouldMonitor) {
            for (const auto& pattern : m_monitoredProcesses) {
                if (MatchProcessName(pe32.szExeFile, pattern)) {
                    shouldMonitor = true;
                    break;
                }
            }
        }
        
        if (!shouldMonitor) {
            continue;
        }
        
        // 检查进程是否已在列表中
        auto it = m_processes.find(processId);
        
        if (it == m_processes.end()) {
            // 新进程
            ProcessInfo processInfo;
            processInfo.processId = processId;
            processInfo.processName = pe32.szExeFile;
            processInfo.parentProcessId = pe32.th32ParentProcessID;
            processInfo.processHandle = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
            processInfo.startTime = GetTickCount();
            processInfo.exitCode = 0;
            processInfo.state = ProcessState::Started;
            
            if (processInfo.processHandle != NULL) {
                processInfo.processPath = GetProcessPath(processInfo.processHandle);
                // 获取会话ID
                if (!ProcessIdToSessionId(processId, &processInfo.sessionId)) {
                    processInfo.sessionId = 0;
                }
            }
            
            // 保存信息前关闭句柄，避免长时间占用系统句柄
            if (processInfo.processHandle != NULL) {
                CloseHandle(processInfo.processHandle);
                processInfo.processHandle = NULL;
            }

            m_processes[processId] = processInfo;

            // 处理进程启动事件
            HandleProcessStart(processInfo);

            // 更新状态为运行中
            m_processes[processId].state = ProcessState::Running;
        } else {
            // 更新现有进程信息
            ProcessInfo& processInfo = it->second;
            
            // 如果进程之前已终止，但又出现在快照中，说明是一个新进程（可能PID被重用）
            if (processInfo.state == ProcessState::Terminated || processInfo.state == ProcessState::Crashed) {
                // 关闭旧句柄
                if (processInfo.processHandle != NULL) {
                    CloseHandle(processInfo.processHandle);
                }
                
                // 重新初始化进程信息
                processInfo.processName = pe32.szExeFile;
                processInfo.parentProcessId = pe32.th32ParentProcessID;
                processInfo.processHandle = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
                processInfo.startTime = GetTickCount();
                processInfo.exitCode = 0;
                processInfo.state = ProcessState::Started;
                
                if (processInfo.processHandle != NULL) {
                    processInfo.processPath = GetProcessPath(processInfo.processHandle);
                    // 获取会话ID
                    if (!ProcessIdToSessionId(processId, &processInfo.sessionId)) {
                        processInfo.sessionId = 0;
                    }
                }
                
                // 保存信息前关闭句柄，避免长时间占用系统句柄
                if (processInfo.processHandle != NULL) {
                    CloseHandle(processInfo.processHandle);
                    processInfo.processHandle = NULL;
                }

                m_processes[processId] = processInfo;

                // 处理进程启动事件
                HandleProcessStart(processInfo);

                // 更新状态为运行中
                m_processes[processId].state = ProcessState::Running;
            }
        }
    } while (Process32NextW(hSnapshot, &pe32));
    
    CloseHandle(hSnapshot);
    
    // 查找已终止的进程
    std::lock_guard<std::mutex> lock(m_processMutex);
    
    for (auto it = m_processes.begin(); it != m_processes.end(); ++it) {
        DWORD processId = it->first;
        ProcessInfo& processInfo = it->second;
        
        // 如果进程不在活动列表中且状态为运行中，则标记为已终止
        if (activeProcessIds.find(processId) == activeProcessIds.end() && 
            (processInfo.state == ProcessState::Running || processInfo.state == ProcessState::Started)) {
            
            DWORD exitCode = 0;
            bool isCrash = false;
            
            HANDLE hTemp = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
            if (hTemp != NULL) {
                if (GetExitCodeProcess(hTemp, &exitCode) && exitCode != STILL_ACTIVE) {
                    processInfo.exitCode = exitCode;
                    isCrash = IsAbnormalTermination(exitCode);
                }
                CloseHandle(hTemp);
            }
            
            // 更新状态
            processInfo.state = isCrash ? ProcessState::Crashed : ProcessState::Terminated;
            
            // 处理相应事件
            if (isCrash) {
                HandleProcessCrash(processInfo);
            } else {
                HandleProcessTerminate(processInfo);
            }
        }
    }
}

bool ProcessMonitor::MatchProcessName(const std::wstring& processName, const std::wstring& pattern) const {
    // 使用 Windows API PathMatchSpecW 支持通配符 ("*", "?")，并不区分大小写
    return PathMatchSpecW(processName.c_str(), pattern.c_str()) == TRUE;
}

std::wstring ProcessMonitor::GetProcessPath(HANDLE processHandle) const {
    if (processHandle == NULL) {
        return L"";
    }
    
    wchar_t path[MAX_PATH] = {0};
    DWORD size = MAX_PATH;
    
    if (QueryFullProcessImageNameW(processHandle, 0, path, &size)) {
        return path;
    }
    
    return L"";
} 