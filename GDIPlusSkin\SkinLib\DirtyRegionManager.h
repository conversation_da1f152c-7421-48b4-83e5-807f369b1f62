#pragma once
#include <set>

// 脏区域管理器，避免不必要的重绘
class CDirtyRegionManager
{
public:
    CDirtyRegionManager() : m_bFullRedraw(true) {}

    // 标记区域为脏
    void InvalidateRect(const CRect& rect) {
        if (m_bFullRedraw) return;
        
        // 合并重叠区域
        bool merged = false;
        for (auto it = m_dirtyRects.begin(); it != m_dirtyRects.end(); ) {
            CRect intersect;
            if (intersect.IntersectRect(&*it, &rect)) {
                CRect unionRect;
                unionRect.UnionRect(&*it, &rect);
                it = m_dirtyRects.erase(it);
                m_dirtyRects.insert(unionRect);
                merged = true;
                break;
            } else {
                ++it;
            }
        }
        
        if (!merged) {
            m_dirtyRects.insert(rect);
        }
        
        // 如果脏区域太多，标记为全部重绘
        if (m_dirtyRects.size() > 10) {
            m_bFullRedraw = true;
            m_dirtyRects.clear();
        }
    }

    // 检查区域是否需要重绘
    bool NeedsRedraw(const CRect& rect) const {
        if (m_bFullRedraw) return true;
        
        for (const auto& dirtyRect : m_dirtyRects) {
            CRect intersect;
            if (intersect.IntersectRect(&dirtyRect, &rect)) {
                return true;
            }
        }
        return false;
    }

    // 清除脏标记
    void ClearDirty() {
        m_bFullRedraw = false;
        m_dirtyRects.clear();
    }

    // 强制全部重绘
    void InvalidateAll() {
        m_bFullRedraw = true;
        m_dirtyRects.clear();
    }

    bool IsFullRedraw() const { return m_bFullRedraw; }

private:
    bool m_bFullRedraw;
    std::set<CRect> m_dirtyRects;
};
