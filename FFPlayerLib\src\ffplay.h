﻿#pragma once
#include "definition.h"
#include <windows.h>


int FF_Open( const wchar_t* wFilePath);
void FF_SetRenderWindow( HWND hwnd );
void FF_Pause( int pause );
void FF_Stop( );
void FF_Seek( double pos_sec );
/* 当前文件总时长（秒），若未知返回 -1 */
double FF_GetDuration( );
/* 当前播放时间（秒），用 master clock 计算 */
double FF_GetCurrentTime( );
/* 音量控制：percent 取值 0~100 */
void FF_SetVolume( int percent );
int FF_GetVolume( );
/* 全屏切换：on=1 进入全屏；on=0 退出全屏 */
void FF_SetFullScreen( int on );

/* ================= 新增统一错误码 ================= */
typedef enum {
    FFERR_OK          = 0,
    FFERR_FAIL        = -1,
    FFERR_NOFILE      = -2,
    FFERR_UNSUPPORT   = -3,
    FFERR_RANGE       = -4
} FFErrorCode;

const char* FF_StrError( int err );

/* ================= 事件回调 ================= */
typedef enum {
    FF_EVT_OPEN_OK = 0,
    FF_EVT_OPEN_FAIL,
    FF_EVT_PLAY_END,
    FF_EVT_ERROR,
    FF_EVT_BUFFERING,      /* p1 = 0~100 */
    FF_EVT_SEEK_DONE,
    FF_EVT_TRACK_CHANGED,  /* p1 = type, p2 = index */
    FF_EVT_VIDEO_SIZE      /* p1 = w, p2 = h */
} FFEventID;

typedef void ( *FFEventCB )( FFEventID id , int64_t p1 , int64_t p2 , void* userdata );
void FF_SetEventCallback( FFEventCB cb , void* userdata );

/* ================= 扩展控制接口 ================= */
int FF_Close( void );                                 /* 关闭当前流，但保留 SDL/FFmpeg 环境 */
int FF_SeekRelative( double delta_sec );              /* 相对 Seek */
int FF_SetMute( int on );
int FF_GetMute( void );
int FF_SetPlaybackRate( double rate );                /* 0.5 ~ 2.0，当前返回 UNSUPPORT */
double FF_GetPlaybackRate( void );
int FF_CaptureBitmapPNG( const wchar_t* savePath );   /* 截图为 PNG，目前返回 UNSUPPORT */

/* 轨道与字幕（Stub 实现） */
typedef enum { FF_TRACK_AUDIO , FF_TRACK_VIDEO , FF_TRACK_SUBTITLE } FFTrackType;
int FF_GetTrackCount( FFTrackType type );
int FF_SelectTrack( FFTrackType type , int index );
int FF_GetCurrentTrack( FFTrackType type );
int FF_LoadSubtitle( const wchar_t* subtitlePath );

/* ================= 水印文字 ================= */
/*
 * text     : UTF-8 / 本地编码均可，nullptr 或空串关闭水印
 * position : 0 = 左上角，1 = 右下角
 * color    : 颜色名称("white"、"red" 或 "#RRGGBB"等)，nullptr = white
 * alpha    : 0.0~1.0 透明度 1.0 = 不透明
 */
int FF_SetWatermarkText( const char* text , int position , const char* color , double alpha );
int FF_SetWatermarkTextW( const wchar_t* text , int position , const char* color , double alpha );
/* 新增 fontsize 参数，<=0 使用内部默认 24 */
int FF_SetWatermarkTextEx( const char* text , int position , const char* color , double alpha , const char* fontfile , int fontsize );
int FF_SetWatermarkTextExW( const wchar_t* text , int position , const char* color , double alpha , const wchar_t* fontfile , int fontsize );

/* 设置自动停止播放的时间点（秒），值 <0 取消。*/
int FF_SetStopPosition( double seconds );