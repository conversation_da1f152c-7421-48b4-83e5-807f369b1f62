﻿#include "pch.h"
#include "VideoAES.h"

CVideoAES::CVideoAES() : m_bInitialized(false)
{
    m_tempBuffer.reserve(1024); // 预分配临时缓冲区
}

CVideoAES::~CVideoAES()
{
    m_pAESEncryption.reset();
}

bool CVideoAES::Initialize(const std::string& password)
{
    if (password.empty())
        return false;

    try
    {
        m_pAESEncryption = std::make_unique<CAESEncryption>(password);
        m_bInitialized = true;
        return true;
    }
    catch (...)
    {
        m_bInitialized = false;
        return false;
    }
}

bool CVideoAES::EncryptPacket(uint8_t* data, size_t size)
{
    if (!m_bInitialized || !data || size == 0)
        return false;

    return EncryptDataBlock(data, size);
}

bool CVideoAES::DecryptPacket(uint8_t* data, size_t size)
{
    if (!m_bInitialized || !data || size == 0)
        return false;

    return DecryptDataBlock(data, size);
}

bool CVideoAES::EncryptPacketPartial(uint8_t* data, size_t size, size_t start_pos)
{
    if (!m_bInitialized || !data || size == 0)
        return false;

    return EncryptDataBlock(data, size, start_pos);
}

bool CVideoAES::DecryptPacketPartial(uint8_t* data, size_t size, size_t start_pos)
{
    if (!m_bInitialized || !data || size == 0 )
        return false;

    return DecryptDataBlock(data, size, start_pos);
}

bool CVideoAES::EncryptPacketPartialWithLength(uint8_t* data, size_t size, size_t start_pos, size_t max_length)
{
    if (!m_bInitialized || !data || size == 0 )
        return false;

    return EncryptDataBlock(data, size, start_pos, max_length);
}

bool CVideoAES::DecryptPacketPartialWithLength(uint8_t* data, size_t size, size_t start_pos, size_t max_length)
{
    if (!m_bInitialized || !data || size == 0 )
        return false;

    return DecryptDataBlock(data, size, start_pos, max_length);
}

bool CVideoAES::EncryptDataBlock(uint8_t* data, size_t size, size_t start_pos, size_t max_length)
{
    if (!m_pAESEncryption || !data || size == 0)
        return false;
    
    // 如果起始位置超出数据包范围，跳过加密（正常情况，如小包无法在指定位置加密）
    if (start_pos >= size)
        return true;

    try
    {
        // 计算可用的数据长度
        size_t available_size = size - start_pos;
        uint8_t* encrypt_data = data + start_pos;
        
        // 确定实际要加密的长度
        size_t encrypt_length;
        if (max_length > 0) {
            // 使用指定的最大长度
            encrypt_length = (std::min)(available_size, max_length);
        } else {
            // 使用所有可用数据
            encrypt_length = available_size;
        }
        
        // CTR模式支持任意长度数据，无需对齐检查
        if (encrypt_length == 0)
            return true;

        // 使用AES加密
        size_t output_size = encrypt_length;
        bool result = m_pAESEncryption->EncryptData(encrypt_data, encrypt_length, 
                                                   encrypt_data, output_size);

        return result;
    }
    catch (...)
    {
        return false;
    }
}

bool CVideoAES::DecryptDataBlock(uint8_t* data, size_t size, size_t start_pos, size_t max_length)
{
    if (!m_pAESEncryption || !data || size == 0)
        return false;
    
    // 如果起始位置超出数据包范围，跳过解密（正常情况，如小包无法在指定位置解密）
    if (start_pos >= size)
        return true;

    try
    {
        // 计算可用的数据长度
        size_t available_size = size - start_pos;
        uint8_t* decrypt_data = data + start_pos;
        
        // 确定实际要解密的长度
        size_t decrypt_length;
        if (max_length > 0) {
            // 使用指定的最大长度
            decrypt_length = (std::min)(available_size, max_length);
        } else {
            // 使用所有可用数据
            decrypt_length = available_size;
        }
        
        // CTR模式支持任意长度数据，无需对齐检查
        if (decrypt_length == 0)
            return true;

        // 使用AES解密
        size_t output_size = decrypt_length;
        bool result = m_pAESEncryption->DecryptData(decrypt_data, decrypt_length, 
                                                   decrypt_data, output_size);

        return result;
    }
    catch (...)
    {
        return false;
    }
}

void CVideoAES::EncryptAudioPacket(uint8_t* data, size_t size)
{
    if (!data || size == 0)
        return;

    // 对音频数据使用简单的异或加密，保持实时性
    for (size_t i = 0; i < size; i++)
    {
        data[i] = data[i] ^ ((i % 256) + 1);
    }
}

void CVideoAES::DecryptAudioPacket(uint8_t* data, size_t size)
{
    if (!data || size == 0)
        return;

    // 音频解密（异或操作的逆操作就是自身）
    for (size_t i = 0; i < size; i++)
    {
        data[i] = data[i] ^ ((i % 256) + 1);
    }
}

void CVideoAES::EncryptAudioPacketPartial(uint8_t* data, size_t size, size_t start_pos)
{
    if (!data || size == 0)
        return;

    // 从指定位置开始加密音频数据
    size_t encrypt_size = (std::min)(size - start_pos, static_cast<size_t>(64)); // 只加密前64字节
    
    for (size_t i = 0; i < encrypt_size; i++)
    {
        size_t pos = start_pos + i;
        data[pos] = data[pos] ^ ((pos % 256) + 1);
    }
}

void CVideoAES::DecryptAudioPacketPartial(uint8_t* data, size_t size, size_t start_pos)
{
    if (!data || size == 0)
        return;

    // 从指定位置开始解密音频数据
    size_t decrypt_size = (std::min)(size - start_pos, static_cast<size_t>(64)); // 只解密前64字节
    
    for (size_t i = 0; i < decrypt_size; i++)
    {
        size_t pos = start_pos + i;
        data[pos] = data[pos] ^ ((pos % 256) + 1);
    }
}