#include "pch.h"
#include "progressHand.h"

namespace progressHand
    {
    void progressWord( unsigned long current , unsigned long total , LPCTSTR pszMsg , void* userdata )
        {
        CWnd *pWnd = reinterpret_cast< CWnd *>( userdata );
        if (!pWnd &&!::IsWindow(pWnd->m_hWnd ))
            return;

        ProgressData* pData = new ProgressData( );
        pData->cur = current;
        pData->tot = total;
        if (pszMsg)
            pData->text = pszMsg;

        ::PostMessage( pWnd->m_hWnd , UWM_CALCULATION_PROGRESS , reinterpret_cast< WPARAM >( pData ) , 0 );
        }
    }