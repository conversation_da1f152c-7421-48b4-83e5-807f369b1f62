﻿#pragma once

#include ".\ui\CaptchaStatic.h"
// CDlgAsk 对话框

class CDlgAsk : public CDialogEx
{
	DECLARE_DYNAMIC(CDlgAsk)

public:
	CDlgAsk(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~CDlgAsk();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_ASK };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	// 逻辑辅助
	void HandleAnswer(int nCtrlID);
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK();
	virtual void OnCancel();
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam) override;

private:
	int m_correctAnswer { 0 };   // 正确结果值
	int m_correctCtrlID { 0 };   // 显示正确答案的控件 ID
	int m_errorCount    { 0 };   // 连续错误次数

	DECLARE_MESSAGE_MAP()
public:
	CCaptchaStatic m_ask;
	virtual BOOL OnInitDialog( );
	CCaptchaStatic m_answer1;
	CCaptchaStatic m_answer2;
	CCaptchaStatic m_answer3;

// 点击事件处理函数
afx_msg void OnClickedAnswer1();
afx_msg void OnClickedAnswer2();
afx_msg void OnClickedAnswer3();
};
