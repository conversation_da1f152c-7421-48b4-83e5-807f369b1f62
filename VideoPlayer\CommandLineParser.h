#pragma once
#include <string>
#include <vector>

class CommandLineParser
{
public:
    CommandLineParser();
    ~CommandLineParser();

    // 解析命令行参数
    bool ParseCommandLine();
    
    // 获取解析结果
    bool HasFile() const { return !m_strFilePath.IsEmpty(); }
    CString GetFilePath() const { return m_strFilePath; }
    bool IsAutoPlay() const { return m_bAutoPlay; }
    bool IsSilent() const { return m_bSilent; }
    bool IsHelp() const { return m_bShowHelp; }
    
    // 显示帮助信息
    void ShowHelp() const;
    
    // 验证文件路径
    bool IsValidFile() const;

private:
    CString m_strFilePath;
    bool m_bAutoPlay;
    bool m_bSilent;
    bool m_bShowHelp;
    
    // 辅助方法
    bool ParseArguments(int argc, LPWSTR* argv);
    bool IsFileExtensionSupported(const CString& filePath) const;
    void TrimQuotes(CString& str) const;
};