#pragma once


// CMFCEditBrowseCtrlEx

class CMFCEditBrowseCtrlEx : public CMFCEditBrowseCtrl
	{
	DECLARE_DYNAMIC(CMFCEditBrowseCtrlEx)

	public:
		CMFCEditBrowseCtrlEx();
		virtual ~CMFCEditBrowseCtrlEx();

	protected:
		BOOL m_bSave;

	public:
		BOOL GetIsSave()
			{
			return m_bSave;
			}
		void SetIsSave(BOOL b)
			{
			m_bSave=b;
			}
	protected:
		DECLARE_MESSAGE_MAP()

		void OnBrowse();
	};


