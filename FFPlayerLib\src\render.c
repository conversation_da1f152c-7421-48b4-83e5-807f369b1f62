﻿#include "render.h"
#include "clock_sync.h"
#include "control.h"
#include "fame_queue.h"
#include "ffplay.h"

/* g_seek_in_progress defined in thread.c */
extern volatile int g_seek_in_progress;

const  TextureFormatEntry   render_sdl_texture_format_map [ ] = {
    { AV_PIX_FMT_RGB8,           SDL_PIXELFORMAT_RGB332 },
    { AV_PIX_FMT_RGB444,         SDL_PIXELFORMAT_RGB444 },
    { AV_PIX_FMT_RGB555,         SDL_PIXELFORMAT_RGB555 },
    { AV_PIX_FMT_BGR555,         SDL_PIXELFORMAT_BGR555 },
    { AV_PIX_FMT_RGB565,         SDL_PIXELFORMAT_RGB565 },
    { AV_PIX_FMT_BGR565,         SDL_PIXELFORMAT_BGR565 },
    { AV_PIX_FMT_RGB24,          SDL_PIXELFORMAT_RGB24 },
    { AV_PIX_FMT_BGR24,          SDL_PIXELFORMAT_BGR24 },
    { AV_PIX_FMT_0RGB32,         SDL_PIXELFORMAT_RGB888 },
    { AV_PIX_FMT_0BGR32,         SDL_PIXELFORMAT_BGR888 },
    { AV_PIX_FMT_NE( RGB0, 0BGR ), SDL_PIXELFORMAT_RGBX8888 },
    { AV_PIX_FMT_NE( BGR0, 0RGB ), SDL_PIXELFORMAT_BGRX8888 },
    { AV_PIX_FMT_RGB32,          SDL_PIXELFORMAT_ARGB8888 },
    { AV_PIX_FMT_RGB32_1,        SDL_PIXELFORMAT_RGBA8888 },
    { AV_PIX_FMT_BGR32,          SDL_PIXELFORMAT_ABGR8888 },
    { AV_PIX_FMT_BGR32_1,        SDL_PIXELFORMAT_BGRA8888 },
    { AV_PIX_FMT_YUV420P,        SDL_PIXELFORMAT_IYUV },
    { AV_PIX_FMT_YUYV422,        SDL_PIXELFORMAT_YUY2 },
    { AV_PIX_FMT_UYVY422,        SDL_PIXELFORMAT_UYVY },
    { AV_PIX_FMT_NONE,           SDL_PIXELFORMAT_UNKNOWN },
    };

int video_open( VideoState* is )
    {
    int w , h;

    w = screen_width ? screen_width : default_width;
    h = screen_height ? screen_height : default_height;

    if (!window_title)
        window_title = input_filename;
    SDL_SetWindowTitle( window , window_title );

    SDL_SetWindowSize( window , w , h );
    SDL_SetWindowPosition( window , screen_left , screen_top );
    if (is_full_screen)
        SDL_SetWindowFullscreen( window , SDL_WINDOW_FULLSCREEN_DESKTOP );
    SDL_ShowWindow( window );

    is->width = w;
    is->height = h;

    return 0;
    }

/* display the current picture, if any */
void video_display( VideoState* is )
    {
    if (!is->width)
        video_open( is );

    SDL_SetRenderDrawColor( renderer , 0 , 0 , 0 , 255 );
    SDL_RenderClear( renderer );
    if (is->audio_st && is->show_mode != SHOW_MODE_VIDEO)
        video_audio_display( is );
    else if (is->video_st)
        video_image_display( is );
    SDL_RenderPresent( renderer );
    }

void video_image_display( VideoState* is )
    {
    Frame* vp;
    Frame* sp = NULL;
    SDL_Rect rect;

    vp = frame_queue_peek_last( &is->pictq );
    if (vk_renderer) {
        vk_renderer_display( vk_renderer , vp->frame );
        return;
        }

    if (is->subtitle_st) {
        if (frame_queue_nb_remaining( &is->subpq ) > 0) {
            sp = frame_queue_peek( &is->subpq );

            if (vp->pts >= sp->pts + ( ( float ) sp->sub.start_display_time / 1000 )) {
                if (!sp->uploaded) {
                    uint8_t* pixels [ 4 ];
                    int pitch [ 4 ];
                    int i;
                    if (!sp->width || !sp->height) {
                        sp->width = vp->width;
                        sp->height = vp->height;
                        }
                    if (realloc_texture( &is->sub_texture , SDL_PIXELFORMAT_ARGB8888 , sp->width , sp->height , SDL_BLENDMODE_BLEND , 1 ) < 0)
                        return;

                    for (i = 0; i < sp->sub.num_rects; i++) {
                        AVSubtitleRect* sub_rect = sp->sub.rects [ i ];

                        sub_rect->x = av_clip( sub_rect->x , 0 , sp->width );
                        sub_rect->y = av_clip( sub_rect->y , 0 , sp->height );
                        sub_rect->w = av_clip( sub_rect->w , 0 , sp->width - sub_rect->x );
                        sub_rect->h = av_clip( sub_rect->h , 0 , sp->height - sub_rect->y );

                        is->sub_convert_ctx = sws_getCachedContext( is->sub_convert_ctx ,
                            sub_rect->w , sub_rect->h , AV_PIX_FMT_PAL8 ,
                            sub_rect->w , sub_rect->h , AV_PIX_FMT_BGRA ,
                            0 , NULL , NULL , NULL );
                        if (!is->sub_convert_ctx) {
                            av_log( NULL , AV_LOG_FATAL , "Cannot initialize the conversion context\n" );
                            return;
                            }
                        if (!SDL_LockTexture( is->sub_texture , ( SDL_Rect* ) sub_rect , ( void** ) pixels , pitch )) {
                            sws_scale( is->sub_convert_ctx , ( const uint8_t* const* ) sub_rect->data , sub_rect->linesize ,
                                0 , sub_rect->h , pixels , pitch );
                            SDL_UnlockTexture( is->sub_texture );
                            }
                        }
                    sp->uploaded = 1;
                    }
                }
            else
                sp = NULL;
            }
        }

    calculate_display_rect( &rect , is->xleft , is->ytop , is->width , is->height , vp->width , vp->height , vp->sar );
    set_sdl_yuv_conversion_mode( vp->frame );

    if (!vp->uploaded) {
        if (upload_texture( &is->vid_texture , vp->frame ) < 0) {
            set_sdl_yuv_conversion_mode( NULL );
            return;
            }
        vp->uploaded = 1;
        vp->flip_v = vp->frame->linesize [ 0 ] < 0;
        }

    SDL_RenderCopyEx( renderer , is->vid_texture , NULL , &rect , 0 , NULL , vp->flip_v ? SDL_FLIP_VERTICAL : 0 );
    set_sdl_yuv_conversion_mode( NULL );
    if (sp) {
#if USE_ONEPASS_SUBTITLE_RENDER
        SDL_RenderCopy( renderer , is->sub_texture , NULL , &rect );
#else
        int i;
        double xratio = ( double ) rect.w / ( double ) sp->width;
        double yratio = ( double ) rect.h / ( double ) sp->height;
        for (i = 0; i < sp->sub.num_rects; i++) {
            SDL_Rect* sub_rect = ( SDL_Rect* ) sp->sub.rects [ i ];
            SDL_Rect target = { .x = rect.x + sub_rect->x * xratio,
                               .y = rect.y + sub_rect->y * yratio,
                               .w = sub_rect->w * xratio,
                               .h = sub_rect->h * yratio };
            SDL_RenderCopy( renderer , is->sub_texture , sub_rect , &target );
            }
#endif
        }
    }

void video_audio_display( VideoState* s )
    {
    int i , i_start , x , y1 , y , ys , delay , n , nb_display_channels;
    int ch , channels , h , h2;
    int64_t time_diff;
    int rdft_bits , nb_freq;

    for (rdft_bits = 1; ( 1 << rdft_bits ) < 2 * s->height; rdft_bits++)
        ;
    nb_freq = 1 << ( rdft_bits - 1 );

    /* compute display index : center on currently output samples */
    channels = s->audio_tgt.ch_layout.nb_channels;
    nb_display_channels = channels;
    if (!s->paused) {
        int data_used = s->show_mode == SHOW_MODE_WAVES ? s->width : ( 2 * nb_freq );
        n = 2 * channels;
        delay = s->audio_write_buf_size;
        delay /= n;

        /* to be more precise, we take into account the time spent since
           the last buffer computation */
        if (audio_callback_time) {
            time_diff = av_gettime_relative( ) - audio_callback_time;
            delay -= ( time_diff * s->audio_tgt.freq ) / 1000000;
            }

        delay += 2 * data_used;
        if (delay < data_used)
            delay = data_used;

        i_start = x = compute_mod( s->sample_array_index - delay * channels , SAMPLE_ARRAY_SIZE );
        if (s->show_mode == SHOW_MODE_WAVES) {
            h = INT_MIN;
            for (i = 0; i < 1000; i += channels) {
                int idx = ( SAMPLE_ARRAY_SIZE + x - i ) % SAMPLE_ARRAY_SIZE;
                int a = s->sample_array [ idx ];
                int b = s->sample_array [ ( idx + 4 * channels ) % SAMPLE_ARRAY_SIZE ];
                int c = s->sample_array [ ( idx + 5 * channels ) % SAMPLE_ARRAY_SIZE ];
                int d = s->sample_array [ ( idx + 9 * channels ) % SAMPLE_ARRAY_SIZE ];
                int score = a - d;
                if (h < score && ( b ^ c ) < 0) {
                    h = score;
                    i_start = idx;
                    }
                }
            }

        s->last_i_start = i_start;
        }
    else {
        i_start = s->last_i_start;
        }

    if (s->show_mode == SHOW_MODE_WAVES) {
        SDL_SetRenderDrawColor( renderer , 255 , 255 , 255 , 255 );

        /* total height for one channel */
        h = s->height / nb_display_channels;
        /* graph height / 2 */
        h2 = ( h * 9 ) / 20;
        for (ch = 0; ch < nb_display_channels; ch++) {
            i = i_start + ch;
            y1 = s->ytop + ch * h + ( h / 2 ); /* position of center line */
            for (x = 0; x < s->width; x++) {
                y = ( s->sample_array [ i ] * h2 ) >> 15;
                if (y < 0) {
                    y = -y;
                    ys = y1 - y;
                    }
                else {
                    ys = y1;
                    }
                fill_rectangle( s->xleft + x , ys , 1 , y );
                i += channels;
                if (i >= SAMPLE_ARRAY_SIZE)
                    i -= SAMPLE_ARRAY_SIZE;
                }
            }

        SDL_SetRenderDrawColor( renderer , 0 , 0 , 255 , 255 );

        for (ch = 1; ch < nb_display_channels; ch++) {
            y = s->ytop + ch * h;
            fill_rectangle( s->xleft , y , s->width , 1 );
            }
        }
    else {
        int err = 0;
        if (realloc_texture( &s->vis_texture , SDL_PIXELFORMAT_ARGB8888 , s->width , s->height , SDL_BLENDMODE_NONE , 1 ) < 0)
            return;

        if (s->xpos >= s->width)
            s->xpos = 0;
        nb_display_channels = FFMIN( nb_display_channels , 2 );
        if (rdft_bits != s->rdft_bits) {
            const float rdft_scale = 1.0;
            av_tx_uninit( &s->rdft );
            av_freep( &s->real_data );
            av_freep( &s->rdft_data );
            s->rdft_bits = rdft_bits;
            s->real_data = av_malloc_array( nb_freq , 4 * sizeof( *s->real_data ) );
            s->rdft_data = av_malloc_array( nb_freq + 1 , 2 * sizeof( *s->rdft_data ) );
            err = av_tx_init( &s->rdft , &s->rdft_fn , AV_TX_FLOAT_RDFT ,
                0 , 1 << rdft_bits , &rdft_scale , 0 );
            }
        if (err < 0 || !s->rdft_data) {
            av_log( NULL , AV_LOG_ERROR , "Failed to allocate buffers for RDFT, switching to waves display\n" );
            s->show_mode = SHOW_MODE_WAVES;
            }
        else {
            float* data_in [ 2 ];
            AVComplexFloat* data [ 2 ];
            SDL_Rect rect = { .x = s->xpos, .y = 0, .w = 1, .h = s->height };
            uint32_t* pixels;
            int pitch;
            for (ch = 0; ch < nb_display_channels; ch++) {
                data_in [ ch ] = s->real_data + 2 * nb_freq * ch;
                data [ ch ] = s->rdft_data + nb_freq * ch;
                i = i_start + ch;
                for (x = 0; x < 2 * nb_freq; x++) {
                    double w = ( x - nb_freq ) * ( 1.0 / nb_freq );
                    data_in [ ch ][ x ] = s->sample_array [ i ] * ( 1.0 - w * w );
                    i += channels;
                    if (i >= SAMPLE_ARRAY_SIZE)
                        i -= SAMPLE_ARRAY_SIZE;
                    }
                s->rdft_fn( s->rdft , data [ ch ] , data_in [ ch ] , sizeof( float ) );
                data [ ch ][ 0 ].im = data [ ch ][ nb_freq ].re;
                data [ ch ][ nb_freq ].re = 0;
                }
            /* Least efficient way to do this, we should of course
             * directly access it but it is more than fast enough. */
            if (!SDL_LockTexture( s->vis_texture , &rect , ( void** ) &pixels , &pitch )) {
                pitch >>= 2;
                pixels += pitch * s->height;
                for (y = 0; y < s->height; y++) {
                    double w = 1 / sqrt( nb_freq );
                    int a = sqrt( w * sqrt( data [ 0 ][ y ].re * data [ 0 ][ y ].re + data [ 0 ][ y ].im * data [ 0 ][ y ].im ) );
                    int b = ( nb_display_channels == 2 ) ? sqrt( w * hypot( data [ 1 ][ y ].re , data [ 1 ][ y ].im ) )
                        : a;
                    a = FFMIN( a , 255 );
                    b = FFMIN( b , 255 );
                    pixels -= pitch;
                    *pixels = ( a << 16 ) + ( b << 8 ) + ( ( a + b ) >> 1 );
                    }
                SDL_UnlockTexture( s->vis_texture );
                }
            SDL_RenderCopy( renderer , s->vis_texture , NULL , NULL );
            }
        if (!s->paused)
            s->xpos++;
        }
    }

/* called to display each frame */
void video_refresh( void* opaque , double* remaining_time )
    {
    VideoState* is = opaque;
    double time;

    Frame* sp , * sp2;

    if (!is->paused && get_master_sync_type( is ) == AV_SYNC_EXTERNAL_CLOCK && is->realtime)
        check_external_clock_speed( is );

    /* 检测是否到达用户设置的停止时间点 */
    extern double g_stop_pos_sec; /* 定义于 ffplay.c */
    extern int g_stop_reason;
    if (g_stop_pos_sec >= 0) {
        double cur = get_master_clock( is );
        if (!isnan( cur ) && cur >= g_stop_pos_sec) {
            g_stop_reason = 1; /* reach scheduled stop */
            FF_Stop();
            g_stop_pos_sec = -1.0; // 仅触发一次
            return; /* 停止后不再刷新 */
        }
    }

    if (!display_disable && is->show_mode != SHOW_MODE_VIDEO && is->audio_st) {
        time = av_gettime_relative( ) / 1000000.0;
        if (is->force_refresh || is->last_vis_time + rdftspeed < time) {
            video_display( is );
            is->last_vis_time = time;
            }
        *remaining_time = FFMIN( *remaining_time , is->last_vis_time + rdftspeed - time );
        }

    if (is->video_st) {
    retry:
        if (frame_queue_nb_remaining( &is->pictq ) == 0) {
            // nothing to do, no picture to display in the queue
            }
        else {
            double last_duration , duration , delay;
            Frame* vp , * lastvp;

            /* dequeue the picture */
            lastvp = frame_queue_peek_last( &is->pictq );
            vp = frame_queue_peek( &is->pictq );

            if (vp->serial != is->videoq.serial) {
                frame_queue_next( &is->pictq );
                goto retry;
                }

            if (lastvp->serial != vp->serial)
                is->frame_timer = av_gettime_relative( ) / 1000000.0;

            if (is->paused)
                goto display;

            /* compute nominal last_duration */
            last_duration = vp_duration( is , lastvp , vp );
            delay = compute_target_delay( last_duration , is );

            time = av_gettime_relative( ) / 1000000.0;
            if (time < is->frame_timer + delay) {
                *remaining_time = FFMIN( is->frame_timer + delay - time , *remaining_time );
                goto display;
                }

            is->frame_timer += delay;
            if (delay > 0 && time - is->frame_timer > AV_SYNC_THRESHOLD_MAX)
                is->frame_timer = time;

            SDL_LockMutex( is->pictq.mutex );
            if (!isnan( vp->pts ))
                update_video_pts( is , vp->pts , vp->serial );
            SDL_UnlockMutex( is->pictq.mutex );

            if (frame_queue_nb_remaining( &is->pictq ) > 1) {
                Frame* nextvp = frame_queue_peek_next( &is->pictq );
                duration = vp_duration( is , vp , nextvp );
                if (!is->step && ( framedrop > 0 || ( framedrop && get_master_sync_type( is ) != AV_SYNC_VIDEO_MASTER ) ) && time > is->frame_timer + duration) {
                    is->frame_drops_late++;
                    frame_queue_next( &is->pictq );
                    goto retry;
                    }
                }

            if (is->subtitle_st) {
                while (frame_queue_nb_remaining( &is->subpq ) > 0) {
                    sp = frame_queue_peek( &is->subpq );

                    if (frame_queue_nb_remaining( &is->subpq ) > 1)
                        sp2 = frame_queue_peek_next( &is->subpq );
                    else
                        sp2 = NULL;

                    if (sp->serial != is->subtitleq.serial
                        || ( is->vidclk.pts > ( sp->pts + ( ( float ) sp->sub.end_display_time / 1000 ) ) )
                        || ( sp2 && is->vidclk.pts > ( sp2->pts + ( ( float ) sp2->sub.start_display_time / 1000 ) ) )) {
                        if (sp->uploaded) {
                            int i;
                            for (i = 0; i < sp->sub.num_rects; i++) {
                                AVSubtitleRect* sub_rect = sp->sub.rects [ i ];
                                uint8_t* pixels;
                                int pitch , j;

                                if (!SDL_LockTexture( is->sub_texture , ( SDL_Rect* ) sub_rect , ( void** ) &pixels , &pitch )) {
                                    for (j = 0; j < sub_rect->h; j++ , pixels += pitch)
                                        memset( pixels , 0 , sub_rect->w << 2 );
                                    SDL_UnlockTexture( is->sub_texture );
                                    }
                                }
                            }
                        frame_queue_next( &is->subpq );
                        }
                    else {
                        break;
                        }
                    }
                }

            frame_queue_next( &is->pictq );
            is->force_refresh = 1;

            if (is->step && !is->paused)
                stream_toggle_pause( is );
            }
    display:
        /* display picture */
        if (!display_disable && is->force_refresh && is->show_mode == SHOW_MODE_VIDEO && is->pictq.rindex_shown) {
            video_display( is );

            /* 第一次真实显示帧后，再触发 SEEK_DONE */
            if (g_seek_in_progress) {
                g_seek_in_progress = 0;
                FF_Internal_SeekDone();
            }
        }
        }
    is->force_refresh = 0;
    if (show_status) {
        AVBPrint buf;
        static int64_t last_time;
        int64_t cur_time;
        int aqsize , vqsize , sqsize;
        double av_diff;

        cur_time = av_gettime_relative( );
        if (!last_time || ( cur_time - last_time ) >= 30000) {
            aqsize = 0;
            vqsize = 0;
            sqsize = 0;
            if (is->audio_st)
                aqsize = is->audioq.size;
            if (is->video_st)
                vqsize = is->videoq.size;
            if (is->subtitle_st)
                sqsize = is->subtitleq.size;
            av_diff = 0;
            if (is->audio_st && is->video_st)
                av_diff = get_clock( &is->audclk ) - get_clock( &is->vidclk );
            else if (is->video_st)
                av_diff = get_master_clock( is ) - get_clock( &is->vidclk );
            else if (is->audio_st)
                av_diff = get_master_clock( is ) - get_clock( &is->audclk );

            av_bprint_init( &buf , 0 , AV_BPRINT_SIZE_AUTOMATIC );
            av_bprintf( &buf ,
                "%7.2f %s:%7.3f fd=%4d aq=%5dKB vq=%5dKB sq=%5dB \r" ,
                get_master_clock( is ) ,
                ( is->audio_st && is->video_st ) ? "A-V" : ( is->video_st ? "M-V" : ( is->audio_st ? "M-A" : "   " ) ) ,
                av_diff ,
                is->frame_drops_early + is->frame_drops_late ,
                aqsize / 1024 ,
                vqsize / 1024 ,
                sqsize );

            if (show_status == 1 && AV_LOG_INFO > av_log_get_level( ))
                 fprintf( stderr , "%s" , buf.str );
             else
                 av_log( NULL , AV_LOG_INFO , "%s" , buf.str );

            fflush( stderr );
            av_bprint_finalize( &buf , NULL );

            last_time = cur_time;
            }
        }
    }

int realloc_texture( SDL_Texture** texture , Uint32 new_format , int new_width , int new_height , SDL_BlendMode blendmode , int init_texture )
        {
        Uint32 format;
        int access , w , h;
        if (!*texture || SDL_QueryTexture( *texture , &format , &access , &w , &h ) < 0 || new_width != w || new_height != h || new_format != format) {
            void* pixels;
            int pitch;
            if (*texture)
                SDL_DestroyTexture( *texture );
            if (!( *texture = SDL_CreateTexture( renderer , new_format , SDL_TEXTUREACCESS_STREAMING , new_width , new_height ) ))
                return -1;
            if (SDL_SetTextureBlendMode( *texture , blendmode ) < 0)
                return -1;
            if (init_texture) {
                if (SDL_LockTexture( *texture , NULL , &pixels , &pitch ) < 0)
                    return -1;
                memset( pixels , 0 , pitch * new_height );
                SDL_UnlockTexture( *texture );
                }
            av_log( NULL , AV_LOG_VERBOSE , "Created %dx%d texture with %s.\n" , new_width , new_height , SDL_GetPixelFormatName( new_format ) );
            }
        return 0;
        }

void calculate_display_rect( SDL_Rect* rect ,
        int scr_xleft , int scr_ytop , int scr_width , int scr_height ,
        int pic_width , int pic_height , AVRational pic_sar )
        {
        AVRational aspect_ratio = pic_sar;
        int64_t width , height , x , y;

        if (av_cmp_q( aspect_ratio , av_make_q( 0 , 1 ) ) <= 0)
            aspect_ratio = av_make_q( 1 , 1 );

        aspect_ratio = av_mul_q( aspect_ratio , av_make_q( pic_width , pic_height ) );

        /* XXX: we suppose the screen has a 1.0 pixel ratio */
        height = scr_height;
        width = av_rescale( height , aspect_ratio.num , aspect_ratio.den ) & ~1;
        if (width > scr_width) {
            width = scr_width;
            height = av_rescale( width , aspect_ratio.den , aspect_ratio.num ) & ~1;
            }
        x = ( scr_width - width ) / 2;
        y = ( scr_height - height ) / 2;
        rect->x = scr_xleft + x;
        rect->y = scr_ytop + y;
        rect->w = FFMAX( ( int ) width , 1 );
        rect->h = FFMAX( ( int ) height , 1 );
        }
void set_sdl_yuv_conversion_mode( AVFrame* frame )
    {
#if SDL_VERSION_ATLEAST(2,0,8)
    SDL_YUV_CONVERSION_MODE mode = SDL_YUV_CONVERSION_AUTOMATIC;
    if (frame && ( frame->format == AV_PIX_FMT_YUV420P || frame->format == AV_PIX_FMT_YUYV422 || frame->format == AV_PIX_FMT_UYVY422 )) {
        if (frame->color_range == AVCOL_RANGE_JPEG)
            mode = SDL_YUV_CONVERSION_JPEG;
        else if (frame->colorspace == AVCOL_SPC_BT709)
            mode = SDL_YUV_CONVERSION_BT709;
        else if (frame->colorspace == AVCOL_SPC_BT470BG || frame->colorspace == AVCOL_SPC_SMPTE170M)
            mode = SDL_YUV_CONVERSION_BT601;
        }
    SDL_SetYUVConversionMode( mode ); /* FIXME: no support for linear transfer */
#endif
    }
int upload_texture( SDL_Texture** tex , AVFrame* frame )
    {
    int ret = 0;
    Uint32 sdl_pix_fmt;
    SDL_BlendMode sdl_blendmode;
    get_sdl_pix_fmt_and_blendmode( frame->format , &sdl_pix_fmt , &sdl_blendmode );
    if (realloc_texture( tex , sdl_pix_fmt == SDL_PIXELFORMAT_UNKNOWN ? SDL_PIXELFORMAT_ARGB8888 : sdl_pix_fmt , frame->width , frame->height , sdl_blendmode , 0 ) < 0)
        return -1;
    switch (sdl_pix_fmt) {
            case SDL_PIXELFORMAT_IYUV:
                if (frame->linesize [ 0 ] > 0 && frame->linesize [ 1 ] > 0 && frame->linesize [ 2 ] > 0) {
                    ret = SDL_UpdateYUVTexture( *tex , NULL , frame->data [ 0 ] , frame->linesize [ 0 ] ,
                        frame->data [ 1 ] , frame->linesize [ 1 ] ,
                        frame->data [ 2 ] , frame->linesize [ 2 ] );
                    }
                else if (frame->linesize [ 0 ] < 0 && frame->linesize [ 1 ] < 0 && frame->linesize [ 2 ] < 0) {
                    ret = SDL_UpdateYUVTexture( *tex , NULL , frame->data [ 0 ] + frame->linesize [ 0 ] * ( frame->height - 1 ) , -frame->linesize [ 0 ] ,
                        frame->data [ 1 ] + frame->linesize [ 1 ] * ( AV_CEIL_RSHIFT( frame->height , 1 ) - 1 ) , -frame->linesize [ 1 ] ,
                        frame->data [ 2 ] + frame->linesize [ 2 ] * ( AV_CEIL_RSHIFT( frame->height , 1 ) - 1 ) , -frame->linesize [ 2 ] );
                    }
                else {
                    av_log( NULL , AV_LOG_ERROR , "Mixed negative and positive linesizes are not supported.\n" );
                    return -1;
                    }
                break;
            default:
                if (frame->linesize [ 0 ] < 0) {
                    ret = SDL_UpdateTexture( *tex , NULL , frame->data [ 0 ] + frame->linesize [ 0 ] * ( frame->height - 1 ) , -frame->linesize [ 0 ] );
                    }
                else {
                    ret = SDL_UpdateTexture( *tex , NULL , frame->data [ 0 ] , frame->linesize [ 0 ] );
                    }
                break;
        }
    return ret;
    }
inline void fill_rectangle( int x , int y , int w , int h )
    {
    SDL_Rect rect;
    rect.x = x;
    rect.y = y;
    rect.w = w;
    rect.h = h;
    if (w && h)
        SDL_RenderFillRect( renderer , &rect );
    }

void get_sdl_pix_fmt_and_blendmode( int format , Uint32* sdl_pix_fmt , SDL_BlendMode* sdl_blendmode )
    {
    int i;
    *sdl_blendmode = SDL_BLENDMODE_NONE;
    *sdl_pix_fmt = SDL_PIXELFORMAT_UNKNOWN;
    if (format == AV_PIX_FMT_RGB32 ||
        format == AV_PIX_FMT_RGB32_1 ||
        format == AV_PIX_FMT_BGR32 ||
        format == AV_PIX_FMT_BGR32_1)
        *sdl_blendmode = SDL_BLENDMODE_BLEND;
    for (i = 0; i < FF_ARRAY_ELEMS( render_sdl_texture_format_map ) - 1; i++) {
        if (format == render_sdl_texture_format_map [ i ].format) {
            *sdl_pix_fmt = render_sdl_texture_format_map [ i ].texture_fmt;
            return;
            }
        }
    }