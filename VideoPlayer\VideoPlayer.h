﻿
// VideoPlayer.h: PROJECT_NAME 应用程序的主头文件
//

#pragma once

#ifndef __AFXWIN_H__
	#error "在包含此文件之前包含 'pch.h' 以生成 PCH"
#endif

#include "resource.h"		// 主符号
#include "CommandLineParser.h"  // 命令行解析器


// CVideoPlayerApp:
// 有关此类的实现，请参阅 VideoPlayer.cpp
//

class CVideoPlayerApp : public CWinApp
{
public:
	CVideoPlayerApp();

// 重写
public:
	virtual BOOL InitInstance();

// 实现
	DECLARE_MESSAGE_MAP()
	virtual int ExitInstance( );

// 命令行相关
public:
	CommandLineParser& GetCommandLineParser() { return m_cmdParser; }
	
private:
	CommandLineParser m_cmdParser;
	};

extern CVideoPlayerApp theApp;
