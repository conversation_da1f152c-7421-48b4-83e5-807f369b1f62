# VE视频加密解密库 - 支持格式说明

## 支持的视频格式

### 完全支持的格式
本库现已支持以下视频格式的加密和解密：

| 格式 | 扩展名 | 视频加密类型 | 音频加密类型 | 说明 |
|------|--------|-------------|-------------|------|
| MPEG | .mpeg, .mpg | 部分加密 | 简单加密 | 标准MPEG格式 |
| Flash Video | .flv | 部分加密 | 简单加密 | Flash视频格式 |
| AVI | .avi | 部分加密 | 简单加密 | 传统AVI格式 |
| MP4 | .mp4 | 关键帧加密 | 部分加密 | 现代容器格式 |
| QuickTime | .mov | 关键帧加密 | 部分加密 | Apple QuickTime格式 |
| Matroska | .mkv | 关键帧加密 | 部分加密 | 开源容器格式 |
| Windows Media | .wmv | 部分加密 | 简单加密 | Microsoft媒体格式 |
| RealMedia | .rmvb | 部分加密 | 简单加密 | RealNetworks格式 |
| DAT | .dat | 部分加密 | 部分加密 | VCD/SVCD格式 |
| 3GPP | .3gp, .3gpp | 完整加密 | 简单加密 | 移动设备视频格式，支持H.263编码 |

## 加密策略说明

### 视频加密类型
1. **关键帧加密 (VIDEO_ENCRYPT_KEYFRAME)**
   - 仅对关键帧（I帧）进行加密
   - 适用于：MP4、MOV、MKV格式
   - 优点：性能好，播放兼容性较好

2. **部分加密 (VIDEO_ENCRYPT_PARTIAL)**
   - 对每个视频包的部分数据进行加密
   - 适用于：MPEG、FLV、AVI、WMV、RMVB、DAT格式
   - 优点：安全性高，兼容性好

3. **完整加密 (VIDEO_ENCRYPT_FULL)**
   - 对整个视频数据包进行加密
   - 适用于：3GP、3GPP格式
   - 优点：最高安全性，支持H.263等特殊编码

### 音频加密类型
1. **简单加密 (AUDIO_ENCRYPT_SIMPLE)**
   - 对整个音频包进行加密
   - 适用于：MPEG、FLV、AVI、WMV、RMVB、3GP格式

2. **部分加密 (AUDIO_ENCRYPT_PARTIAL)**
   - 对音频包的部分数据进行加密
   - 适用于：MP4、MOV、MKV、DAT格式

## 关键帧检测

库支持多种视频编码格式的关键帧检测：
- **H.264**: 检测NAL单元类型5（IDR帧）
- **H.265**: 检测NAL单元类型19-20（IDR帧）
- **H.263**: 检测Picture Start Code (PSC)和PTYPE字段（适用于3GP格式）
- **MPEG-2**: 检测图像起始码和I帧标识
- **VC-1**: 检测VC-1起始码和帧类型（WMV格式）

## 特殊格式处理

### 3GP/3GPP格式
- **输出格式转换**: 解密时自动转换为MP4格式以确保最佳兼容性
- **H.263编码支持**: 完整支持H.263编码的关键帧检测和加密
- **时间基准修正**: 自动修正H.263视频的时间基准参数
- **兼容性优化**: 清除可能导致播放问题的编码标签

## 文件扩展名规则

加密后的文件扩展名规则：
- 原文件：video.mp4
- 加密后：video.vemp4
- 解密后：video.mp4 或 video_decrypted.mp4

### 3GP文件特殊处理：
- 原文件：video.3gp
- 加密后：video.ve3gp  
- 解密后：video.3gp（保持原始3GP格式）

## 使用建议

1. **高质量视频推荐**：MP4、MOV、MKV格式，使用关键帧加密
2. **兼容性优先**：AVI、WMV格式，使用部分加密
3. **移动设备视频**：3GP格式，使用完整加密，解密后转为MP4
4. **特殊格式**：RMVB建议转换为MP4后再加密
5. **性能考虑**：大文件建议使用关键帧加密模式 