﻿#pragma once
#include "definition.h"

void stream_toggle_pause( VideoState* is );
void stream_seek( VideoState* is , int64_t pos , int64_t rel , int by_bytes );
void toggle_pause( VideoState* is );
void toggle_mute( VideoState* is );
void toggle_full_screen( VideoState* is );
void toggle_audio_display( VideoState* is );
void seek_chapter( VideoState* is , int incr );
void event_loop( VideoState* cur_stream );
void refresh_loop_wait_event( VideoState* is , SDL_Event* event );
void do_exit( VideoState* is );
void update_volume( VideoState* is , int sign , double step );
void step_to_next_frame( VideoState* is );
void stream_cycle_channel( VideoState* is , int codec_type );