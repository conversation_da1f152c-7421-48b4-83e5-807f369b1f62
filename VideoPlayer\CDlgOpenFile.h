﻿#pragma once

#include "Empower.h"
#include "..\EP-common\VeErrorCodes.h"
// CDlgOpenFile 对话框

class CDlgOpenFile : public CDialogEx
{
	DECLARE_DYNAMIC(CDlgOpenFile)

public:
	CDlgOpenFile(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~CDlgOpenFile();
	virtual BOOL OnInitDialog( );
	afx_msg LRESULT OnOpComplete( WPARAM wParam , LPARAM lParam );
// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_PROGRESS };
#endif
    void SetVa( CEmpower* pva )
        {
        m_pva = pva;
        }

	void SetFilePath( const CString& strFilePath )
		{
		m_strFilePath = strFilePath;
		}

	static UINT __cdecl WorkThread( LPVOID lParam );
	
protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
    virtual BOOL PreTranslateMessage( MSG* pMsg );
    virtual BOOL OnCommand( WPARAM wParam , LPARAM lParam ) override;

	DECLARE_MESSAGE_MAP()
	VE::VeOpenFileError EmpowerCheck( void );
	void CheckEmpFinish( );
	VE::VeOpenFileError Init( void );
	void CheckInitFinish( );
	VE::VeOpenFileError PlayVerCheck( void );
	void CheckPlayFinish( );
private:
    CEmpower* m_pva;
	CProgressCtrl m_progressCtrl;
	CString       m_strFilePath;
};
