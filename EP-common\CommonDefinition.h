﻿#pragma once

// VE库的公共定义和常量
#include <Windows.h>

// 进度回调函数类型
typedef void (*PROGRESS_CALLBACK)(unsigned long current, unsigned long total, LPCTSTR message, void* userdata);

// 错误代码定义
enum VE_RESULT
{
    VE_SUCCESS = 0,                      // 操作成功
    VE_ERROR_PARAMETER = 1,              // 参数错误
    VE_ERROR_OPEN_INPUT_FILE = 2,        // 打开输入文件失败
    VE_ERROR_NO_STREAM_INFO = 3,         // 获取流信息失败
    VE_ERROR_NO_VIDEO_STREAM = 4,        // 没有视频流
    VE_ERROR_NO_AUDIO_STREAM = 5,        // 没有音频流
    VE_ERROR_OPEN_DECODER = 6,           // 打开解码器失败
    VE_ERROR_OPEN_ENCODER = 7,           // 打开编码器失败
    VE_ERROR_MEMORY_ALLOCATION = 8,      // 内存分配失败
    VE_ERROR_CREATE_OUTPUT_FILE = 9,     // 创建输出文件失败
    VE_ERROR_WRITE_HEADER = 10,          // 写入文件头失败
    VE_ERROR_ENCRYPTION_FAILED = 11,     // 加密失败
    VE_ERROR_DECRYPTION_FAILED = 12,     // 解密失败
    VE_ERROR_FORMAT_NOT_SUPPORTED = 13,  // 格式不支持
    VE_ERROR_FILE_EXISTS = 14,           // 文件已存在
    VE_ERROR_INVALID_PASSWORD = 15,      // 密码无效
    VE_ERROR_INVALID_INPUT = 16          // 输入文件无效或已损坏
};

// === 新增：文件校验类型定义 ===
enum FILE_VERIFY_TYPE
{
    VERIFY_FULL_MD5 = 0,        // 完整MD5校验（传统方式）
    VERIFY_FAST_FINGERPRINT,    // 快速指纹校验（推荐用于大文件）
    VERIFY_PARTIAL_MD5,         // 部分MD5校验（中等大小文件）
    VERIFY_CRC32,               // CRC32校验（最快）
    VERIFY_HYBRID_FAST,         // 混合快速校验
    VERIFY_SMART_AUTO           // 智能自动选择（根据文件大小）
};

// 文件校验配置
struct FileVerifyConfig
{
    FILE_VERIFY_TYPE type;
    size_t head_size;       // 文件头校验大小
    size_t tail_size;       // 文件尾校验大小
    size_t middle_size;     // 文件中间校验大小
    
    // 默认配置
    FileVerifyConfig() 
        : type(VERIFY_SMART_AUTO)
        , head_size(64 * 1024)
        , tail_size(64 * 1024)
        , middle_size(32 * 1024)
    {}
};

// 视频文件信息结构体
typedef struct tagVideoFileInfo
{
    int duration;           // 时长(秒)
    int64_t size;          // 文件大小(字节)
    int width;             // 视频宽度
    int height;            // 视频高度
    int bit_rate;          // 码率
    int frame_rate;        // 帧率
    int key_frame_count;   // 关键帧数量
    wchar_t format[32];       // 视频格式
    wchar_t codec[32];        // 编码格式
    wchar_t create_time[64];  // 创建时间
    wchar_t modify_time[64];  // 修改时间
} VideoFileInfo, VideoInfo; // 为了兼容性，同时定义两个名称

// 加密参数结构体
typedef struct tagEncryptParams
{
    void* main_window;                      // 主窗口句柄
    PROGRESS_CALLBACK progress_callback;    // 进度回调函数 (新名称)
    //PROGRESS_CALLBACK progress_fn;          // 进度回调函数 (旧名称，保持兼容)
    void* user_data;                        // 用户数据 (新名称)
    //void* userdata;                         // 用户数据 (旧名称，保持兼容)
    bool delete_source;                     // 是否删除源文件
    wchar_t input_file[512];                   // 输入文件路径
    wchar_t output_file[512];                  // 输出文件路径
    wchar_t password[128];                     // 加密密码
} EncryptParams; 