#pragma once

#include <Windows.h>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>
#include "DebugLogger.h"

// 进程状态
enum class ProcessState {
    Started,        // 进程已启动
    Running,        // 进程正在运行
    Terminated,     // 进程已终止
    Crashed         // 进程崩溃
};

// 进程信息结构
struct ProcessInfo {
    DWORD processId;                // 进程ID
    std::wstring processName;       // 进程名称
    std::wstring processPath;       // 进程完整路径
    DWORD parentProcessId;          // 父进程ID
    HANDLE processHandle;           // 进程句柄
    DWORD startTime;                // 启动时间戳
    DWORD exitCode;                 // 退出代码
    ProcessState state;             // 进程状态
    std::wstring commandLine;       // 命令行参数
    DWORD sessionId;                // 会话ID
};

// 进程回调函数类型
using ProcessCallback = std::function<void(const ProcessInfo& processInfo)>;

// 进程监控类
class ProcessMonitor {
public:
    // 单例模式获取实例
    static ProcessMonitor& GetInstance();

    // 禁止拷贝和赋值
    ProcessMonitor(const ProcessMonitor&) = delete;
    ProcessMonitor& operator=(const ProcessMonitor&) = delete;

    // 初始化监控系统
    bool Initialize(const std::wstring& appName);

    // 停止监控系统
    void Shutdown();

    // 开始监控
    bool StartMonitoring();

    // 停止监控
    void StopMonitoring();

    // 设置进程启动回调
    void SetProcessStartCallback(ProcessCallback callback);

    // 设置进程终止回调
    void SetProcessTerminateCallback(ProcessCallback callback);

    // 设置进程崩溃回调
    void SetProcessCrashCallback(ProcessCallback callback);

    // 添加需要监控的进程名称（支持通配符）
    void AddMonitoredProcess(const std::wstring& processName);

    // 移除监控的进程名称
    void RemoveMonitoredProcess(const std::wstring& processName);

    // 清空监控的进程列表
    void ClearMonitoredProcesses();

    // 获取当前正在运行的所有进程
    std::vector<ProcessInfo> GetRunningProcesses() const;

    // 获取特定进程信息
    ProcessInfo GetProcessInfo(DWORD processId) const;

    // 检查进程是否正在运行
    bool IsProcessRunning(DWORD processId) const;
    bool IsProcessRunning(const std::wstring& processName) const;

    // 强制终止进程
    bool TerminateProcess(DWORD processId, DWORD exitCode = 1);
    bool TerminateProcess(const std::wstring& processName, DWORD exitCode = 1);

    // 获取进程内存使用情况
    size_t GetProcessMemoryUsage(DWORD processId) const;

    // 获取进程CPU使用率
    float GetProcessCpuUsage(DWORD processId) const;

private:
    ProcessMonitor();
    ~ProcessMonitor();

    // 监控线程函数
    void MonitorThreadFunc();

    // 处理进程启动
    void HandleProcessStart(const ProcessInfo& processInfo);

    // 处理进程终止
    void HandleProcessTerminate(const ProcessInfo& processInfo);

    // 处理进程崩溃
    void HandleProcessCrash(const ProcessInfo& processInfo);

    // 检测异常退出
    bool IsAbnormalTermination(DWORD exitCode) const;

    // 获取进程命令行
    std::wstring GetProcessCommandLine(HANDLE processHandle) const;

    // 更新进程列表
    void UpdateProcessList();

    // 进程名称匹配（支持通配符）
    bool MatchProcessName(const std::wstring& processName, const std::wstring& pattern) const;

    // 获取进程路径
    std::wstring GetProcessPath(HANDLE processHandle) const;

private:
    static std::unique_ptr<ProcessMonitor> s_instance;
    static std::once_flag s_onceFlag;

    std::thread m_monitorThread;                // 监控线程
    std::atomic<bool> m_isRunning;              // 运行标志
    mutable std::mutex m_processMutex;          // 进程互斥锁
    mutable std::mutex m_callbackMutex;         // 回调互斥锁
    std::map<DWORD, ProcessInfo> m_processes;   // 进程映射表
    std::vector<std::wstring> m_monitoredProcesses; // 需要监控的进程名称列表
    ProcessCallback m_startCallback;            // 进程启动回调
    ProcessCallback m_terminateCallback;        // 进程终止回调
    ProcessCallback m_crashCallback;            // 进程崩溃回调
    DebugLogger& m_logger;                      // 日志记录器
    DWORD m_monitorIntervalMs;                  // 监控间隔(毫秒)

    // CPU使用率计算相关
    struct CPUUsageData {
        FILETIME lastSysKernel;
        FILETIME lastSysUser;
        FILETIME lastProcKernel;
        FILETIME lastProcUser;
        DWORD lastTime;
        float usage;
    };
    mutable std::map<DWORD, CPUUsageData> m_cpuUsageData; // 进程CPU使用率数据
    mutable std::mutex m_cpuUsageMutex;                   // CPU使用率数据互斥锁
};

// 辅助宏，方便使用
#define PROCESS_MONITOR ProcessMonitor::GetInstance() 