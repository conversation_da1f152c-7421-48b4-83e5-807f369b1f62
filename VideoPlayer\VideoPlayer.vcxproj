<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{F2C561EC-52DE-4768-8274-89E9381B5444}</ProjectGuid>
    <Keyword>MFCProj</Keyword>
    <RootNamespace>VideoPlayer</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)..\bin\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
    <RunCodeAnalysis>true</RunCodeAnalysis>
    <EnableClangTidyCodeAnalysis>false</EnableClangTidyCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)..\bin\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)..\int\$(Configuration)\$(ProjectName)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;_CRTDBG_MAP_ALLOC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>A:\金钻视频加密专家\ve-common;A:\金钻视频加密专家\EP-common;A:\crypto++\8.90\include;A:\金钻视频加密专家\FunLib;A:\WebView2\include;A:\ffmpeg\msvc\include;A:\VMProtectSDK\Include\C;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <FloatingPointModel>Precise</FloatingPointModel>
      <ControlFlowGuard>Guard</ControlFlowGuard>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalLibraryDirectories>A:\金钻视频加密专家\out\Debug\FunLib\Win32;A:\VMProtectSDK\Lib\Windows;A:\crypto++\8.90\lib\Debug;A:\ffmpeg\msvc\lib\x86;A:\WebView2\x86;A:\金钻视频加密专家\lib\Debug\Win32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>libavdeviced.lib;libavfilterd.lib;libavformatd.lib;libavcodecd.lib;libpostprocd.lib;libswresampled.lib;libswscaled.lib;libavutild.lib;libsdl2d.lib;cryptlib.lib;winmm.lib;Ws2_32.lib;bcrypt.lib;EP-common.lib;FFPlayerLib.lib;ve-common.lib;funlib.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MinimumRequiredVersion>6.1</MinimumRequiredVersion>
      <ImageHasSafeExceptionHandlers>true</ImageHasSafeExceptionHandlers>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <DebugInformationFormat>None</DebugInformationFormat>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <AdditionalIncludeDirectories>A:\VMProtectSDK\Include\C;A:\金钻视频加密专家\ve-common;A:\金钻视频加密专家\EP-common;A:\crypto++\8.90\include;A:\金钻视频加密专家\FunLib;A:\WebView2\include;A:\ffmpeg\msvc\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ExceptionHandling>Async</ExceptionHandling>
      <FloatingPointModel>Precise</FloatingPointModel>
      <ControlFlowGuard>Guard</ControlFlowGuard>
      <OpenMPSupport>false</OpenMPSupport>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>libavutil.lib;libavcodec.lib;libavformat.lib;libswresample.lib;libswscale.lib;libavfilter.lib;libpostproc.lib;libsdl2.lib;cryptlib.lib;winmm.lib;Ws2_32.lib;bcrypt.lib;EP-common.lib;FFPlayerLib.lib;ve-common.lib;funlib.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>A:\WebView2\x86;A:\ffmpeg\msvc\lib\x86;A:\crypto++\8.90\lib\Release;A:\金钻视频加密专家\bin\Release\Win32;A:\VMProtectSDK\Lib\Windows;A:\金钻视频加密专家\lib\Release\Win32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <GenerateMapFile>true</GenerateMapFile>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="CDlgAsk.h" />
    <ClInclude Include="CDlgInfo.h" />
    <ClInclude Include="CDlgOpenFile.h" />
    <ClInclude Include="CDlgWed.h" />
    <ClInclude Include="CommandLineParser.h" />
    <ClInclude Include="framework.h" />
    <ClInclude Include="funLib.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="progressHand.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="ScopeGuard.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="UI\BitmapSlider.h" />
    <ClInclude Include="UI\CaptchaStatic.h" />
    <ClInclude Include="UI\DUIButton.h" />
    <ClInclude Include="UI\memdc.h" />
    <ClInclude Include="UI\pngButton\Border.h" />
    <ClInclude Include="UI\pngButton\colors.h" />
    <ClInclude Include="UI\pngButton\GResource.h" />
    <ClInclude Include="UI\pngButton\Layers.h" />
    <ClInclude Include="UI\pngButton\MemDC.h" />
    <ClInclude Include="UI\pngButton\Style.h" />
    <ClInclude Include="UI\pngButton\StyleButton.h" />
    <ClInclude Include="UI\pngButton\StyleEnum.h" />
    <ClInclude Include="VideoPlayer.h" />
    <ClInclude Include="VideoPlayerDlg.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CDlgAsk.cpp" />
    <ClCompile Include="CDlgInfo.cpp" />
    <ClCompile Include="CDlgOpenFile.cpp" />
    <ClCompile Include="CDlgWed.cpp" />
    <ClCompile Include="CommandLineParser.cpp" />
    <ClCompile Include="funLib.cpp" />
    <ClCompile Include="LeakHook.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="progressHand.cpp" />
    <ClCompile Include="UI\BitmapSlider.cpp" />
    <ClCompile Include="UI\CaptchaStatic.cpp" />
    <ClCompile Include="UI\DUIButton.cpp" />
    <ClCompile Include="UI\pngButton\Border.cpp" />
    <ClCompile Include="UI\pngButton\Layers.cpp" />
    <ClCompile Include="UI\pngButton\Style.cpp" />
    <ClCompile Include="UI\pngButton\StyleButton.cpp" />
    <ClCompile Include="VideoPlayer.cpp" />
    <ClCompile Include="VideoPlayerDlg.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="VideoPlayer.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="res\VideoPlayer.rc2" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\mp_channel.bmp" />
    <Image Include="res\mp_channel_active.bmp" />
    <Image Include="res\mp_thumb.bmp" />
    <Image Include="res\mp_thumb_active.bmp" />
    <Image Include="res\ok.ico" />
    <Image Include="res\png1.png" />
    <Image Include="res\png\01.png" />
    <Image Include="res\png\02.png" />
    <Image Include="res\png\03.png" />
    <Image Include="res\png\04.png" />
    <Image Include="res\png\05.png" />
    <Image Include="res\png\06.png" />
    <Image Include="res\png\6.png" />
    <Image Include="res\png\jy32.png" />
    <Image Include="res\png\yl32.png" />
    <Image Include="res\VideoPlayer.ico" />
    <Image Include="res\vol_off.bmp" />
    <Image Include="res\vol_on.bmp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>