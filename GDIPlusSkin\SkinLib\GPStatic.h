/************************************************************************
ģ����:
************************************************************************/

#pragma once

#include <memory>
#include <gdiplus.h>
using namespace Gdiplus;

class CGPStatic : public CStatic {
    public:

    CGPStatic( );
    virtual ~CGPStatic( );

    void SetTextColor( Color cTextColor );
    void SetFontBold( BOOL bBold );
    void SetFontUnderline( BOOL bSet );
    void SetlfHeight( LONG lfHeight );
    void SetLinkCursor( int nCursorId );

    void SetDisable( BOOL bDisable )
        {
        m_bDisable = bDisable;
        }

    void SetDT( BOOL bDT )
        {
        m_bDT = bDT;
        }

    void SetFontSize( const int &iFontSize )
        {
        m_iFontSize = iFontSize;
        m_fontDirty = true;
        }

    void SetSelect( LPCTSTR sImage );
    void SetSelectAsync( LPCTSTR sImage, bool bLazyLoad = true ); // ??????
    void DrawItem( CDC* pDC );

    void SetCharSpacing(float v){ m_fCharSpacing = v; Invalidate(); }
    void SetLineSpacing( int n ){ m_nLineSpacing = n; Invalidate(); }
    // 控件级字体缩放（默认1.0，与全局一致；用于细微观感校准）
    void SetFontScale(float f){ if(f>0.5f && f<=1.35f){ m_fControlFontScale=f; m_fontDirty=true; Invalidate(); } }
    
    // 强制完全重绘，解决文字重叠问题
    void ForceRedraw() { 
        m_fontDirty = true; 
        RedrawWindow(NULL, NULL, RDW_INVALIDATE | RDW_ERASE | RDW_UPDATENOW);
    }

    // ===== ??????? =====
    bool SetImage( UINT uResID , LPCTSTR lpszResType = nullptr );   // ???? ID ????
    bool SetImage( LPCTSTR lpszFilePath );                          // ??????????
    void ClearImage( );                                             // ????

    // 重写SetWindowText，确保文字更新时完全重绘
    virtual void SetWindowText(LPCTSTR lpszString);
    
    // 专用的文字更新接口，完全控制重绘流程
    void UpdateText(LPCTSTR lpszString);

    public:
    void CacheParentBackground();   // 预抓父窗口背景
    afx_msg void OnSize(UINT nType, int cx, int cy); // 处理尺寸变化
    virtual void PreSubclassWindow( );
    void PaintParentBackground(CDC* pDC, const CRect& rcClient); // 让父窗口绘制背景
    //{{AFX_MSG(CGPStatic)
    virtual void    DrawItem( LPDRAWITEMSTRUCT lpDrawItemStruct );
    afx_msg void    OnDestroy( );
    afx_msg void    OnPaint( );
    afx_msg void    OnMouseMove( UINT nFlags , CPoint point );
    afx_msg BOOL    OnEraseBkgnd( CDC* pDC );
    afx_msg BOOL    OnSetCursor( CWnd* pWnd , UINT nHitTest , UINT message );
    afx_msg LRESULT OnLButtonUp( WPARAM wparam , LPARAM lparam );
    afx_msg LRESULT OnMouseLeave( WPARAM wparam , LPARAM lparam );
    afx_msg LRESULT OnMouseHover( WPARAM wparam , LPARAM lparam );
    afx_msg LRESULT OnDpiChanged( WPARAM wParam , LPARAM lParam );
    //}}AFX_MSG

    DECLARE_MESSAGE_MAP( )

    private:
    Color	m_cNormalColor;
    Color	m_cDisableColor;// Gainsboro
    BOOL    m_bBold;
    BOOL    m_bDisable;
    BOOL    m_bUnderline;

    BOOL	m_bHovering;
    BOOL	m_bTracking;
    BOOL    m_bSelect;

    LONG    m_lfHeight;
    HCURSOR m_hCursor;
    BOOL    m_bDT;
    BOOL    m_bInit;
    int     m_iFontSize;
    Image*  m_pSelect;
    CString m_sSelectKey;  // ????????????????

    // GDI
    std::unique_ptr<Gdiplus::Font> m_spFont;
    float                 m_fCurrentPix { 1.0f };
    Gdiplus::StringFormat          m_sfFormat;
    float                           m_fControlFontScale { 1.0f };

    //
    void            RebuildFont( CDC* pDC );
    void            UpdatePix( float fPix );

    bool    m_fontDirty { true };

    int     m_iSelWidth  { 0 };   // ????????
    int     m_iSelHeight { 0 };   // ??????
    int     m_iSelFrames { 0 };   // ??????

    float  m_fCharSpacing = 0.0f;  // ??????
    int    m_nLineSpacing = 4;  // ??

    // ===== ????????? =====
    enum class ImgType { None , Bitmap };
    std::unique_ptr<Gdiplus::Image> m_spImage;   // GDI+ ????
    ImgType  m_eImgType { ImgType::None };       // ??????

    // === 背景缓存 ===
    CBitmap m_bmpBk;
    CSize   m_bkSize{0,0};
    };
