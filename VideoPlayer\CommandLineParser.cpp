#include "pch.h"
#include "CommandLineParser.h"
#include <shellapi.h>

CommandLineParser::CommandLineParser()
    : m_b<PERSON><PERSON>Play(false)
    , m_bSilent(false)
    , m_bShow<PERSON>elp(false)
{
}

CommandLineParser::~CommandLineParser()
{
}

bool CommandLineParser::ParseCommandLine()
{
    int argc = 0;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);
    
    if (argv == nullptr || argc < 1)
    {
        return false;
    }
    
    bool result = ParseArguments(argc, argv);
    LocalFree(argv);
    
    return result;
}

bool CommandLineParser::ParseArguments(int argc, LPWSTR* argv)
{
    // 如果只有程序名，返回 true（正常启动）
    if (argc == 1)
    {
        return true;
    }
    
    for (int i = 1; i < argc; i++)
    {
        CString arg(argv[i]);
        arg.MakeLower();
        
        if (arg == L"--help" || arg == L"-h" || arg == L"/?")
        {
            m_bShowHelp = true;
        }
        else if (arg == L"--autoplay" || arg == L"-a")
        {
            m_bAutoPlay = true;
        }
        else if (arg == L"--silent" || arg == L"-s")
        {
            m_bSilent = true;
        }
        else if (arg == L"--file" || arg == L"-f")
        {
            // 下一个参数应该是文件路径
            if (i + 1 < argc)
            {
                i++;
                m_strFilePath = argv[i];
                TrimQuotes(m_strFilePath);
            }
        }
        else if (arg.Left(2) != L"--" && arg.Left(1) != L"-")
        {
            // 直接的文件路径参数（不带前缀）
            if (m_strFilePath.IsEmpty())
            {
                m_strFilePath = argv[i];
                TrimQuotes(m_strFilePath);
            }
        }
    }
    
    return true;
}

void CommandLineParser::TrimQuotes(CString& str) const
{
    str.Trim();
    if (str.GetLength() >= 2)
    {
        if ((str.Left(1) == L"\"" && str.Right(1) == L"\"") ||
            (str.Left(1) == L"'" && str.Right(1) == L"'"))
        {
            str = str.Mid(1, str.GetLength() - 2);
        }
    }
}

bool CommandLineParser::IsValidFile() const
{
    if (m_strFilePath.IsEmpty())
    {
        return false;
    }
    
    // 检查文件是否存在
    DWORD dwAttrib = GetFileAttributesW(m_strFilePath);
    if (dwAttrib == INVALID_FILE_ATTRIBUTES)
    {
        return false;
    }
    
    // 检查是否为文件（不是目录）
    if (dwAttrib & FILE_ATTRIBUTE_DIRECTORY)
    {
        return false;
    }
    
    // 检查文件扩展名
    return IsFileExtensionSupported(m_strFilePath);
}

bool CommandLineParser::IsFileExtensionSupported(const CString& filePath) const
{
    CString ext = PathFindExtensionW(filePath);
    ext.MakeLower();
    
    // 支持的加密视频文件扩展名
    if (ext == L".vemp4" || ext == L".veavi" || ext == L".vemkv" || 
        ext == L".vemov" || ext == L".vewmv" || ext == L".veflv" || 
        ext == L".vewebm")
    {
        return true;
    }
    
    // 支持的普通视频文件扩展名
    if (ext == L".mp4" || ext == L".avi" || ext == L".mkv" || 
        ext == L".mov" || ext == L".wmv" || ext == L".flv" || 
        ext == L".webm")
    {
        return true;
    }
    
    return false;
}

void CommandLineParser::ShowHelp() const
{
    CString helpText = 
        L"金钻视频加密专家 - 命令行使用说明\n\n"
        L"用法:\n"
        L"  VideoPlayer.exe [选项] [文件路径]\n\n"
        L"选项:\n"
        L"  --file, -f <路径>     指定要播放的视频文件\n"
        L"  --autoplay, -a        自动开始播放\n"
        L"  --silent, -s          静默模式启动\n"
        L"  --help, -h, /?        显示此帮助信息\n\n"
        L"示例:\n"
        L"  VideoPlayer.exe \"C:\\Videos\\movie.vemp4\"\n"
        L"  VideoPlayer.exe --file \"C:\\Videos\\movie.vemp4\" --autoplay\n"
        L"  VideoPlayer.exe -f \"movie.mp4\" -a -s\n\n"
        L"支持的文件格式:\n"
        L"  .vemp4, .veavi, .vemkv, .vemov, .vewmv, .veflv, .vewebm (加密视频文件)\n"
        L"  .mp4, .avi, .mkv, .mov, .wmv, .flv, .webm (普通视频文件)\n";
    
    MessageBoxW(nullptr, helpText, L"使用帮助", MB_OK | MB_ICONINFORMATION);
}