﻿// VideoPlayerDlg.h: 头文件
//

#pragma once
#include ".\UI\pngButton\StyleButton.h"
#include ".\ui\BitmapSlider.h"
#include <VideoAESCommon.h>
#include <closewindows/WindowCloser.h>
#include <Empower.h>
#include <mmdeviceapi.h>
#include <endpointvolume.h>
#include <chrono>
// 新增头文件用于时间点
#pragma comment(lib, "Ole32.lib")
#define WM_USER_SHOW_WEB (WM_USER + 2000)
#define WM_USER_SHOW_ASK (WM_USER + 2100) // 随机校验弹窗消息
// CVideoPlayerDlg 对话框
class CVideoPlayerDlg : public CDialogEx
{
// 构造
public:
	CVideoPlayerDlg(CWnd* pParent = nullptr);	// 标准构造函数
    afx_msg void OnClose( );
    // 命令行相关方法
    void SetCommandLineFile( const CString& filePath , bool autoPlay );
// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_VIDEOPLAYER_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持
    virtual BOOL PreTranslateMessage( MSG* pMsg );
   
// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
    afx_msg void OnSize( UINT nType , int cx , int cy );
    afx_msg BOOL OnEraseBkgnd( CDC* pDC );
    afx_msg HBRUSH OnCtlColor( CDC* pDC , CWnd* pWnd , UINT nCtlColor );
	DECLARE_MESSAGE_MAP()

    // 播放完成消息处理
    afx_msg LRESULT OnPlaybackComplete( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnOpenSuccess( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnSeekDone( WPARAM wParam , LPARAM lParam );
    afx_msg void OnBnClickedButtonPlay( );
    afx_msg void OnBnClickedButtonStop( );
    afx_msg void OnTimer( UINT_PTR nIDEvent );
	afx_msg LRESULT OnSeeking( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnSeek( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnDown( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnFailedWnd( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnFailedProcess( WPARAM wParam , LPARAM lParam );
    afx_msg LRESULT OnShowAsk( WPARAM wParam , LPARAM lParam ); // 新增：显示校验弹窗
    afx_msg void OnGetMinMaxInfo( MINMAXINFO* lpMMI );
    afx_msg void OnBnClickedButtonVol( );
    afx_msg void OnHScroll( UINT nSBCode , UINT nPos , CScrollBar* pScrollBar );
    afx_msg void OnLButtonDblClk( UINT nFlags , CPoint point );
    afx_msg void OnLButtonDown( UINT nFlags , CPoint point );
    afx_msg void OnVideoAreaClicked();          // 静态控件点击
    afx_msg void OnVideoAreaDblClk();           // 静态控件双击
    afx_msg void OnParentNotify(UINT message, LPARAM lParam);
    afx_msg LRESULT OnVideoClickMsg(WPARAM, LPARAM);
    afx_msg LRESULT OnVideoDblClkMsg(WPARAM, LPARAM);
    afx_msg void OnActivate( UINT nState , CWnd* pWndOther , BOOL bMinimized );
    afx_msg void OnEnterSizeMove();   // 拖动或移动窗口开始
    afx_msg void OnExitSizeMove();    // 拖动或移动窗口结束
    afx_msg void OnBnClickedButtonBack( );
    afx_msg void OnBnClickedButtonForward( );
    afx_msg void OnBnClickedButtonOpen( );

    void PauseOrPlay( bool pause );
    void GetCloseProcessList(const  CString &strList );
    // 布局辅助函数
    void AddCtrl( UINT id , bool scaleW , bool scaleH );

    // 更新进度条
	void UpdateProgressBar( );

	void UpdateTimeDisplay( );
	void ClearVideoArea( );
	CString FormatTime( double seconds );
    int GetSystemVolumePercent( );
    void         ToggleFullScreen( );
    CString      OpenFile( void );
    CString      OpenVECA( void );
    FILETIME     m_playTime;

    // 后台播放线程函数（类成员）
    struct PlayTaskInfo {
        CString            filePath;      // 待播放文件路径
        int                volume;        // 初始音量 0~100
        bool               isEncrypted;   // 是否加密
        VideoEncryptType   vt;            // 视频加密类型
        AudioEncryptType   at;            // 音频加密类型
        LONGLONG           lTryTime;
        char               password [ 256 ]; // 播放口令
        };
   
private:
    // 定时器ID
    static const UINT_PTR TIMER_ID_UPDATE_PROGRESS = 1001;
    static const UINT_PTR TIMER_ID_SEEK_COMPLETE = 1002;
    static const UINT_PTR TIMER_ID_COMMAND_LINE_PLAY = 1003;
    
    vector<wstring> m_processBlocklist;
    bool    m_bFullScreen;
   
    enum class PlayState
        {
        Standby,
        Playing,
        ​Paused,
        ​Stopped
        };

    PlayTaskInfo  m_pti;
    WindowCloser  m_wc;
    bool          m_bMonitSucess;

	HWND m_hwndVideo;  // 用于显示视频的窗口句柄
	CWnd* m_pVideoWnd;

    BOOL     m_bIsRunOther;
    BOOL     m_bIsPlayAsk;
    CString m_strPlayBefore;
    CString m_strPlayAfter;
    // 状态变量
    PlayState  m_uPlayState;
    bool m_bIsSeeking;
	double m_dSeekPreviewTime;
	double m_totalTime;
    bool    m_bVolOn;
    // 添加光标限制状态
    bool m_bIsLimitMouse;
    // 标记是否正在调整窗口大小
    bool m_bIsSizing;

    BOOL m_bIsWatermark;
    CString m_strWatermark;

    // === 自动校验相关 ===
    std::chrono::steady_clock::time_point m_nextAskTime; // 下次弹窗时间点
    bool m_bAskDialogShowing;                            // 弹窗是否已显示
    std::chrono::milliseconds RandomInterval();          // 随机间隔生成

    CBrush   m_brush;
	CDC         m_memDC;
	CBitmap     m_memBitmap;

	CStatic m_staticTime;
    StyleButton m_btnPlay;
    StyleButton m_btnStop;
	CBitmapSlider m_playprogress;	
    CSliderCtrl m_sliderVolume;
	StyleButton m_btnOpen;
	StyleButton m_btnBack;
	StyleButton m_btnForward;
    StyleButton m_btnVol;


    CRect        m_rcVideoOrig;
    UINT         m_butW;
    UINT         m_butH;
    // 初始布局度量
    int          m_marginEdge;   // 外边距
    int          m_marginCtrl;   // 控件间距
    int          m_progressH;    // 进度条高度
    int          m_volumeH;      // 音量滑块高度
    int          m_volumeW;      // 音量滑块宽度
	
     // 布局信息结构体
    struct CtrlInfo {
        UINT   id;       // 控件 ID
        CRect  rcOrig;   // 初始矩形（客户坐标）
        bool   bScaleW;  // 是否缩放宽度
        bool   bScaleH;  // 是否缩放高度
        };
    // 保存所有控件初始布局
    std::vector<CtrlInfo> m_vecCtrlInfo;
    CRect   m_rcDlgOrig;      // 对话框初始客户区
    CSize   m_szMinTrack;     // 最小窗口尺寸

  
     // 精确布局度量（初始化时采集）
    int          m_marginLeft;     // 左外边距（按钮距离左边）
    int          m_marginRight;    // 右外边距（音量控件距离右边）
    int          m_marginBottom;   // 窗口底边到按钮底边距离
    int          m_spacingBtn;     // 相邻按钮之间水平间距
    int          m_gapProgBtn;     // 进度条到底部按钮之间的垂直间距
    int          m_marginBottomVol;   // 音量滑块到底边距离
    int          m_marginBottomTime;  // 时间文本到底边距离
  

    void PlayVideoThread( PlayTaskInfo task );
    void BackgroundWorkerThread(void);
    bool EvaluateFile( void );
    
  
    void StartCommandLinePlayback();
    
    // 全屏切换时用于恢复窗口原始位置/状态
    WINDOWPLACEMENT m_prevPlacement;
    bool            m_hasPrevPlacement;
    
    // 命令行相关变量
    CString m_strCommandLineFile;
    bool    m_bCommandLineAutoPlay;
};
