/************************************************************************

************************************************************************/

#include "pch.h"
#include "GPStatic.h"
#include "GPImageInfo.h"
#include "command/MemoryDC.h"
#include "command/Functions.h"
#include "FastImageRenderer.h"
#include <Shlwapi.h>
//#include <ShellScalingAPI.h>   // 
#include <UxTheme.h>
#pragma comment(lib, "Shlwapi.lib")
#pragma comment(lib, "UxTheme.lib")

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

//
#pragma warning(disable:4244)

CGPStatic::CGPStatic( ) :m_bBold( FALSE )
, m_lfHeight( 0)
, m_bUnderline( FALSE )
, m_hCursor( NULL )
, m_bDisable( FALSE )
, m_bDT( FALSE )
, m_bInit(FALSE )
, m_iFontSize( 0 )
, m_pSelect( NULL )
, m_bHovering( FALSE )
, m_bTracking( FALSE )
, m_bSelect( FALSE )
, m_fontDirty( true )
    {
    m_cNormalColor = ( ARGB ) ( Color::Black );
    m_cDisableColor = ( ARGB ) ( Color::Gray );

    // 与 GPButton 一致：记录当前 DPI 比例，避免首次绘制前尺寸不一致
    m_fCurrentPix = g_fPix;
    }

CGPStatic::~CGPStatic( )
    {
    //
    m_pSelect = NULL;

    ClearImage();   // 
    
    if (m_hCursor) {
        ::DestroyCursor(m_hCursor);
        m_hCursor = NULL;
    }
    }

BEGIN_MESSAGE_MAP( CGPStatic , CStatic )
    //{{AFX_MSG_MAP(CGPStatic)
    ON_WM_DESTROY( )
    ON_WM_ERASEBKGND( )
    ON_WM_PAINT()
    //}}AFX_MSG_MAP
    ON_WM_SETCURSOR( )
    ON_WM_MOUSEMOVE( )
    ON_WM_SIZE()
    ON_MESSAGE( WM_LBUTTONUP , &CGPStatic::OnLButtonUp )
    ON_MESSAGE( WM_MOUSELEAVE , &CGPStatic::OnMouseLeave )
    ON_MESSAGE( WM_MOUSEHOVER , &CGPStatic::OnMouseHover )
    ON_MESSAGE( WM_DPICHANGED , &CGPStatic::OnDpiChanged )
END_MESSAGE_MAP( )

//=============================================================================
void CGPStatic::OnDestroy( )
    {
    ClearImage();           //
    CWnd::OnDestroy( );
    }
//=============================================================================
void CGPStatic::SetSelect( LPCTSTR sImage )
    {
    // 
    if (sImage == NULL) {
        return;
    }
    
    //
    Image* pNewImage = CGPImageInfo::Instance()->ImageFromFile(sImage);
    
    // 
    if (pNewImage != NULL) {
        m_pSelect = pNewImage;
        m_sSelectKey = sImage;  //

        // 
        m_iSelHeight = m_pSelect->GetHeight();
        UINT imgW   = m_pSelect->GetWidth();
        m_iSelFrames = (m_iSelHeight>0) ? static_cast<int>(imgW / m_iSelHeight) : 0;
        if (m_iSelFrames < 3) m_iSelFrames = 4; // 
        m_iSelWidth  = imgW / (m_iSelFrames ? m_iSelFrames : 1);
        
        //
    }
    }

//=============================================================================
void CGPStatic::SetSelectAsync( LPCTSTR sImage, bool bLazyLoad )
    {
    //
    if (sImage == NULL) {
        return;
    }
    
    if (!bLazyLoad) {
        //
        SetSelect(sImage);
        return;
    }
    
    //
    m_sSelectKey = sImage;
    
    //
    CGPImageInfo::Instance()->ImageFromFileAsync(sImage, 
        [this, sImage](Gdiplus::Image* pImage, const CString& path) {
            if (pImage != NULL && path == m_sSelectKey) {
                //
                m_pSelect = pImage;
                
                //
                m_iSelHeight = m_pSelect->GetHeight();
                UINT imgW   = m_pSelect->GetWidth();
                m_iSelFrames = (m_iSelHeight>0) ? static_cast<int>(imgW / m_iSelHeight) : 0;
                if (m_iSelFrames < 3) m_iSelFrames = 4; //
                m_iSelWidth  = imgW / (m_iSelFrames ? m_iSelFrames : 1);
                
                //
                if (::IsWindow(m_hWnd)) {
                    Invalidate();
                }
            }
        }, CLazyImageLoader::LoadPriority::Normal);
    }
//=============================================================================
BOOL CGPStatic::OnEraseBkgnd( CDC *pDC )
    {
    UNREFERENCED_PARAMETER(pDC);
    return FALSE; // 交给 WM_PAINT/DrawItem 统一处理背景
    }
//=============================================================================
void CGPStatic::PaintParentBackground(CDC* pDC, const CRect& rcClient)
    {
    if (!pDC) return;
    CWnd* pParent = GetParent();
    if (!pParent || !::IsWindow(pParent->GetSafeHwnd()))
        {
        pDC->FillSolidRect(rcClient, ::GetSysColor(COLOR_3DFACE));
        return;
        }

    // 优先使用主题API绘制父背景
    RECT rc = rcClient;
    if (SUCCEEDED(::DrawThemeParentBackground(m_hWnd, pDC->GetSafeHdc(), &rc)))
        {
        return;
        }

    // 回退：通过偏移视口让父窗口把内容画进来
    POINT pt = {0,0};
    MapWindowPoints(pParent, &pt, 1);
    int saved = pDC->SaveDC();
    pDC->SetViewportOrg(-pt.x, -pt.y);
    pParent->SendMessage(WM_ERASEBKGND, (WPARAM)pDC->GetSafeHdc());
    pParent->SendMessage(WM_PRINTCLIENT, (WPARAM)pDC->GetSafeHdc(), PRF_CLIENT);
    pDC->RestoreDC(saved);
    }
//=============================================================================
void CGPStatic::OnPaint()
{
	CPaintDC dc(this);
	DrawItem( &dc );
}

void CGPStatic::DrawItem( LPDRAWITEMSTRUCT lpDrawItemStruct )
    {
    CDC dc;
    dc.Attach( lpDrawItemStruct->hDC );
    DrawItem( &dc );
    dc.Detach( );
    }

void CGPStatic::DrawItem( CDC* pDC )
    {
    if (!pDC || !pDC->GetSafeHdc()) return;  // 
    if (!(GetStyle() & WS_VISIBLE)) return;


    // 若字体尚未准备，或 DPI 已变化，则重建字体
    float newPix = g_fPix;
    if (fabs( newPix - m_fCurrentPix ) > FLT_EPSILON) {
        UpdatePix( newPix );
        RebuildFont( pDC );
        }
    else if (!m_spFont) {
        RebuildFont( pDC );
        }


    CRect rcClient; GetClientRect(rcClient);
    PaintParentBackground(pDC, rcClient);

    CString sName;
    GetWindowText(sName);
    
    // 
    if (sName.IsEmpty() && ::IsWindow(m_hWnd)) {
        int nLen = ::GetWindowTextLength(m_hWnd);
        if (nLen > 0) {
            LPTSTR lpszText = sName.GetBuffer(nLen + 1);
            ::GetWindowText(m_hWnd, lpszText, nLen + 1);
            sName.ReleaseBuffer();
        }
    }
    
    if (sName.IsEmpty() && !m_spImage) return;   //

    // 直接使用传入的DC，不创建CMemoryDC以保持透明效果
    Gdiplus::Graphics graphics(pDC->GetSafeHdc());
    // 统一使用 ClearType 文本渲染，提升与按钮观感一致性
    graphics.SetTextRenderingHint(Gdiplus::TextRenderingHintClearTypeGridFit);
    
    // 使用快速渲染器进行批量优化
    CFastImageRenderer::Instance().BeginBatchDraw(&graphics);

    // 
    if (m_spImage)
        {
        // 
        CString imageKey;
        imageKey.Format(L"GPStatic_Image_%p", m_spImage.get());
        
        //
        CFastImageRenderer::Instance().PreprocessImage(m_spImage.get(), imageKey);
        
        // 
        CFastImageRenderer::Instance().FastDrawImage(&graphics, imageKey,
            Gdiplus::RectF((REAL)rcClient.left,
                           (REAL)rcClient.top,
                           (REAL)rcClient.Width(),
                           (REAL)rcClient.Height()));
        
        CFastImageRenderer::Instance().EndBatchDraw(&graphics);
        return;   //
        }

    // 
    if (m_pSelect != NULL && !m_sSelectKey.IsEmpty()) {
        int i = 0;
        if (m_bHovering) {
            i = 1;
        }
        else if (m_bSelect) {
            i = 2;
        }

        if (i > 0) {
            //
            int frameCount = (m_iSelFrames > 0) ? m_iSelFrames : 4;
            int iWidth     = (m_iSelWidth  > 0) ? m_iSelWidth  : m_pSelect->GetWidth() / frameCount;
            int iHeight    = (m_iSelHeight > 0) ? m_iSelHeight : m_pSelect->GetHeight();

            if(i >= frameCount) i = frameCount - 1; // 

            // 
            CFastImageRenderer::Instance().FastDrawImage(&graphics, m_sSelectKey,
                RectF(rcClient.left, rcClient.top, rcClient.Width(), rcClient.Height()),
                i * iWidth, 0, iWidth, iHeight);
        }
    }
   
    // 统一由 RebuildFont 处理所有字体样式与 DPI 缩放
    if (m_fontDirty || m_spFont == nullptr) {
        RebuildFont(pDC);
    }

    //
    Gdiplus::Color gdColor(m_bDisable ? m_cDisableColor : m_cNormalColor);
    Gdiplus::SolidBrush brush(gdColor);

    const int charSpacing = static_cast<int>(m_fCharSpacing + 0.5f); // 
    const int lineSpacing = m_nLineSpacing;          //

    // 
    REAL lineHeightF = m_spFont->GetHeight(&graphics);
    int  lineHeight  = static_cast<int>(lineHeightF + 0.5f);

    std::vector<std::basic_string<TCHAR>> vecLines;

    // 
    std::basic_string<TCHAR> currentLine;

    if(charSpacing<0.01f) {
        //
        Gdiplus::StringFormat measureFmt(StringFormatFlagsNoWrap);
        for(size_t idx=0; idx<sName.GetLength(); ++idx){
            TCHAR ch = sName[idx];

            if(ch==_T('\n')){
                vecLines.push_back(currentLine);
                currentLine.clear();
                continue;
            }

            std::basic_string<TCHAR> testLine = currentLine + ch;
            RectF sizeRect;
            graphics.MeasureString(testLine.c_str(), static_cast<int>(testLine.size()), m_spFont.get(), PointF(0,0), &measureFmt, &sizeRect);

            if(sizeRect.Width > rcClient.Width()){
                // 
                if(!currentLine.empty())
                    vecLines.push_back(currentLine);
                currentLine.clear();
            }

            currentLine.push_back(ch);
        }
        if(!currentLine.empty()) vecLines.push_back(currentLine);
    }
    else {
        //
        float curWidth = 0.f;
        for(size_t idx = 0; idx < sName.GetLength(); ++idx){
            TCHAR ch = sName[idx];
            if(ch==_T('\n')){
                vecLines.push_back(currentLine);
                currentLine.clear();
                curWidth = 0.f;
                continue;
            }

            WCHAR wc[2] = { ch, 0 };
            RectF sizeRect;
            graphics.MeasureString(wc, 1, m_spFont.get(), PointF(0,0), &sizeRect);
            float charW = sizeRect.Width;

            if(curWidth + charW > rcClient.Width()){
                vecLines.push_back(currentLine);
                currentLine.clear();
                curWidth = 0.f;
            }

            currentLine.push_back(ch);
            curWidth += charW + charSpacing;
        }
        if(!currentLine.empty()) vecLines.push_back(currentLine);
    }

    // 
    int totalHeight = static_cast<int>(vecLines.size()) * lineHeight + (static_cast<int>(vecLines.size()) - 1) * lineSpacing;
    int startY = (rcClient.Height() - totalHeight) / 2;

    // ---  ---
    int drawY = startY;
    if(charSpacing==0){
        //
        Gdiplus::StringFormat lineFmt;
        lineFmt.SetAlignment(StringAlignmentNear);
        lineFmt.SetLineAlignment(StringAlignmentNear);

        for(const auto& oneLine: vecLines){
            RectF lineRect(0, static_cast<REAL>(drawY), static_cast<REAL>(rcClient.Width()), static_cast<REAL>(lineHeight));
            graphics.DrawString(oneLine.c_str(), static_cast<int>(oneLine.size()), m_spFont.get(), lineRect, &lineFmt, &brush);
            drawY += lineHeight + lineSpacing;
        }
    } else {
        //
        Gdiplus::StringFormat chFmt(StringFormatFlagsNoWrap);
        for(const auto& oneLine : vecLines) {
            int drawX = 0;
            for(size_t i=0;i<oneLine.size();++i){
                WCHAR wc[2] = { oneLine[i], 0 };
                RectF sizeRect;
                graphics.MeasureString(wc, 1, m_spFont.get(), PointF(0,0), &chFmt, &sizeRect);
                graphics.DrawString(wc, 1, m_spFont.get(), PointF(static_cast<REAL>(drawX), static_cast<REAL>(drawY)), &brush);
                drawX += static_cast<int>(sizeRect.Width) + charSpacing;
            }
            drawY += lineHeight + lineSpacing;
        }
    }
    // ---------------------------------------------------------
    
    //
    CFastImageRenderer::Instance().EndBatchDraw(&graphics);
}

//=============================================================================
void CGPStatic::SetTextColor( Color cTextColor )
    {
    m_cNormalColor = cTextColor;
    ForceRedraw();  // 确保颜色更改时完全重绘
    }
//=============================================================================
void CGPStatic::SetFontBold( BOOL bBold )
    {
    m_bBold = bBold;
    m_fontDirty = true;
    ForceRedraw();  // 确保字体更改时完全重绘
    }
//=============================================================================
void CGPStatic::SetFontUnderline( BOOL bSet )
    {
    m_bUnderline = bSet;
    m_fontDirty = true;
    ForceRedraw();  // 确保字体更改时完全重绘
    }
//=============================================================================
void CGPStatic::SetLinkCursor( int nCursorId )
    {
    if ( nCursorId ) {
        //
        if ( m_hCursor ) {
            ::DestroyCursor( m_hCursor );
            m_hCursor = NULL;
            }

        HINSTANCE hInstResource = AfxFindResourceHandle( MAKEINTRESOURCE( nCursorId ) , RT_GROUP_CURSOR );
        if (hInstResource != NULL) {
            HCURSOR hNewCursor = (HCURSOR)::LoadImage( hInstResource, 
                                                      MAKEINTRESOURCE( nCursorId ), 
                                                      IMAGE_CURSOR, 
                                                      0, 
                                                      0, 
                                                      0 );
            //
            if (hNewCursor != NULL) {
                m_hCursor = hNewCursor;
            }
        }
    }
    }
//=============================================================================
void CGPStatic::SetlfHeight( LONG lfHeight )
    {
    m_iFontSize = lfHeight;
    m_fontDirty = true;
    ForceRedraw();  // 使用统一的强制重绘方法
    }
//=============================================================================
void CGPStatic::PreSubclassWindow( )
    {
    // 取消 WS_EX_TRANSPARENT，由控件自行绘制父背景，避免时序引发的残影
    ModifyStyleEx(WS_EX_TRANSPARENT, 0);

    // Set control to owner draw
    ModifyStyle( 0 , SS_OWNERDRAW | SS_NOTIFY, SWP_FRAMECHANGED );

    
    CDC *pDC = GetDC( );

    if ( NULL != pDC ) {
        int iFH = MulDiv( 12 , GetDeviceCaps( pDC->m_hDC , LOGPIXELSY ) , 72 );

        CRect rtClient;
        GetClientRect( rtClient );

        if ( iFH > rtClient.Height( ) ) {
            SetWindowPos( NULL , 0 , 0 , rtClient.Width( ) , iFH , SWP_NOMOVE );
            }

     
        ReleaseDC( pDC );  
        }

    CacheParentBackground();
    CStatic::PreSubclassWindow( );
    }

//=============================================================================
void CGPStatic::CacheParentBackground()
    {
    if (!GetSafeHwnd()) return;
    CRect rcClient; GetClientRect(rcClient);
    CSize newSize(rcClient.Width(), rcClient.Height());
    if(newSize.cx<=0||newSize.cy<=0) return;
    // 如果尺寸变化，重新创建位图
    if (!m_bmpBk.GetSafeHandle() || newSize != m_bkSize)
        {
        CDC* pWndDC = GetDC();
        if(!pWndDC) return;
        CDC memDC; memDC.CreateCompatibleDC(pWndDC);
        CBitmap newBmp; newBmp.CreateCompatibleBitmap(pWndDC, newSize.cx, newSize.cy);
        CBitmap* pOld = memDC.SelectObject(&newBmp);

        // 复制父背景
        CWnd* pParent = GetParent();
        if(pParent && ::IsWindow(pParent->GetSafeHwnd()))
            {
            CDC* pParentDC = pParent->GetDC();
            if(pParentDC)
                {
                CPoint pt(0,0); MapWindowPoints(pParent, &pt, 1);
                memDC.BitBlt(0,0,newSize.cx,newSize.cy,pParentDC,pt.x,pt.y,SRCCOPY);
                pParent->ReleaseDC(pParentDC);
                }
            }

        memDC.SelectObject(pOld);
        ReleaseDC(pWndDC);

        m_bmpBk.Detach();
        m_bmpBk.Attach(newBmp.Detach());
        m_bkSize = newSize;
        }
    }

//=============================================================================
void CGPStatic::OnSize(UINT nType, int cx, int cy)
    {
    CStatic::OnSize(nType, cx, cy);
    CacheParentBackground();
    Invalidate();
    }

//=============================================================================
void CGPStatic::UpdateText(LPCTSTR lpszString)
    {
    if (!lpszString) lpszString = _T("");
    CString currentText; GetWindowText(currentText);
    if (currentText == lpszString) {
        return;
    }
    CStatic::SetWindowText(lpszString);
    m_fontDirty = true;
    RedrawWindow(NULL, NULL, RDW_INVALIDATE | RDW_ERASE | RDW_UPDATENOW);
    }

void CGPStatic::SetWindowText(LPCTSTR lpszString)
    {
    // 简单调用我们的专用接口
    UpdateText(lpszString);
    }
//=============================================================================
BOOL CGPStatic::OnSetCursor( CWnd *pWnd , UINT nHitTest , UINT message )
    {
    if ( m_hCursor ) {
        ::SetCursor( m_hCursor );
        return TRUE;
        }

    return CStatic::OnSetCursor( pWnd , nHitTest , message );
    }
//=============================================================================
void CGPStatic::OnMouseMove( UINT nFlags , CPoint point )
    {
    if ( !m_bTracking ) {

        TRACKMOUSEEVENT tme;

        tme.cbSize       = sizeof( tme );
        tme.hwndTrack    = m_hWnd;
        tme.dwFlags      = TME_LEAVE | TME_HOVER;
        tme.dwHoverTime  = HOVER_DEFAULT;
        m_bTracking      = _TrackMouseEvent( &tme );
        }

    CStatic::OnMouseMove( nFlags , point );
    }
//=============================================================================
LRESULT CGPStatic::OnMouseHover( WPARAM wparam , LPARAM lparam )
    {
    UNREFERENCED_PARAMETER( wparam );
    UNREFERENCED_PARAMETER( lparam );
    m_bHovering = TRUE;
    Invalidate( );
    return 0;
    }

//=============================================================================
LRESULT CGPStatic::OnMouseLeave( WPARAM wparam , LPARAM lparam )
    {
    UNREFERENCED_PARAMETER( wparam );
    UNREFERENCED_PARAMETER( lparam );
    m_bTracking = FALSE;
    m_bHovering = FALSE;
    Invalidate( );
    return 0;
    }


//=============================================================================
LRESULT CGPStatic::OnLButtonUp( WPARAM wparam , LPARAM lparam )
    {
    UNREFERENCED_PARAMETER( wparam );
    UNREFERENCED_PARAMETER( lparam );
    //
    m_bSelect = !m_bSelect;
    Invalidate( );
    return 0;
    }

// 
bool CGPStatic::SetImage( UINT uResID , LPCTSTR lpszResType /*=nullptr*/ )
    {
    ClearImage();
    if (uResID == 0) return false;

    LPCTSTR lpType = lpszResType ? lpszResType : _T("PNG");

    // 
    if (lpType == RT_ICON || _tcsicmp(lpType, _T("ICON")) == 0)
        {
        //
        int cx = 256, cy = 256;
        if (GetSafeHwnd()) {
            CRect rc; GetClientRect(&rc);
            cx = rc.Width()  ? rc.Width()  : 256;
            cy = rc.Height() ? rc.Height() : 256;
        }
        //
        int iconEdge = min(cx, cy);
        cx = cy = iconEdge;
        HICON hIcon = (HICON)::LoadImage(AfxGetInstanceHandle(),
                                         MAKEINTRESOURCE(uResID),
                                         IMAGE_ICON,
                                         cx, cy,
                                         LR_DEFAULTCOLOR);      //
        if (!hIcon) return false;

        m_spImage.reset(Gdiplus::Bitmap::FromHICON(hIcon));
        ::DestroyIcon(hIcon);   //
        m_eImgType = ImgType::Bitmap;
        
        //
        if (m_spImage) {
            CString imageKey;
            imageKey.Format(L"GPStatic_Image_%p", m_spImage.get());
            CFastImageRenderer::Instance().PreprocessImage(m_spImage.get(), imageKey);
        }
        
        Invalidate();
        return m_spImage != nullptr;
        }

    //
    HRSRC hRes = ::FindResource(AfxGetInstanceHandle(),
                                MAKEINTRESOURCE(uResID), lpType);
    if (!hRes) return false;

    DWORD dwSize = ::SizeofResource(AfxGetInstanceHandle(), hRes);
    HGLOBAL hGlobal = ::LoadResource(AfxGetInstanceHandle(), hRes);
    if (!dwSize || !hGlobal) return false;

    void* pData = ::LockResource(hGlobal);
    if (!pData) return false;

    HGLOBAL hBuf = ::GlobalAlloc(GMEM_MOVEABLE, dwSize);
    if (!hBuf) return false;
    memcpy(::GlobalLock(hBuf), pData, dwSize);
    ::GlobalUnlock(hBuf);

    IStream* pStream = nullptr;
    if (SUCCEEDED(::CreateStreamOnHGlobal(hBuf, TRUE, &pStream)))
        {
        Gdiplus::Image* pImg = Gdiplus::Image::FromStream(pStream);
        if (pImg && pImg->GetLastStatus() == Gdiplus::Ok)
            {
            m_spImage.reset(pImg);
            m_eImgType = ImgType::Bitmap;
            
            //
            if (m_spImage) {
                CString imageKey;
                imageKey.Format(L"GPStatic_Image_%p", m_spImage.get());
                CFastImageRenderer::Instance().PreprocessImage(m_spImage.get(), imageKey);
            }
            }
        else
            delete pImg;
        pStream->Release();
        }

    Invalidate();
    return m_spImage != nullptr;
    }

bool CGPStatic::SetImage( LPCTSTR lpszFilePath )
    {
    ClearImage();
    if (!lpszFilePath || !*lpszFilePath) return false;

    CString ext = PathFindExtension(lpszFilePath);
    ext.MakeLower();

    // ICO
    if (ext == _T(".ico"))
        {
        int cx = 256, cy = 256;
        if (GetSafeHwnd()) {
            CRect rc; GetClientRect(&rc);
            cx = rc.Width()  ? rc.Width()  : 256;
            cy = rc.Height() ? rc.Height() : 256;
        }
        int iconEdge = min(cx, cy);
        cx = cy = iconEdge;
        HICON hIcon = nullptr;
        if (::PrivateExtractIcons(lpszFilePath,
                                  0,
                                  cx, cy,
                                  &hIcon, nullptr, 1,
                                  LR_LOADFROMFILE | LR_DEFAULTCOLOR) != 1 || !hIcon)
        {
            hIcon = (HICON)::LoadImage(nullptr, lpszFilePath,
                                       IMAGE_ICON, 0, 0,
                                       LR_LOADFROMFILE | LR_DEFAULTSIZE);
        }
        if (!hIcon) return false;

        m_spImage.reset(Gdiplus::Bitmap::FromHICON(hIcon));
        m_eImgType = ImgType::Bitmap;
        DestroyIcon(hIcon);
        
        //
        if (m_spImage) {
            CString imageKey;
            imageKey.Format(L"GPStatic_Image_%p", m_spImage.get());
            CFastImageRenderer::Instance().PreprocessImage(m_spImage.get(), imageKey);
        }
        }
    else
        {
        Gdiplus::Image* pImg = Gdiplus::Image::FromFile(lpszFilePath);
        if (pImg && pImg->GetLastStatus() == Gdiplus::Ok)
            {
            m_spImage.reset(pImg);
            m_eImgType = ImgType::Bitmap;
            
            // 
            if (m_spImage) {
                CString imageKey;
                imageKey.Format(L"GPStatic_Image_%p", m_spImage.get());
                CFastImageRenderer::Instance().PreprocessImage(m_spImage.get(), imageKey);
            }
            }
        else
            delete pImg;
        }

    Invalidate();
    return m_spImage != nullptr;
    }

void CGPStatic::ClearImage()
    {
    if (m_spImage)
        {
        m_spImage.reset();
        m_eImgType = ImgType::None;
        Invalidate();
        }
    }
// ---------------- 辅助函数 -----------------
void CGPStatic::UpdatePix( float fPix )
    {
    m_fCurrentPix = fPix;
    }

void CGPStatic::RebuildFont( CDC* pDC )
    {
    if (nullptr == pDC)
        return;

    LOGFONT lfFont;
    CFunctions::GetDefaultFont( lfFont );

    // 统一处理样式
    lfFont.lfUnderline = (BYTE)m_bUnderline;
    lfFont.lfWeight    = m_bBold ? FW_BOLD : FW_NORMAL;

    // 统一处理字号与 DPI，并考虑控件级字体缩放
    if (m_iFontSize > 0) {
        // m_iFontSize 视为点数：按系统字体逻辑直接使用点数对应像素，不额外乘 DPI。
        LONG px96 = -MulDiv(m_iFontSize, 96, 72);
        lfFont.lfHeight = static_cast<LONG>(px96 * m_fControlFontScale);
    } else {
        // 与 CGPCheckBox/系统一致：不再额外按 DPI 放大
        lfFont.lfHeight = static_cast<LONG>( lfFont.lfHeight * m_fControlFontScale );
    }

#ifdef _DEBUG
#undef new
#endif
    m_spFont.reset( new Gdiplus::Font( pDC->GetSafeHdc( ) , &lfFont ) );
#ifdef _DEBUG
#define new DEBUG_NEW
#endif
    m_fontDirty = false;
    }

// DPI 变化
LRESULT CGPStatic::OnDpiChanged( WPARAM wParam , LPARAM lParam )
    {
    const UINT dpiX = LOWORD( wParam );
    float fPix = dpiX / 96.0f;
    UpdatePix( fPix );

    // 更新全局 g_fPix，保持一致
    g_fPix = fPix;

    CDC* pDC = GetDC( );
    RebuildFont( pDC );
    ReleaseDC( pDC );

    Invalidate( );
    return 0;
    }