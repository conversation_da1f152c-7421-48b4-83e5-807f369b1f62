#include "pch.h"
#include "LazyImageLoader.h"
#include "GPImageInfo.h"
#include "FastImageRenderer.h"
#include <algorithm>

// 消息定义
#define WM_IMAGE_LOADED (WM_USER + 1001)

// 全局实例
CLazyImageLoader* CLazyImageLoader::s_pInstance = nullptr;
std::mutex CLazyImageLoader::s_instanceMutex;

CLazyImageLoader& CLazyImageLoader::Instance()
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    if (!s_pInstance) {
        s_pInstance = new CLazyImageLoader();
    }
    return *s_pInstance;
}

void CLazyImageLoader::Destroy()
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    if (s_pInstance) {
        delete s_pInstance;
        s_pInstance = nullptr;
    }
}

CLazyImageLoader::CLazyImageLoader()
    : m_running(false)
    , m_mainThreadId(::GetCurrentThreadId())
    , m_hNotifyWnd(nullptr)
{
}

CLazyImageLoader::~CLazyImageLoader()
{
    Stop();
}

void CLazyImageLoader::Initialize(HWND hNotifyWnd)
{
    m_hNotifyWnd = hNotifyWnd;
    Start();
}

void CLazyImageLoader::LoadImageAsync(const CString& imagePath, ImageLoadCallback callback, LoadPriority priority)
{
    // 检查是否已经在缓存中
    {
        std::lock_guard<std::mutex> lock(m_cacheMutex);
        auto it = m_imageCache.find(imagePath);
        if (it != m_imageCache.end() && it->second != nullptr) {
            // 图片已经加载，直接调用回调
            if (callback) {
                if (::GetCurrentThreadId() == m_mainThreadId) {
                    callback(it->second, imagePath);
                } else {
                    // 需要在主线程中执行回调
                    PostCallback(it->second, imagePath, callback);
                }
            }
            return;
        }
    }

    // 添加到加载队列
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_loadQueue.push({imagePath, callback, priority, ::GetTickCount()});
    }
    m_condition.notify_one();
}

void CLazyImageLoader::PreloadImagesWhenIdle(const std::vector<CString>& imagePaths)
{
    // 使用低优先级线程进行预加载
    std::thread([this, imagePaths]() {
        ::SetThreadPriority(::GetCurrentThread(), THREAD_PRIORITY_BELOW_NORMAL);
        
        for (const auto& path : imagePaths) {
            // 检查是否已经加载
            bool needLoad = false;
            {
                std::lock_guard<std::mutex> lock(m_cacheMutex);
                needLoad = (m_imageCache.find(path) == m_imageCache.end());
            }
            
            if (needLoad) {
                // 使用低优先级加载
                LoadImageAsync(path, nullptr, LoadPriority::Low);
                ::Sleep(100); // 避免占用过多资源
            }
        }
    }).detach();
}

bool CLazyImageLoader::IsImageLoaded(const CString& imagePath) const
{
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    auto it = m_imageCache.find(imagePath);
    return it != m_imageCache.end() && it->second != nullptr;
}

Gdiplus::Image* CLazyImageLoader::GetCachedImage(const CString& imagePath) const
{
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    auto it = m_imageCache.find(imagePath);
    return (it != m_imageCache.end()) ? it->second : nullptr;
}

void CLazyImageLoader::ClearCache()
{
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_imageCache.clear();
}

void CLazyImageLoader::Start()
{
    if (!m_running) {
        m_running = true;
        m_workerThread = std::thread(&CLazyImageLoader::WorkerThread, this);
    }
}

void CLazyImageLoader::Stop()
{
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_running = false;
    }
    m_condition.notify_all();
    
    if (m_workerThread.joinable()) {
        m_workerThread.join();
    }
}

void CLazyImageLoader::WorkerThread()
{
    ::CoInitialize(nullptr); // 初始化COM，某些图片格式可能需要
    
    while (m_running) {
        LoadTask task;
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_condition.wait(lock, [this] { return !m_loadQueue.empty() || !m_running; });
            
            if (!m_running) break;
            if (m_loadQueue.empty()) continue;
            
            // 获取最高优先级的任务
            task = GetHighestPriorityTask();
        }
        
        // 执行加载
        Gdiplus::Image* pImage = nullptr;
        
        // 首先尝试从GPImageInfo加载（可能已经被其他地方加载）
        pImage = CGPImageInfo::Instance()->ImageFromFile(task.imagePath);
        
        if (pImage != nullptr) {
            // 缓存图片
            {
                std::lock_guard<std::mutex> lock(m_cacheMutex);
                m_imageCache[task.imagePath] = pImage;
            }
            
            // 统计信息
            DWORD loadTime = ::GetTickCount() - task.timestamp;
            TRACE(L"LazyImageLoader: Loaded %s in %d ms\n", task.imagePath.GetString(), loadTime);
            
            // 执行回调
            if (task.callback) {
                PostCallback(pImage, task.imagePath, task.callback);
            }
        }
    }
    
    ::CoUninitialize();
}

CLazyImageLoader::LoadTask CLazyImageLoader::GetHighestPriorityTask()
{
    // 简单实现：按优先级排序
    std::priority_queue<LoadTask> tempQueue;
    
    while (!m_loadQueue.empty()) {
        tempQueue.push(m_loadQueue.front());
        m_loadQueue.pop();
    }
    
    LoadTask task = tempQueue.top();
    tempQueue.pop();
    
    // 将剩余任务放回队列
    while (!tempQueue.empty()) {
        m_loadQueue.push(tempQueue.top());
        tempQueue.pop();
    }
    
    return task;
}

void CLazyImageLoader::PostCallback(Gdiplus::Image* pImage, const CString& imagePath, ImageLoadCallback callback)
{
    if (m_hNotifyWnd && ::IsWindow(m_hNotifyWnd)) {
        // 创建回调数据
        CallbackData* pData = new CallbackData{pImage, imagePath, callback};
        ::PostMessage(m_hNotifyWnd, WM_IMAGE_LOADED, (WPARAM)pData, 0);
    }
}

// 静态消息处理函数，需要在主窗口的消息映射中调用
LRESULT CLazyImageLoader::OnImageLoaded(WPARAM wParam, LPARAM lParam)
{
    CallbackData* pData = reinterpret_cast<CallbackData*>(wParam);
    if (pData) {
        if (pData->callback) {
            pData->callback(pData->pImage, pData->imagePath);
        }
        delete pData;
    }
    return 0;
}
