﻿#pragma once

// 在 framework.h 文件最开头添加（在所有 #include 之前）
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601  // Windows 7
#endif

#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容


#include <afxwin.h>
#include <afxdialogex.h>  // 支持 CDialogEx 类
#include <gdiplus.h>
#include <afxext.h>
#include <afxcmn.h>
#pragma comment(lib, "gdiplus.lib")

#include "GDIPlusSkinH.h"

#include <vector>
#include <string>
#include <map>

#ifdef _M_X64
#define safe_closehandle(h) do {if ((h != INVALID_HANDLE_VALUE) && (h != NULL) && 0xdddddddd != (unsigned long long)h) {CloseHandle(h); h = INVALID_HANDLE_VALUE;}} while(0)
#else
#define safe_closehandle(h) do {if ((h != INVALID_HANDLE_VALUE) && (h != NULL) && 0xdddddddd != (unsigned int)h) {CloseHandle(h); h = INVALID_HANDLE_VALUE;}} while(0)
#endif

#define safe_free(p) do {if(NULL != p) {free((void*)p); p = NULL;}} while(0)

#define safe_release_dc(hWnd, hDC) do {if ((hDC != INVALID_HANDLE_VALUE) && (hDC != NULL)) {::ReleaseDC(hWnd, hDC); hDC = NULL;}} while(0)

#define safe_atoi(str) ((((char*)(str))==NULL)?0:atoi(str))
#define safe_strlen(str) ((((char*)(str))==NULL)?0:strlen(str))

#define safe_wtoi(str) ((((wchar_t*)(str))==NULL)?0:_wtoi(str))
#define safe_wcslen(str) ((((wchar_t*)(str))==NULL)?0:wcslen(str))
#define safe_wcsicmp(str1, str2) _wcsicmp((((wchar_t*)(str1)==NULL)?L"<NULL>":str1), (((wchar_t*)(str2)==NULL)?L"<NULL>":str2))
#define safe_wcscmp(str1, str2) wcscmp((((wchar_t*)(str1)==NULL)?L"<NULL>":str1), (((wchar_t*)(str2)==NULL)?L"<NULL>":str2))
