﻿#pragma once
#include "afxwin.h"

// 线程完成消息
#define UM_THREAD_END (WM_APP + 199)

// CDlgRemoveKey 对话框
class CDlgRemoveKey : public CGPDlgSkinBase
{
    DECLARE_DYNAMIC(CDlgRemoveKey)

public:
    CDlgRemoveKey(CWnd* pParent = nullptr);   // 标准构造函数
    virtual ~CDlgRemoveKey();

    enum { IDD = IDD_DIALOG_REMOVE_KEY };

protected:
    virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
    virtual BOOL OnInitDialog();

    // 按钮及线程回调
    afx_msg void OnBnClickedButtonRemove();
    afx_msg LRESULT OnThreadEnd(WPARAM wParam, LPARAM lParam);

    DECLARE_MESSAGE_MAP()

    // 工作线程相关
    static UINT __cdecl WorkThread(LPVOID lParam);
    UINT Work(UINT& step);

private:
    // 授权信息
    CString   m_strContact;          // 授权给
    CString   m_strExpire;           // 到期时间(字符串)
    CString   m_strCnt;              // 数量
    CString   m_strSerial;           // 原始序列号
    CString   m_strSerialMasked;     // 脱敏后序列号
    CString   m_strHWID;             // HWID
    CString   m_strErr;              // 服务器错误信息
    int       m_iHttpCode;           // HTTP 状态码

    // 界面控件
    CGPStatic m_gsContact;           // IDC_STATIC_LIC_TO
    CGPStatic m_gsExpire;            // IDC_STATIC_LIC_EXP
    CGPStatic m_gsCnt;               // IDC_STATIC_LIC_CNT
    CGPStatic m_gsSerial;            // IDC_STATIC_SERIAL
    CGPStatic m_gsHWID;              // IDC_EDIT_HID (只读显示)
};
